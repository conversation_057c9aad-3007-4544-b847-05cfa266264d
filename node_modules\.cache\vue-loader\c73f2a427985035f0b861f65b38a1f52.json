{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyAuditRecord\\index.vue?vue&type=template&id=21e7e914&scoped=true&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyAuditRecord\\index.vue", "mtime": 1750323702933}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745219693993}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uKCkgewogIHZhciBfdm0gPSB0aGlzCiAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgdmFyIF9jID0gX3ZtLl9zZWxmLl9jIHx8IF9oCiAgcmV0dXJuIF9jKAogICAgImRpdiIsCiAgICB7IHN0YXRpY0NsYXNzOiAicm91dGVyLXdyYXAtdGFibGUiIH0sCiAgICBbCiAgICAgIF9jKCJoZWFkZXIiLCB7IHN0YXRpY0NsYXNzOiAidGFibGUtaGVhZGVyIiB9LCBbCiAgICAgICAgX2MoInNlY3Rpb24iLCB7IHN0YXRpY0NsYXNzOiAidGFibGUtaGVhZGVyLW1haW4iIH0sIFsKICAgICAgICAgIF9jKCJzZWN0aW9uIiwgeyBzdGF0aWNDbGFzczogInRhYmxlLWhlYWRlci1zZWFyY2giIH0sIFsKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgInNlY3Rpb24iLAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICJzaG93IiwKICAgICAgICAgICAgICAgICAgICByYXdOYW1lOiAidi1zaG93IiwKICAgICAgICAgICAgICAgICAgICB2YWx1ZTogIV92bS5pc1Nob3csCiAgICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogIiFpc1Nob3ciCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogInRhYmxlLWhlYWRlci1zZWFyY2gtaW5wdXQiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImVsLXNlbGVjdCIsCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBjbGVhcmFibGU6ICIiLCBwbGFjZWhvbGRlcjogIuetlueVpeexu+WeiyIgfSwKICAgICAgICAgICAgICAgICAgICBvbjogeyBjaGFuZ2U6IF92bS5oYW5kbGVTZWFyY2ggfSwKICAgICAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5zZWFyY2hGb3JtLnR5cGUsCiAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24oJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KF92bS5zZWFyY2hGb3JtLCAidHlwZSIsICQkdikKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAic2VhcmNoRm9ybS50eXBlIgogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7IGF0dHJzOiB7IGxhYmVsOiAi5YWo6YOoIiwgdmFsdWU6ICIiIH0gfSksCiAgICAgICAgICAgICAgICAgICAgX2MoImVsLW9wdGlvbiIsIHsgYXR0cnM6IHsgbGFiZWw6ICLph4fpm4YiLCB2YWx1ZTogMCB9IH0pLAogICAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7IGF0dHJzOiB7IGxhYmVsOiAi6L+H5rukIiwgdmFsdWU6IDEgfSB9KQogICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICksCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJzZWN0aW9uIiwKICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAidGFibGUtaGVhZGVyLXNlYXJjaC1idXR0b24iIH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgIV92bS5pc1Nob3cKICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgb246IHsgY2xpY2s6IF92bS5oYW5kbGVTZWFyY2ggfQogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuafpeivoiIpXQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgIF9jKCJlbC1idXR0b24iLCB7IG9uOiB7IGNsaWNrOiBfdm0udG9nZ2xlU2hvdyB9IH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KCIg6auY57qn5pCc57SiICIpLAogICAgICAgICAgICAgICAgICBfYygiaSIsIHsKICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImVsLWljb24tLXJpZ2h0IiwKICAgICAgICAgICAgICAgICAgICBjbGFzczogX3ZtLmlzU2hvdwogICAgICAgICAgICAgICAgICAgICAgPyAiZWwtaWNvbi1hcnJvdy11cCIKICAgICAgICAgICAgICAgICAgICAgIDogImVsLWljb24tYXJyb3ctZG93biIKICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICkKICAgICAgICAgIF0pLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJzZWN0aW9uIiwKICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInRhYmxlLWhlYWRlci1idXR0b24iIH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAiZGFuZ2VyIiB9LAogICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmJhdGNoRGVsZXRlUHJvdG9jb2wgfQogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIFtfdm0uX3YoIuaJuemHj+WIoOmZpCIpXQogICAgICAgICAgICAgICkKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIF0pLAogICAgICAgIF9jKAogICAgICAgICAgInNlY3Rpb24iLAogICAgICAgICAgeyBzdGF0aWNDbGFzczogInRhYmxlLWhlYWRlci1leHRlbmQiIH0sCiAgICAgICAgICBbCiAgICAgICAgICAgIF9jKCJlbC1jb2xsYXBzZS10cmFuc2l0aW9uIiwgWwogICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAic2hvdyIsCiAgICAgICAgICAgICAgICAgICAgICByYXdOYW1lOiAidi1zaG93IiwKICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uaXNTaG93LAogICAgICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImlzU2hvdyIKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgZ3V0dGVyOiAyMCB9IH0sCiAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICJlbC1jb2wiLAogICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtc2VsZWN0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgY2xlYXJhYmxlOiAiIiwgcGxhY2Vob2xkZXI6ICLnrZbnlaXnsbvlnosiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7IGNoYW5nZTogX3ZtLmhhbmRsZVNlYXJjaCB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uc2VhcmNoRm9ybS50eXBlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbigkJHYpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KF92bS5zZWFyY2hGb3JtLCAidHlwZSIsICQkdikKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJzZWFyY2hGb3JtLnR5cGUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6ICLlhajpg6giLCB2YWx1ZTogIiIgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLW9wdGlvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBsYWJlbDogIumHh+mbhiIsIHZhbHVlOiAwIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6ICLov4fmu6QiLCB2YWx1ZTogMSB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGd1dHRlcjogMjAgfSB9LAogICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAiZWwtY29sIiwKICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyBzcGFuOiAyNCwgYWxpZ246ICJyaWdodCIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdHlwZTogInByaW1hcnkiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlU2VhcmNoIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLmn6Xor6IiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC1idXR0b24iLCB7IG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlUmVzZXQgfSB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIumHjee9riIpCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLWJ1dHRvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGljb246ICJzb2MtaWNvbi1zY3JvbGxlci10b3AtYWxsIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgb246IHsgY2xpY2s6IF92bS50b2dnbGVTaG93IH0KICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgKQogICAgICAgICAgICBdKQogICAgICAgICAgXSwKICAgICAgICAgIDEKICAgICAgICApCiAgICAgIF0pLAogICAgICBfYygibWFpbiIsIHsgc3RhdGljQ2xhc3M6ICJ0YWJsZS1ib2R5IiB9LCBbCiAgICAgICAgX3ZtLl9tKDApLAogICAgICAgIF9jKAogICAgICAgICAgInNlY3Rpb24iLAogICAgICAgICAgewogICAgICAgICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgbmFtZTogImxvYWRpbmciLAogICAgICAgICAgICAgICAgcmF3TmFtZTogInYtbG9hZGluZyIsCiAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLmxvYWRpbmcsCiAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAibG9hZGluZyIKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAidGFibGUtYm9keS1tYWluIgogICAgICAgICAgfSwKICAgICAgICAgIFsKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgImVsLXRhYmxlIiwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICBkYXRhOiBfdm0udGFibGVMaXN0LnJvd3MgfHwgW10sCiAgICAgICAgICAgICAgICAgICJlbGVtZW50LWxvYWRpbmctYmFja2dyb3VuZCI6ICJyZ2JhKDAsIDAsIDAsIDAuMykiLAogICAgICAgICAgICAgICAgICBzaXplOiAibWluaSIsCiAgICAgICAgICAgICAgICAgICJoaWdobGlnaHQtY3VycmVudC1yb3ciOiAiIiwKICAgICAgICAgICAgICAgICAgInRvb2x0aXAtZWZmZWN0IjogImxpZ2h0IiwKICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAiMTAwJSIKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBvbjogeyAic2VsZWN0aW9uLWNoYW5nZSI6IF92bS5vblNlbGVjdGlvbkNoYW5nZSB9CiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAic2VsZWN0aW9uIiwgd2lkdGg6ICI1NSIsIGFsaWduOiAiY2VudGVyIiB9CiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5bqP5Y+3Iiwgd2lkdGg6ICI4MCIsIGFsaWduOiAiY2VudGVyIiB9LAogICAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KFsKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgIGZuOiBmdW5jdGlvbihzY29wZSkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgICAgICIgIiArCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoX3ZtLnBhZ2luYXRpb24ucGFnZUluZGV4IC0gMSkgKgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLnBhZ2luYXRpb24ucGFnZVNpemUgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUuJGluZGV4ICsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSArCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICIgIgogICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICBwcm9wOiAibmFtZSIsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLljY/orq4iLAogICAgICAgICAgICAgICAgICAgICJzaG93LW92ZXJmbG93LXRvb2x0aXAiOiAiIgogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJhZGRUaW1lIiwgbGFiZWw6ICLml7bpl7QiIH0sCiAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoWwogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgIiAiICsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5hZGRUaW1lID09PSAiLSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gc2NvcGUucm93LmFkZFRpbWUKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLmZvcm1hdFRpbWUoc2NvcGUucm93LmFkZFRpbWUpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiICIKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJzdGF0dXMiLCBsYWJlbDogIueKtuaAgSIgfSwKICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdShbCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24oc2NvcGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnN0YXR1cyA9PSAiMCIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gInN0YXR1cy1mYWlsZWQiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICJzdGF0dXMtc3VjY2VzcyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiICIgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cuc3RhdHVzID09ICIwIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gIuS4i+WPkeWksei0pSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICLkuIvlj5HmiJDlip8iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApICsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICIgIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAib3BlcmF0ZVR5cGUiLCBsYWJlbDogIuaTjeS9nOexu+WeiyIgfSwKICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdShbCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24oc2NvcGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAiICIgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93Lm9wZXJhdGVUeXBlID09IDAgPyAi6YeH6ZuGIiA6ICLov4fmu6QiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiICIKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJjb3VudHMiLCBsYWJlbDogIuaTjeS9nOaVsOmHjyIgfQogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgIHByb3A6ICJkZXNjcmlwdGlvbiIsCiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLmj4/ov7AiLAogICAgICAgICAgICAgICAgICAgICJzaG93LW92ZXJmbG93LXRvb2x0aXAiOiAiIgogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5pON5L2cIiwgd2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoWwogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJhY3Rpb24tYnV0dG9ucyIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbC1idXR0b24tLWJsdWUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdHlwZTogInRleHQiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24oJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5kZWxldGVQcm90b2NvbChzY29wZS5yb3cpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuWIoOmZpCIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICkKICAgICAgICAgIF0sCiAgICAgICAgICAxCiAgICAgICAgKQogICAgICBdKSwKICAgICAgX2MoCiAgICAgICAgImZvb3RlciIsCiAgICAgICAgeyBzdGF0aWNDbGFzczogInRhYmxlLWZvb3RlciIgfSwKICAgICAgICBbCiAgICAgICAgICBfdm0udGFibGVMaXN0LnRvdGFsID4gMAogICAgICAgICAgICA/IF9jKCJlbC1wYWdpbmF0aW9uIiwgewogICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgc21hbGw6ICIiLAogICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAiIiwKICAgICAgICAgICAgICAgICAgYWxpZ246ICJyaWdodCIsCiAgICAgICAgICAgICAgICAgICJjdXJyZW50LXBhZ2UiOiBfdm0ucGFnaW5hdGlvbi5wYWdlSW5kZXgsCiAgICAgICAgICAgICAgICAgICJwYWdlLXNpemVzIjogWzEwLCAyMCwgNTAsIDEwMF0sCiAgICAgICAgICAgICAgICAgICJwYWdlLXNpemUiOiBfdm0ucGFnaW5hdGlvbi5wYWdlU2l6ZSwKICAgICAgICAgICAgICAgICAgbGF5b3V0OiAidG90YWwsIHNpemVzLCBwcmV2LCBwYWdlciwgbmV4dCwganVtcGVyIiwKICAgICAgICAgICAgICAgICAgdG90YWw6IF92bS50YWJsZUxpc3QudG90YWwgfHwgMAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICJzaXplLWNoYW5nZSI6IF92bS5vblNob3dTaXplQ2hhbmdlLAogICAgICAgICAgICAgICAgICAiY3VycmVudC1jaGFuZ2UiOiBfdm0uaGFuZGxlUGFnZUNoYW5nZQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIDogX3ZtLl9lKCkKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgICAgX2MoInZpZXctY29tcG9uZW50IiwgeyByZWY6ICJ2aWV3UmVmIiB9KQogICAgXSwKICAgIDEKICApCn0KdmFyIHN0YXRpY1JlbmRlckZucyA9IFsKICBmdW5jdGlvbigpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJzZWN0aW9uIiwgeyBzdGF0aWNDbGFzczogInRhYmxlLWJvZHktaGVhZGVyIiB9LCBbCiAgICAgIF9jKCJoMiIsIHsgc3RhdGljQ2xhc3M6ICJ0YWJsZS1ib2R5LXRpdGxlIiB9LCBbX3ZtLl92KCLnrZbnlaXlrqHorqHorrDlvZUiKV0pCiAgICBdKQogIH0KXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}