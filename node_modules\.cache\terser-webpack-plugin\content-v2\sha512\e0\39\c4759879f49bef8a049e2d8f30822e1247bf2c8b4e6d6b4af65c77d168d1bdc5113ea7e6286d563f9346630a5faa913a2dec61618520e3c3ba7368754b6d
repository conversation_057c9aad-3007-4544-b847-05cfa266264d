{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-97520b06\"],{\"07c0\":function(e,t,a){},\"0df3\":function(e,t,a){},\"2ca0\":function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"06cf\").f,i=a(\"50c4\"),o=a(\"5a34\"),s=a(\"1d80\"),c=a(\"ab13\"),l=a(\"c430\"),u=\"\".startsWith,d=Math.min,f=c(\"startsWith\"),p=!l&&!f&&!!function(){var e=n(String.prototype,\"startsWith\");return e&&!e.writable}();r({target:\"String\",proto:!0,forced:!p&&!f},{startsWith:function(e){var t=String(s(this));o(e);var a=i(d(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return u?u.call(t,r,a):t.slice(a,a+r.length)===r}})},\"2d1d\":function(e,t,a){\"use strict\";var r=a(\"07c0\"),n=a.n(r);n.a},\"3a86\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return i}));var r=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.offsetTop),t},n=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.clientHeight),t},i=function(e){try{var t=n(\".audit-container\")||n(\".router-container\"),a=r(\".el-table\"),i=48,o=t-a-i-20;return e>0?Math.max(o,300):300}catch(s){return console.error(\"计算表格高度出错:\",s),500}}},\"5a34\":function(e,t,a){var r=a(\"44e7\");e.exports=function(e){if(r(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"7a6f\":function(e,t,a){\"use strict\";var r=a(\"ccf2\"),n=a.n(r);n.a},\"84f0\":function(e,t,a){\"use strict\";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"user-container\"},[a(\"div\",{staticClass:\"user-content\"},[a(\"el-tabs\",{attrs:{type:\"card\"},on:{\"tab-click\":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:\"activeTab\"}},[a(\"el-tab-pane\",{attrs:{label:\"账号管理\",name:\"account\"}},[\"account\"===e.activeTab?a(\"account-management\"):e._e()],1),a(\"el-tab-pane\",{attrs:{label:\"角色管理\",name:\"role\"}},[\"role\"===e.activeTab?a(\"role-management\"):e._e()],1)],1)],1)])},n=[],i=(a(\"b0c0\"),a(\"f3f3\")),o=(a(\"96cf\"),a(\"c964\")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"account-container\"},[a(\"div\",{staticClass:\"account-content self-page self-position\"},[a(\"div\",{staticClass:\"account-title\"},[e._v(\"用户列表\")]),a(\"div\",{staticClass:\"account-actions\"},[a(\"el-button\",{staticClass:\"action-button\",attrs:{type:\"primary\"},on:{click:function(t){return e.handleEdit()}}},[e._v(\" 新增 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.handleDel(e.selectedRowKeys)}}},[e._v(\" 删除 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length||e.invocationButton},on:{click:function(t){return e.updateStatus(e.selectedRowKeys,0)}}},[e._v(\" 启用 \")]),a(\"el-button\",{attrs:{disabled:!e.selectedRowKeys.length||e.disabledButton},on:{click:function(t){return e.updateStatus(e.selectedRowKeys,1)}}},[e._v(\" 禁用 \")])],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{data:e.list,height:e.tableHeight},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\"}}),a(\"el-table-column\",{attrs:{prop:\"user_name\",label:\"账号\"}}),a(\"el-table-column\",{attrs:{prop:\"role_id\",label:\"角色\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-tag\",[a(\"i\",{staticClass:\"el-icon-user\"}),e._v(\" \"+e._s(e.listKeyValue.filter((function(e){return e.key===t.row.role_id}))[0]?e.listKeyValue.filter((function(e){return e.key===t.row.role_id}))[0].value:\"\")+\" \")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"is_disable\",label:\"状态\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[0===t.row.is_disable?a(\"span\",{staticStyle:{color:\"rgb(55, 150, 255)\"}},[e._v(\"已启用\")]):a(\"span\",{staticStyle:{color:\"#fa8c16\"}},[e._v(\"已禁用\")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"remark\",label:\"备注\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"操作\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"admin\"!==t.row.user_name||\"admin\"===e.$store.state.hostguardianUser.userData.user_name?a(\"el-button\",{staticClass:\"operation-button\",staticStyle:{color:\"#3796ff\"},attrs:{type:\"text\"},on:{click:function(a){return e.handleEdit(t.$index)}}},[e._v(\" 编辑 \")]):e._e(),t.row.user_name!==e.$store.state.hostguardianUser.userData.user_name&&\"admin\"!==t.row.user_name?a(\"el-button\",{staticClass:\"operation-button\",staticStyle:{color:\"#3796ff\"},attrs:{type:\"text\"},on:{click:function(a){return e.updateStatus(t.row.id,0===t.row.is_disable?1:0)}}},[e._v(\" \"+e._s(0===t.row.is_disable?\"禁用\":\"启用\")+\" \")]):e._e(),t.row.user_name!==e.$store.state.hostguardianUser.userData.user_name&&\"admin\"!==t.row.user_name?a(\"el-button\",{staticClass:\"operation-button\",staticStyle:{color:\"#3796ff\"},attrs:{type:\"text\"},on:{click:function(a){return e.handleDel(t.row.id)}}},[e._v(\" 删除 \")]):e._e()]}}])})],1),a(\"pagination\",e._b({staticClass:\"self-pagination\",on:{change:e.handlePaginationChange,showSizeChange:e.handleShowSizeChange}},\"pagination\",e.pagination,!1))],1),a(\"Edit\",{attrs:{visible:e.edit.visible,\"confirm-loading\":e.edit.confirmLoading,\"form-origin\":e.formOrigin,list:e.listKeyValue},on:{\"update:visible\":function(t){return e.$set(e.edit,\"visible\",t)},\"update:confirmLoading\":function(t){return e.$set(e.edit,\"confirmLoading\",t)},\"update:confirm-loading\":function(t){return e.$set(e.edit,\"confirmLoading\",t)},\"validate:ok\":e.handleSave}})],1)},c=[],l=(a(\"a623\"),a(\"4160\"),a(\"d81d\"),a(\"b64b\"),a(\"159b\"),a(\"6544\")),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-dialog\",{staticClass:\"edit-dialog\",attrs:{title:e.form.id?\"编辑\":\"新增\",visible:e.sVisible,width:\"600px\",\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.sVisible=t},closed:e.handleClose}},[a(\"el-form\",{ref:\"form\",staticClass:\"edit-form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"80px\"}},[a(\"el-form-item\",{attrs:{label:\"账号\",prop:\"user_name\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入账号\",disabled:!!e.form.id},model:{value:e.form.user_name,callback:function(t){e.$set(e.form,\"user_name\",\"string\"===typeof t?t.trim():t)},expression:\"form.user_name\"}})],1),a(\"el-form-item\",{attrs:{label:\"初始密码\",prop:\"init_password\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入初始密码\",type:e.typeInitial},model:{value:e.form.init_password,callback:function(t){e.$set(e.form,\"init_password\",\"string\"===typeof t?t.trim():t)},expression:\"form.init_password\"}},[a(\"i\",{class:\"text\"===e.typeInitial?\"el-icon-view\":\"el-icon-hide\",attrs:{slot:\"suffix\"},on:{click:function(t){e.typeInitial=\"text\"===e.typeInitial?\"password\":\"text\"}},slot:\"suffix\"})])],1),a(\"el-form-item\",{attrs:{label:\"确认密码\",prop:\"confirm_password\"}},[a(\"el-input\",{attrs:{placeholder:\"请确认初始密码\",type:e.typeConfirm},model:{value:e.form.confirm_password,callback:function(t){e.$set(e.form,\"confirm_password\",\"string\"===typeof t?t.trim():t)},expression:\"form.confirm_password\"}},[a(\"i\",{class:\"text\"===e.typeConfirm?\"el-icon-view\":\"el-icon-hide\",attrs:{slot:\"suffix\"},on:{click:function(t){e.typeConfirm=\"text\"===e.typeConfirm?\"password\":\"text\"}},slot:\"suffix\"})])],1),a(\"el-form-item\",{attrs:{label:\"角色\",prop:\"role_id\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择角色\"},model:{value:e.form.role_id,callback:function(t){e.$set(e.form,\"role_id\",t)},expression:\"form.role_id\"}},e._l(e.list,(function(e){return a(\"el-option\",{key:e.key,attrs:{label:e.value,value:e.key}})})),1)],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\",rows:4},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.sVisible=!1}}},[e._v(\"取消\")]),a(\"el-button\",{attrs:{type:\"primary\",loading:e.sConfirmLoading},on:{click:e.handleOk}},[e._v(\"确定\")])],1)],1)},d=[],f={name:\"Edit\",props:{visible:{type:Boolean,default:!1},confirmLoading:{type:Boolean,default:!1},formOrigin:{type:Object,default:function(){return{}}},list:{type:Array,default:function(){return[]}}},data:function(){return{sVisible:this.visible,sConfirmLoading:this.confirmLoading,typeConfirm:\"password\",typeInitial:\"password\",form:{id:void 0,user_name:void 0,init_password:void 0,confirm_password:void 0,remark:void 0,role_id:void 0},rules:{user_name:[{required:!0,trigger:\"change\",message:\"请输入账号\"}],init_password:[{required:!0,trigger:\"change\",message:\"请输入密码\"},{min:6,max:20,trigger:\"blur\",message:\"密码长度为6-20个字符\"}],confirm_password:[{required:!0,trigger:\"change\",message:\"请输入确认密码\"},{validator:this.validateConfirmPassword,trigger:\"blur\"}],role_id:[{required:!0,trigger:\"change\",message:\"请选择角色\"}]}}},watch:{visible:function(e){this.sVisible=e,e&&this.initForm()},confirmLoading:function(e){this.sConfirmLoading=e},sVisible:function(e){this.$emit(\"update:visible\",e)},sConfirmLoading:function(e){this.$emit(\"update:confirmLoading\",e)}},methods:{initForm:function(){var e=this;this.form={id:void 0,user_name:void 0,init_password:void 0,confirm_password:void 0,remark:void 0,role_id:void 0},this.formOrigin&&Object.keys(this.form).forEach((function(t){void 0!==e.formOrigin[t]&&(e.form[t]=e.formOrigin[t])}))},validateConfirmPassword:function(e,t,a){t!==this.form.init_password?a(new Error(\"密码不一致\")):a()},handleClose:function(){this.$refs.form.clearValidate(),this.typeConfirm=\"password\",this.typeInitial=\"password\",this.sConfirmLoading=!1},handleOk:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.typeConfirm=\"password\",e.typeInitial=\"password\",e.sConfirmLoading=!0,e.$emit(\"validate:ok\",e.form))}))}}},p=f,m=(a(\"2d1d\"),a(\"2877\")),b=Object(m[\"a\"])(p,u,d,!1,null,\"454a8208\",null),g=b.exports,h=a(\"ee97\");function v(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/del\",method:\"delete\",data:e})}function _(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/batch_del\",method:\"delete\",data:e})}function w(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/update\",method:\"post\",data:e})}function y(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/add\",method:\"post\",data:e})}function k(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/list\",method:\"get\",params:e})}function x(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/update_disable\",method:\"post\",data:e})}function O(e){return Object(h[\"a\"])({url:\"/api/ieg/v1/user/batch_update_disable\",method:\"post\",data:e})}function C(){return Object(h[\"a\"])({url:\"/api/ieg/v1/user_role/list\",method:\"get\"})}function j(){return Object(h[\"a\"])({url:\"/api/ieg/v1/user_role/list_key_value\",method:\"get\"})}var S=a(\"3a86\"),R=a(\"d2c9\"),$={name:\"AccountManagement\",components:{Edit:g,Pagination:l[\"a\"]},data:function(){return{formOrigin:{},list:[],loading:!1,edit:{visible:!1,confirmLoading:!1},listKeyValue:[],tableHeight:0,selectedRowKeys:[],selected:[],disabledButton:!1,invocationButton:!1,pagination:{current:1,pageSize:20,total:0,showSizeChanger:!0,pageSizeOptions:[\"10\",\"20\",\"50\",\"100\"]}}},mounted:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(R[\"a\"])();case 2:e.getList(),e.getRoleName(),window.addEventListener(\"resize\",(function(){e.tableHeight=Object(S[\"a\"])(e.pagination.total)-50}));case 5:case\"end\":return t.stop()}}),t)})))()},beforeDestroy:function(){var e=this;window.removeEventListener(\"resize\",(function(){e.tableHeight=Object(S[\"a\"])(e.pagination.total)-50}))},methods:{getList:function(e){var t=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.loading=!0,a.prev=1,a.next=4,k(Object(i[\"a\"])({page:t.pagination.current,limit:t.pagination.pageSize},e||{}));case 4:r=a.sent,0===r.code&&(t.list=r.data.list.map((function(e){return Object.keys(e).forEach((function(t){\"string\"!==typeof e[t]||e[t]||(e[t]=\"-\")})),e})),t.pagination=Object(i[\"a\"])(Object(i[\"a\"])({},t.pagination),{},{total:r.data.pagination.total}),t.$nextTick((function(){t.tableHeight=Object(S[\"a\"])(t.pagination.total)-50}))),a.next=12;break;case 8:a.prev=8,a.t0=a[\"catch\"](1),console.error(\"获取用户列表失败:\",a.t0),t.$message.error(\"获取用户列表失败\");case 12:return a.prev=12,t.loading=!1,a.finish(12);case 15:case\"end\":return a.stop()}}),a,null,[[1,8,12,15]])})))()},getRoleName:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,j();case 3:a=t.sent,0===a.code&&(e.listKeyValue=a.data.list),t.next=11;break;case 7:t.prev=7,t.t0=t[\"catch\"](0),console.error(\"获取角色列表失败:\",t.t0),e.$message.error(\"获取角色列表失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,7]])})))()},handleEdit:function(e){var t=this;if(this.formOrigin={},\"number\"===typeof e){var a=this.list[e];Object.keys(a).forEach((function(e){t.formOrigin[e]=\"-\"===a[e]?void 0:a[e]}))}this.edit.visible=!0},handleSave:function(e){var t=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function a(){var r,n,i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,r=e.id,!r){a.next=8;break}return a.next=5,w({id:e.id,init_password:e.init_password,confirm_password:e.confirm_password,remark:e.remark,role_id:e.role_id});case 5:a.t0=a.sent,a.next=11;break;case 8:return a.next=10,y(e);case 10:a.t0=a.sent;case 11:n=a.t0,i=r?\"编辑\":\"新增\",0===n.code&&(t.$message.success(n.msg||\"\".concat(i,\"成功\")),t.edit.visible=!1,t.getList()),a.next=20;break;case 16:a.prev=16,a.t1=a[\"catch\"](0),console.error(\"保存用户信息失败:\",a.t1),t.$message.error(\"保存用户信息失败\");case 20:return a.prev=20,t.edit.confirmLoading=!1,a.finish(20);case 23:case\"end\":return a.stop()}}),a,null,[[0,16,20,23]])})))()},handleDel:function(e){var t=this;this.formOrigin={},this.$confirm(\"number\"===typeof e?\"确定删除该用户吗?\":\"确定删除已选用户吗？\",\"提示\",{confirmButtonText:\"删除\",cancelButtonText:\"取消\",type:\"warning\",center:!0}).then(Object(o[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,\"number\"!==typeof e){a.next=7;break}return a.next=4,v({id:e});case 4:a.t0=a.sent,a.next=10;break;case 7:return a.next=9,_({ids:e});case 9:a.t0=a.sent;case 10:r=a.t0,0===r.code&&(t.$message.success(r.msg||\"删除成功\"),t.selectedRowKeys=[],t.getList()),a.next=18;break;case 14:a.prev=14,a.t1=a[\"catch\"](0),console.error(\"删除用户失败:\",a.t1),t.$message.error(\"删除用户失败\");case 18:case\"end\":return a.stop()}}),a,null,[[0,14]])})))).catch((function(){}))},updateStatus:function(e,t){var a=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function r(){var n,i;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,\"number\"===typeof e){r.next=8;break}return r.next=4,O({ids:e,is_disable:t});case 4:n=r.sent,0===n.code&&(a.$message.success(n.msg||\"更新成功\"),a.selectedRowKeys=[],a.getList()),r.next=12;break;case 8:return r.next=10,x({id:e,is_disable:t});case 10:i=r.sent,0===i.code&&(a.$message.success(i.msg||\"更新成功\"),a.selectedRowKeys=[],a.getList());case 12:r.next=18;break;case 14:r.prev=14,r.t0=r[\"catch\"](0),console.error(\"更新用户状态失败:\",r.t0),a.$message.error(\"更新用户状态失败\");case 18:case\"end\":return r.stop()}}),r,null,[[0,14]])})))()},handleSelectionChange:function(e){var t=e.map((function(e){return e.id})),a=e;this.disabledButton=a.every((function(e){return 0!==e.is_disable})),this.invocationButton=a.every((function(e){return 1!==e.is_disable})),this.selected=e,this.selectedRowKeys=t},handlePaginationChange:function(e){this.pagination.current=e,this.getList()},handleShowSizeChange:function(e,t){this.pagination.current=1,this.pagination.pageSize=t,this.getList()}}},E=$,L=(a(\"c291\"),a(\"abe1\")),T=a.n(L),P=Object(m[\"a\"])(E,s,c,!1,null,\"87fa444a\",null);\"function\"===typeof T.a&&T()(P);var V=P.exports,I=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"role-container\"},[a(\"div\",{staticClass:\"role-actions\"}),a(\"div\",{staticClass:\"role-content\"},[a(\"el-table\",{staticStyle:{width:\"100%\"},attrs:{data:e.list}},[a(\"el-table-column\",{attrs:{prop:\"role_name\",label:\"角色名称\",width:\"120\"}}),a(\"el-table-column\",{attrs:{prop:\"remark\",label:\"描述\",width:\"120\"}}),a(\"el-table-column\",{attrs:{prop:\"rule_description\",label:\"权限范围\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"permission-tags\"},e._l(t.row.rule_description,(function(t,r){return a(\"el-tag\",{key:r,staticStyle:{margin:\"2px\"}},[e._v(\" \"+e._s(t)+\" \")])})),1)]}}])}),a(\"el-table-column\",{attrs:{prop:\"user_names\",label:\"关联用户\",width:\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return e._l(t.row.user_names,(function(t,r){return a(\"el-tag\",{key:r,staticClass:\"user-tag\",staticStyle:{margin:\"2px\"},attrs:{size:\"small\"}},[e._v(\" \"+e._s(t)+\" \")])}))}}])})],1)],1)])},K=[],A={name:\"RoleManagement\",data:function(){return{list:[]}},mounted:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.getRoleList();case 1:case\"end\":return t.stop()}}),t)})))()},methods:{getRoleList:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,C();case 3:a=t.sent,0===a.code&&(e.list=a.data.list),t.next=11;break;case 7:t.prev=7,t.t0=t[\"catch\"](0),console.error(\"获取角色列表失败:\",t.t0),e.$message.error(\"获取角色列表失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,7]])})))()}}},q=A,B=(a(\"d745\"),a(\"cb87\")),U=a.n(B),D=Object(m[\"a\"])(q,I,K,!1,null,\"251e364f\",null);\"function\"===typeof U.a&&U()(D);var z=D.exports,M={name:\"UserManagement\",components:{AccountManagement:V,RoleManagement:z},data:function(){return{activeTab:\"account\"}},mounted:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(R[\"a\"])();case 2:a=e.$route.query.tab,a&&(e.activeTab=a);case 4:case\"end\":return t.stop()}}),t)})))()},methods:{handleTabClick:function(e){this.$router.push({query:Object(i[\"a\"])(Object(i[\"a\"])({},this.$route.query),{},{tab:e.name})})}}},N=M,H=(a(\"7a6f\"),a(\"b3db\")),W=a.n(H),F=Object(m[\"a\"])(N,r,n,!1,null,\"785012d0\",null);\"function\"===typeof W.a&&W()(F);t[\"default\"]=F.exports},\"9b77\":function(e,t,a){},a623:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").every,i=a(\"a640\"),o=a(\"ae40\"),s=i(\"every\"),c=o(\"every\");r({target:\"Array\",proto:!0,forced:!s||!c},{every:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},ab13:function(e,t,a){var r=a(\"b622\"),n=r(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[n]=!1,\"/./\"[e](t)}catch(r){}}return!1}},abe1:function(e,t){},b3db:function(e,t){},bae8:function(e,t,a){\"use strict\";a.d(t,\"b\",(function(){return n})),a.d(t,\"a\",(function(){return i}));var r=a(\"ee97\");function n(e){return Object(r[\"a\"])({url:\"/api/ieg/v1/login/login_in\",method:\"post\",data:e})}function i(){return Object(r[\"a\"])({url:\"/api/ieg/v1/system/info\",method:\"get\"})}},c291:function(e,t,a){\"use strict\";var r=a(\"9b77\"),n=a.n(r);n.a},cb87:function(e,t){},ccf2:function(e,t,a){},d2c9:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return s}));a(\"d3b7\"),a(\"25f0\"),a(\"96cf\");var r=a(\"c964\"),n=a(\"bae8\"),i=6e5;function o(){var e=localStorage.getItem(\"hg_token_timestamp\");if(!e)return!1;var t=(new Date).getTime(),a=parseInt(e),r=t-a;return r<i}function s(){return c.apply(this,arguments)}function c(){return c=Object(r[\"a\"])(regeneratorRuntime.mark((function e(){var t,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=localStorage.getItem(\"hg_token\"),!t){e.next=7;break}if(a=o(),!a){e.next=6;break}return console.log(\"本地token有效，无需重新登录\"),e.abrupt(\"return\",t);case 6:console.log(\"本地token已过期，需要重新登录\");case 7:return e.prev=7,e.next=10,Object(n[\"b\"])({name:\"admin\",password:\"123456\"});case 10:if(r=e.sent,!(r&&0===r.code&&r.data&&r.data.token)){e.next=18;break}return console.log(\"隐式登录成功，获取到新token\"),localStorage.setItem(\"hg_token\",r.data.token),localStorage.setItem(\"hg_token_timestamp\",(new Date).getTime().toString()),e.abrupt(\"return\",r.data.token);case 18:return console.error(\"隐式登录失败:\",r),e.abrupt(\"return\",\"\");case 20:e.next=26;break;case 22:return e.prev=22,e.t0=e[\"catch\"](7),console.error(\"隐式登录出错:\",e.t0),e.abrupt(\"return\",\"\");case 26:case\"end\":return e.stop()}}),e,null,[[7,22]])}))),c.apply(this,arguments)}},d745:function(e,t,a){\"use strict\";var r=a(\"0df3\"),n=a.n(r);n.a},d81d:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").map,i=a(\"1dde\"),o=a(\"ae40\"),s=i(\"map\"),c=o(\"map\");r({target:\"Array\",proto:!0,forced:!s||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},ee97:function(e,t,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var r=a(\"bc3a\"),n=a.n(r),i=a(\"4360\"),o=a(\"a18c\"),s=a(\"a47e\"),c=a(\"f7b5\"),l=a(\"f907\"),u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),u=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,f=r.VUE_APP_BASE_API,p=\"true\"===d?\"\":f;\"production\"===u&&(p=\"\");var m={baseURL:p,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===u&&(m.timeout=a),t){case\"upload\":m.headers[\"Content-Type\"]=\"multipart/form-data\",m[\"processData\"]=!1,m[\"contentType\"]=!1;break;case\"download\":m[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var b=n.a.create(m);return b.interceptors.request.use((function(e){var t=i[\"a\"].getters.token;if(\"\"!==t&&e.url.startsWith(\"/api/ieg/\")){var a=localStorage.getItem(\"hg_token\");a&&(e.headers[\"authtoken\"]=a)}return e}),(function(e){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:e,print:!0}),Promise.reject(\"response-err:\"+e)})),b.interceptors.response.use((function(e){var a=void 0===e.headers[\"code\"]?200:Number(e.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){o[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,n=\"\";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n=\"error\"),e.data.code>=2e3&&e.data.code<3e3&&(n=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(t),type:n}),Promise.reject(\"response-err-status:\".concat(r||l[\"a\"][a][t],\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.\".concat(a,\".\").concat(t))))};switch(e.data.code){case l[\"a\"].exception.system:t(\"system\");break;case l[\"a\"].exception.server:t(\"server\");break;case l[\"a\"].exception.session:r();break;case l[\"a\"].exception.access:r();break;case l[\"a\"].exception.certification:t(\"certification\");break;case l[\"a\"].exception.auth:t(\"auth\"),o[\"a\"].replace({path:\"/401\"});break;case l[\"a\"].exception.token:t(\"token\");break;case l[\"a\"].exception.param:t(\"param\");break;case l[\"a\"].exception.idempotency:t(\"idempotency\");break;case l[\"a\"].exception.ip:t(\"ip\"),i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"});break;case l[\"a\"].exception.upload:t(\"upload\");break;case l[\"a\"].attack.xss:t(\"xss\",\"attack\");break;default:t(\"code\",\"exception\",-1);break}};switch(t){case\"upload\":if(0===a)return e.data.data;n();break;case\"download\":if(0===a)return{data:e.data,fileName:decodeURI(e.headers[\"file-name\"])};n();break;default:if(0===e.data.code)return e.data;n();break}}),(function(e){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){o[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===t?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(s[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:\".concat(e,\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.service.timeout\"))))})),b(e)};t[\"a\"]=u}}]);", "extractedComments": []}