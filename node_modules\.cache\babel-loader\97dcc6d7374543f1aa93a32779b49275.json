{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\api\\auditold\\strategyCollection.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\api\\auditold\\strategyCollection.js", "mtime": 1750384015792}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}