{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyProtocol\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyProtocol\\index.vue", "mtime": 1750323118519}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}