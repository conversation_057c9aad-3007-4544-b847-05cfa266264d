<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-select v-model="searchForm.type" clearable placeholder="策略类型" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="采集" :value="0" />
              <el-option label="过滤" :value="1" />
            </el-select>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button type="danger" @click="batchDeleteProtocol">批量删除</el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-select v-model="searchForm.type" clearable placeholder="策略类型" @change="handleSearch">
                  <el-option label="全部" value="" />
                  <el-option label="采集" :value="0" />
                  <el-option label="过滤" :value="1" />
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">策略审计记录</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableList.rows || []"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="onSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="协议" show-overflow-tooltip />
          <el-table-column prop="addTime" label="时间">
            <template slot-scope="scope">
              {{
                scope.row.addTime === '-'
                  ? scope.row.addTime
                  : formatTime(scope.row.addTime)
              }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span :class="scope.row.status == '0' ? 'status-failed' : 'status-success'">
                {{ scope.row.status == '0' ? '下发失败' : '下发成功' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="operateType" label="操作类型">
            <template slot-scope="scope">
              {{ scope.row.operateType == 0 ? '采集' : '过滤' }}
            </template>
          </el-table-column>
          <el-table-column prop="counts" label="操作数量" />
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="deleteProtocol(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="tableList.total > 0"
        small
        background
        align="right"
        :current-page="pagination.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableList.total || 0"
        @size-change="onShowSizeChange"
        @current-change="handlePageChange"
      />
    </footer>

    <view-component ref="viewRef" />
  </div>
</template>

<script>
import { tacticsPages, tacticsDeleteR } from '@api/auditold/strategyAuditRecord'
import ViewComponent from './components/view'
import moment from 'moment'

export default {
  name: 'StrategyAuditRecord',
  components: {
    ViewComponent,
  },
  data() {
    return {
      isShow: false,
      tableList: {},
      searchForm: {
        type: '',
      },
      queryValue: {},
      loading: false,
      selectedRowKeys: [],
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },

    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
              ...this.queryValue,
            }
          : {
              ...this.pagination,
              ...this.queryValue,
            }
        const res = await tacticsPages(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
          this.selectedRowKeys = []
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 删除
    deleteProtocol(record = {}) {
      this.$confirm('确定要删除选中协议记录吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(async () => {
          try {
            const res = await tacticsDeleteR({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getSourceData()
            } else {
              this.$message.error(res.msg)
            }
          } catch (err) {
            console.error('删除失败:', err)
          }
        })
        .catch(() => {
          console.log('取消删除')
        })
    },

    // 批量删除
    batchDeleteProtocol() {
      if (this.selectedRowKeys.length) {
        this.$confirm('确定要删除选中策略记录吗?删除后不可恢复', '删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
        })
          .then(async () => {
            try {
              const res = await tacticsDeleteR({ ids: this.selectedRowKeys.join(',') })
              if (res.retcode === 0) {
                this.$message.success('删除成功')
                this.calcPageNo(this.tableList, this.selectedRowKeys.length)
                this.getSourceData()
              } else {
                this.$message.error(res.msg)
              }
            } catch (err) {
              console.error('批量删除失败:', err)
            }
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 查看
    view(record = {}) {
      this.$refs.viewRef.showDrawer(record)
    },

    // 条件查询
    handleSearch() {
      this.queryValue = { ...this.searchForm }
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 条件清除
    handleReset() {
      this.searchForm = {
        type: '',
      }
      this.queryValue = {}
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 选择改变
    onSelectionChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },

    // 计算页码
    calcPageNo(tableList, deleteCount) {
      const { pageIndex, pageSize } = this.pagination
      const total = tableList.total || 0
      const currentPageCount = tableList.rows ? tableList.rows.length : 0

      if (currentPageCount <= deleteCount && pageIndex > 1) {
        this.pagination.pageIndex = pageIndex - 1
      }
    },

    // 格式化时间
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
  },
}
</script>

<style scoped lang="scss">
.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}
</style>
