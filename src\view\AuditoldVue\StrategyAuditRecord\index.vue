<template>
  <div>
    <el-form
      class="searchBg"
      :model="searchForm"
      label-width="120px"
      :inline="true"
      @submit.native.prevent="handleSearch"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="策略类型">
            <el-select v-model="searchForm.type" placeholder="全部">
              <el-option label="全部" value="" />
              <el-option label="采集" :value="0" />
              <el-option label="过滤" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="searchBtn">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button style="margin-left: 15px" @click="handleReset">清除</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div style="margin-bottom: 20px; margin-top: 20px">
      <el-button style="border-radius: 2px" @click="batchDeleteProtocol">批量删除</el-button>
    </div>
    <div class="tableBg">
      <el-table
        :data="tableList.list || []"
        :loading="loading"
        row-key="id"
        @selection-change="onSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="协议" />
        <el-table-column prop="addTime" label="时间">
          <template slot-scope="scope">
            {{
              scope.row.addTime === '-'
                ? scope.row.addTime
                : $moment(scope.row.addTime).format('YYYY-MM-DD HH:mm:ss')
            }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span
              :class="scope.row.status == '0' ? 'redColor' : 'blueColor'"
            >
              {{ scope.row.status == '0' ? '下发失败' : '下发成功' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="operateType" label="操作类型">
          <template slot-scope="scope">
            {{ scope.row.operateType == 0 ? '采集' : '过滤' }}
          </template>
        </el-table-column>
        <el-table-column prop="counts" label="操作数量" />
        <el-table-column prop="description" label="描述" width="500" show-overflow-tooltip />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="table-option">
              <el-button type="text" @click="deleteProtocol(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-row type="flex" justify="end" style="margin-top: 20px">
        <el-pagination
          :current-page="pagination.pageIndex"
          :page-size="pagination.pageSize"
          :total="tableList.total || 0"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onShowSizeChange"
          @current-change="handlePageChange"
        />
      </el-row>
    </div>
    <view-component ref="viewRef" />
  </div>
</template>

<script>
import { tacticsPages, tacticsDeleteR } from '@api/auditold/strategyAuditRecord'
import ViewComponent from './components/view'
import moment from 'moment'

export default {
  name: 'StrategyAuditRecord',
  components: {
    ViewComponent,
  },
  data() {
    return {
      tableList: {},
      searchForm: {
        type: '',
      },
      queryValue: {},
      loading: false,
      selectedRowKeys: [],
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
              ...this.queryValue,
            }
          : {
              ...this.pagination,
              ...this.queryValue,
            }
        const res = await tacticsPages(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
          this.selectedRowKeys = []
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 删除
    deleteProtocol(record = {}) {
      this.$confirm('确定要删除选中协议记录吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(async () => {
          try {
            const res = await tacticsDeleteR({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.getSourceData()
            } else {
              this.$message.error(res.msg)
            }
          } catch (err) {
            console.error('删除失败:', err)
          }
        })
        .catch(() => {
          console.log('取消删除')
        })
    },

    // 批量删除
    batchDeleteProtocol() {
      if (this.selectedRowKeys.length) {
        this.$confirm('确定要删除选中策略记录吗?删除后不可恢复', '删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
        })
          .then(async () => {
            try {
              const res = await tacticsDeleteR({ ids: this.selectedRowKeys.join(',') })
              if (res.retcode === 0) {
                this.$message.success('删除成功')
                this.calcPageNo(this.tableList, this.selectedRowKeys.length)
                this.getSourceData()
              } else {
                this.$message.error(res.msg)
              }
            } catch (err) {
              console.error('批量删除失败:', err)
            }
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 查看
    view(record = {}) {
      this.$refs.viewRef.showDrawer(record)
    },

    // 条件查询
    handleSearch() {
      this.queryValue = { ...this.searchForm }
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 条件清除
    handleReset() {
      this.searchForm = {
        type: '',
      }
      this.queryValue = {}
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 选择改变
    onSelectionChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },

    // 计算页码
    calcPageNo(tableList, deleteCount) {
      const { pageIndex, pageSize } = this.pagination
      const total = tableList.total || 0
      const currentPageCount = tableList.list ? tableList.list.length : 0
      
      if (currentPageCount <= deleteCount && pageIndex > 1) {
        this.pagination.pageIndex = pageIndex - 1
      }
    },
  },
}
</script>

<style scoped lang="scss">
.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}

.searchBtn {
  text-align: right;
}

.tableBg {
  .el-table {
    border: 1px solid #ebeef5;
  }
}

.table-option {
  a {
    margin-right: 10px;
    color: #1890ff;
    cursor: pointer;
    
    &:hover {
      color: #40a9ff;
    }
  }
}

.redColor {
  color: #f56c6c;
}

.blueColor {
  color: #409eff;
}
</style>
