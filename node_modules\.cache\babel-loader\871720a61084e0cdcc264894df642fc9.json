{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\src\\router\\module\\guard.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\router\\module\\guard.js", "mtime": 1750060609938}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZyI7CmV4cG9ydCBkZWZhdWx0IFt7CiAgcGF0aDogJy9ndWFyZC9kZXZpY2UtbGlzdCcsCiAgbmFtZTogJ0RldmljZUxpc3QnLAogIGNvbXBvbmVudDogZnVuY3Rpb24gY29tcG9uZW50KCkgewogICAgcmV0dXJuIGltcG9ydCgnQC92aWV3L2Fzc2V0L1NlbnRpbmVWdWUvRGV2aWNlTWFuYWdlbWVudC9pbmRleCcpOwogIH0sCiAgbWV0YTogewogICAgdGl0bGU6ICforr7lpIfliJfooagnLAogICAgaWNvbjogJ2xpc3QnCiAgfQp9LCB7CiAgcGF0aDogJy9ndWFyZC9kZXZpY2UtZ3JvdXAnLAogIG5hbWU6ICdEZXZpY2VHcm91cCcsCiAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICByZXR1cm4gaW1wb3J0KCdAL3ZpZXcvYXNzZXQvSG9zdGd1YXJkaWFuZ3JvdXAvR3JvdXBNYW5hZ2VtZW50Jyk7CiAgfSwKICBtZXRhOiB7CiAgICB0aXRsZTogJ+iuvuWkh+WIhue7hCcsCiAgICBpY29uOiAnZ3JvdXAnCiAgfQp9LCB7CiAgcGF0aDogJy9ndWFyZC91cGdyYWRlLW1hbmFnZW1lbnQnLAogIG5hbWU6ICdVcGdyYWRlTWFuYWdlbWVudCcsCiAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICByZXR1cm4gaW1wb3J0KCdAL3ZpZXcvYXNzZXQvU2VudGluZVZ1ZS9VcGdyYWRlTWFuYWdlbWVudC9pbmRleCcpOwogIH0sCiAgbWV0YTogewogICAgdGl0bGU6ICfljYfnuqfnrqHnkIYnLAogICAgaWNvbjogJ3VwZ3JhZGUnCiAgfQp9LCB7CiAgcGF0aDogJy9ndWFyZC9hdXRoLW1hbmFnZW1lbnQnLAogIG5hbWU6ICdBdXRoTWFuYWdlbWVudCcsCiAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICByZXR1cm4gaW1wb3J0KCdAL3ZpZXcvYXNzZXQvU2VudGluZVZ1ZS9BdXRoTWFuYWdlbWVudC9pbmRleCcpOwogIH0sCiAgbWV0YTogewogICAgdGl0bGU6ICforqTor4HnrqHnkIYnLAogICAgaWNvbjogJ2F1dGgnCiAgfQp9LCB7CiAgcGF0aDogJy9ndWFyZC9zdHJhdGVneS9tYW5hZ2UnLAogIG5hbWU6ICdTdHJhdGVneU1hbmFnZW1lbnQnLAogIGNvbXBvbmVudDogZnVuY3Rpb24gY29tcG9uZW50KCkgewogICAgcmV0dXJuIGltcG9ydCgnQC92aWV3L2Fzc2V0L1NlbnRpbmVWdWUvU3RyYXRlZ3lTZW50aW5lL2luZGV4Jyk7CiAgfSwKICBtZXRhOiB7CiAgICB0aXRsZTogJ+etlueVpeeuoeeQhicsCiAgICBpY29uOiAnc3RyYXRlZ3knCiAgfQp9LCB7CiAgcGF0aDogJy9ndWFyZC9zdHJhdGVneS1yZWNvcmQnLAogIG5hbWU6ICdTdHJhdGVneVJlY29yZCcsCiAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICByZXR1cm4gaW1wb3J0KCdAL3ZpZXcvYXNzZXQvU2VudGluZVZ1ZS9TdHJhdGVneVJlY29yZC9pbmRleCcpOwogIH0sCiAgbWV0YTogewogICAgdGl0bGU6ICfnrZbnlaXorrDlvZUnLAogICAgaWNvbjogJ3JlY29yZCcKICB9Cn1dOw=="}, null]}