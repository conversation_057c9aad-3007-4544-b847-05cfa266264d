{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-623a88d6\"],{\"2ca0\":function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"06cf\").f,s=a(\"50c4\"),i=a(\"5a34\"),o=a(\"1d80\"),c=a(\"ab13\"),u=a(\"c430\"),l=\"\".startsWith,d=Math.min,p=c(\"startsWith\"),h=!u&&!p&&!!function(){var e=n(String.prototype,\"startsWith\");return e&&!e.writable}();r({target:\"String\",proto:!0,forced:!h&&!p},{startsWith:function(e){var t=String(o(this));i(e);var a=s(d(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return l?l.call(t,r,a):t.slice(a,a+r.length)===r}})},\"2d24\":function(e,t,a){},4706:function(e,t,a){},\"4d3a\":function(e,t,a){\"use strict\";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"router-wrap-table\"},[a(\"header\",{staticClass:\"table-header\"},[a(\"section\",{staticClass:\"table-header-main\"},[a(\"section\",{staticClass:\"table-header-search\"},[a(\"section\",{directives:[{name:\"show\",rawName:\"v-show\",value:!e.isShow,expression:\"!isShow\"}],staticClass:\"table-header-search-input\"},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:\"任务名称\",\"prefix-icon\":\"soc-icon-search\"},on:{change:e.handleQuery},model:{value:e.queryInput.taskName,callback:function(t){e.$set(e.queryInput,\"taskName\",t)},expression:\"queryInput.taskName\"}})],1),a(\"section\",{staticClass:\"table-header-search-button\"},[e.isShow?e._e():a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleQuery}},[e._v(\"查询\")]),a(\"el-button\",{on:{click:e.toggleShow}},[e._v(\" 高级搜索 \"),a(\"i\",{staticClass:\"el-icon--right\",class:e.isShow?\"el-icon-arrow-up\":\"el-icon-arrow-down\"})])],1)]),a(\"section\",{staticClass:\"table-header-button\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleUpgrade}},[e._v(\"设备升级\")]),a(\"el-button\",{attrs:{type:\"danger\"},on:{click:e.handleBatchDelete}},[e._v(\"批量删除\")])],1)]),a(\"section\",{staticClass:\"table-header-extend\"},[a(\"el-collapse-transition\",[a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.isShow,expression:\"isShow\"}]},[a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:6}},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:\"任务名称\"},on:{change:e.handleQuery},model:{value:e.queryInput.taskName,callback:function(t){e.$set(e.queryInput,\"taskName\",t)},expression:\"queryInput.taskName\"}})],1),a(\"el-col\",{attrs:{span:6}},[a(\"el-select\",{attrs:{clearable:\"\",placeholder:\"状态\"},on:{change:e.handleQuery},model:{value:e.queryInput.status,callback:function(t){e.$set(e.queryInput,\"status\",t)},expression:\"queryInput.status\"}},[a(\"el-option\",{attrs:{label:\"全部\",value:\"\"}}),a(\"el-option\",{attrs:{label:\"升级中\",value:\"upgrading\"}}),a(\"el-option\",{attrs:{label:\"成功\",value:\"success\"}}),a(\"el-option\",{attrs:{label:\"失败\",value:\"failed\"}})],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:24,align:\"right\"}},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleQuery}},[e._v(\"查询\")]),a(\"el-button\",{on:{click:e.handleReset}},[e._v(\"重置\")]),a(\"el-button\",{attrs:{icon:\"soc-icon-scroller-top-all\"},on:{click:e.toggleShow}})],1)],1)],1)])],1)]),a(\"main\",{staticClass:\"table-body\"},[e._m(0),a(\"section\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"table-body-main\"},[a(\"el-table\",{attrs:{data:e.tableData,\"element-loading-background\":\"rgba(0, 0, 0, 0.3)\",size:\"mini\",\"highlight-current-row\":\"\",\"tooltip-effect\":\"light\",height:\"100%\"},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"序号\",width:\"80\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s((e.pagination.currentPage-1)*e.pagination.pageSize+t.$index+1)+\" \")]}}])}),a(\"el-table-column\",{attrs:{prop:\"deviceName\",label:\"设备名称\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"deviceIp\",label:\"设备IP\"}}),a(\"el-table-column\",{attrs:{prop:\"updateTime\",label:\"时间\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.formatTime(t.row.updateTime))+\" \")]}}])}),a(\"el-table-column\",{attrs:{prop:\"description\",label:\"描述\"}}),a(\"el-table-column\",{attrs:{prop:\"versionNum\",label:\"版本\"}}),a(\"el-table-column\",{attrs:{prop:\"updateStatus\",label:\"升级状态\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",{class:e.getStatusClass(t.row.updateStatus)},[e._v(\" \"+e._s(e.getStatusText(t.row.updateStatus))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"150\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"action-buttons\"},[\"upgrading\"===t.row.status?a(\"el-button\",{staticClass:\"el-button--blue\",attrs:{type:\"text\"},on:{click:function(a){return e.handleViewProgress(t.row)}}},[e._v(\"查看进度\")]):e._e(),a(\"el-button\",{staticClass:\"el-button--blue\",attrs:{type:\"text\",disabled:\"upgrading\"===t.row.status},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])],1)]}}])})],1)],1)]),a(\"footer\",{staticClass:\"table-footer\"},[e.pagination.visible?a(\"el-pagination\",{attrs:{small:\"\",background:\"\",align:\"right\",\"current-page\":e.pagination.currentPage,\"page-sizes\":[10,20,50,100],\"page-size\":e.pagination.pageSize,layout:\"total, sizes, prev, pager, next, jumper\",total:e.pagination.total},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handlePageChange}}):e._e()],1),a(\"upgrade-modal\",{attrs:{visible:e.upgradeModalVisible},on:{\"update:visible\":function(t){e.upgradeModalVisible=t},\"on-submit\":e.handleUpgradeSubmit}}),a(\"progress-modal\",{attrs:{visible:e.progressModalVisible,\"current-task\":e.currentTask},on:{\"update:visible\":function(t){e.progressModalVisible=t}}})],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"section\",{staticClass:\"table-body-header\"},[a(\"h2\",{staticClass:\"table-body-title\"},[e._v(\"升级任务管理\")])])}],s=(a(\"a15b\"),a(\"d81d\"),a(\"f3f3\")),i=(a(\"96cf\"),a(\"c964\")),o=a(\"c9d9\");function c(e){return Object(o[\"a\"])({url:\"/dev/virusUpdate/pages\",method:\"post\",data:e||{}})}function u(e){return Object(o[\"a\"])({url:\"/dev/virusUpdate/start\",method:\"post\",data:e||{}})}function l(e){return Object(o[\"a\"])({url:\"/dev/virusUpdate/progress\",method:\"post\",data:e||{}})}function d(e){return Object(o[\"a\"])({url:\"/dev/virusUpdate/delete\",method:\"post\",data:e||{}})}function p(e){return Object(o[\"a\"])({url:\"/dev/device/all\",method:\"post\",data:e||{}})}var h=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-dialog\",{attrs:{title:\"设备升级\",visible:e.dialogVisible,width:\"700px\",\"before-close\":e.handleClose,\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[a(\"el-form\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],ref:\"form\",attrs:{model:e.formData,rules:e.rules,\"label-width\":\"120px\"}},[a(\"el-form-item\",{attrs:{label:\"升级包文件\",prop:\"upgradeFile\"}},[a(\"el-upload\",{ref:\"upload\",attrs:{action:e.uploadAction,headers:e.uploadHeaders,\"on-success\":e.handleUploadSuccess,\"on-error\":e.handleUploadError,\"before-upload\":e.beforeUpload,\"file-list\":e.fileList,\"auto-upload\":!0,accept:\".bin,.img,.tar,.gz\"}},[a(\"el-button\",[a(\"i\",{staticClass:\"el-icon-upload\"}),e._v(\" 选择升级包 \")]),a(\"div\",{staticClass:\"el-upload__tip\",attrs:{slot:\"tip\"},slot:\"tip\"},[e._v(\" 支持 .bin/.img/.tar/.gz 格式的升级包文件，文件大小不超过 500MB \")])],1)],1),e.upgradePackageInfo.name?a(\"el-form-item\",{attrs:{label:\"升级包信息\"}},[a(\"div\",{staticClass:\"package-info\"},[a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"label\"},[e._v(\"文件名：\")]),a(\"span\",{staticClass:\"value\"},[e._v(e._s(e.upgradePackageInfo.name))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"label\"},[e._v(\"文件大小：\")]),a(\"span\",{staticClass:\"value\"},[e._v(e._s(e.upgradePackageInfo.size))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"label\"},[e._v(\"版本号：\")]),a(\"span\",{staticClass:\"value\"},[e._v(e._s(e.upgradePackageInfo.version||\"未知\"))])]),a(\"div\",{staticClass:\"info-row\"},[a(\"span\",{staticClass:\"label\"},[e._v(\"适用设备：\")]),a(\"span\",{staticClass:\"value\"},[e._v(e._s(e.upgradePackageInfo.deviceType||\"通用\"))])])])]):e._e(),a(\"el-form-item\",{attrs:{label:\"选择设备\",prop:\"deviceIds\"}},[a(\"el-tree\",{ref:\"deviceTree\",staticStyle:{\"max-height\":\"200px\",overflow:\"auto\"},attrs:{data:e.deviceData,\"show-checkbox\":\"\",\"node-key\":\"srcId\",props:e.treeProps,\"check-strictly\":!1,\"default-checked-keys\":e.selectedDeviceIds},on:{check:e.handleTreeCheck}})],1),a(\"el-form-item\",{attrs:{label:\"升级选项\"}},[a(\"el-checkbox-group\",{model:{value:e.formData.upgradeOptions,callback:function(t){e.$set(e.formData,\"upgradeOptions\",t)},expression:\"formData.upgradeOptions\"}},[a(\"el-checkbox\",{attrs:{value:\"backup\"}},[e._v(\"升级前自动备份\")]),a(\"el-checkbox\",{attrs:{value:\"reboot\"}},[e._v(\"升级后自动重启\")]),a(\"el-checkbox\",{attrs:{value:\"verify\"}},[e._v(\"升级后验证\")])],1)],1),a(\"el-alert\",{attrs:{title:\"升级风险提示\",type:\"warning\",description:\"设备升级存在风险，升级过程中设备将暂时不可用。建议在业务低峰期进行升级操作，并确保升级包与设备型号匹配。\",\"show-icon\":\"\",closable:!1}})],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.handleClose}},[e._v(\"取消\")]),a(\"el-button\",{attrs:{type:\"danger\",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v(\"开始升级\")])],1)],1)},f=[],g=(a(\"4160\"),a(\"b0c0\"),a(\"b6802\"),a(\"159b\"),{name:\"UpgradeModal\",props:{visible:{type:Boolean,default:!1}},data:function(){return{loading:!1,submitLoading:!1,formData:{upgradeFile:\"\",deviceIds:[],upgradeOptions:[\"backup\",\"verify\"]},rules:{upgradeFile:[{required:!0,message:\"请选择升级包文件\",trigger:\"change\"}],deviceIds:[{required:!0,message:\"请选择设备\",trigger:\"change\"}]},deviceData:[],selectedDeviceIds:[],fileList:[],upgradePackageInfo:{},treeProps:{children:\"childList\",label:\"name\",disabled:function(e){return\"0\"===e.type}},uploadAction:\"/dev/upgrade/upload\",uploadHeaders:{authorization:\"authorization-text\"}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit(\"update:visible\",e)}}},watch:{visible:function(e){e&&(this.initForm(),this.loadDeviceData())}},methods:{initForm:function(){var e=this;this.formData={upgradeFile:\"\",deviceIds:[],upgradeOptions:[\"backup\",\"verify\"]},this.selectedDeviceIds=[],this.fileList=[],this.upgradePackageInfo={},this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},loadDeviceData:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,p({});case 4:a=t.sent,0===a.retcode?e.deviceData=e.transformTreeData(a.data||[]):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](1),e.$message.error(\"获取设备列表失败\");case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case\"end\":return t.stop()}}),t,null,[[1,8,11,14]])})))()},transformTreeData:function(e){var t=this;return e.map((function(e){var a=Object(s[\"a\"])(Object(s[\"a\"])({},e),{},{disabled:\"0\"===e.type});return e.childList&&e.childList.length>0&&(a.childList=t.transformTreeData(e.childList)),a}))},handleTreeCheck:function(e,t){var a=[],r=t.checkedNodes||[];r.forEach((function(e){\"1\"===e.type&&a.push(e.srcId)})),this.formData.deviceIds=a},beforeUpload:function(e){var t=/\\.(bin|img|tar|gz)$/i.test(e.name);if(!t)return this.$message.error(\"只能上传 .bin/.img/.tar/.gz 格式的升级包文件!\"),!1;var a=e.size/1024/1024<500;return!!a||(this.$message.error(\"升级包文件大小不能超过 500MB!\"),!1)},handleUploadSuccess:function(e,t){this.formData.upgradeFile=t.name,this.upgradePackageInfo={name:t.name,size:this.formatFileSize(t.size),version:e.version||\"未知\",deviceType:e.deviceType||\"通用\"},this.$message.success(\"\".concat(t.name,\" 上传成功\")),this.$refs.form.validateField(\"upgradeFile\")},handleUploadError:function(e,t){this.$message.error(\"\".concat(t.name,\" 上传失败\"))},formatFileSize:function(e){if(0===e)return\"0 B\";var t=1024,a=[\"B\",\"KB\",\"MB\",\"GB\"],r=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,r)).toFixed(2))+\" \"+a[r]},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i[\"a\"])(regeneratorRuntime.mark((function t(a){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a&&e.$confirm(\"确认开始升级吗？升级过程中设备将暂时不可用，请确保在业务低峰期进行操作。\",\"升级确认\",{confirmButtonText:\"开始升级\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.submitLoading=!0,t.prev=1,a={upgradeFile:e.formData.upgradeFile,deviceIds:e.formData.deviceIds.join(\",\"),upgradeOptions:e.formData.upgradeOptions.join(\",\")},t.next=5,u(a);case 5:r=t.sent,0===r.retcode?(e.$message.success(\"升级任务已启动，请在升级管理页面查看进度\"),e.$emit(\"on-submit\"),e.handleClose()):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t[\"catch\"](1),e.$message.error(\"启动升级失败\");case 12:return t.prev=12,e.submitLoading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[1,9,12,15]])})))).catch((function(){}));case 1:case\"end\":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){this.dialogVisible=!1}}}),v=g,m=(a(\"f33e\"),a(\"2877\")),b=Object(m[\"a\"])(v,h,f,!1,null,\"8b434e0a\",null),w=b.exports,k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-dialog\",{attrs:{title:\"升级进度\",visible:e.dialogVisible,width:\"800px\",\"before-close\":e.handleClose,\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[a(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"progress-content\"},[a(\"div\",{staticClass:\"task-info\"},[a(\"h3\",[e._v(e._s(e.currentTask.taskName))]),a(\"p\",[e._v(\"升级包：\"+e._s(e.currentTask.upgradeFile))]),a(\"p\",[e._v(\"设备数量：\"+e._s(e.currentTask.deviceCount))])]),a(\"div\",{staticClass:\"overall-progress\"},[a(\"h4\",[e._v(\"总体进度\")]),a(\"el-progress\",{attrs:{percentage:e.overallProgress,status:e.getProgressStatus(e.currentTask.status),\"stroke-width\":20}})],1),a(\"div\",{staticClass:\"device-progress\"},[a(\"h4\",[e._v(\"设备升级详情\")]),a(\"el-table\",{attrs:{data:e.deviceProgressList,size:\"mini\",height:\"300\"}},[a(\"el-table-column\",{attrs:{prop:\"deviceName\",label:\"设备名称\"}}),a(\"el-table-column\",{attrs:{prop:\"deviceIp\",label:\"设备IP\"}}),a(\"el-table-column\",{attrs:{prop:\"progress\",label:\"进度\",width:\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-progress\",{attrs:{percentage:t.row.progress,status:e.getDeviceProgressStatus(t.row.status),\"stroke-width\":8}})]}}])}),a(\"el-table-column\",{attrs:{prop:\"status\",label:\"状态\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",{class:e.getStatusClass(t.row.status)},[e._v(\" \"+e._s(e.getStatusText(t.row.status))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"message\",label:\"消息\",\"show-overflow-tooltip\":\"\"}})],1)],1)]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.handleClose}},[e._v(\"关闭\")]),a(\"el-button\",{attrs:{type:\"primary\",loading:e.refreshLoading},on:{click:e.handleRefresh}},[e._v(\"刷新\")])],1)])},y=[],$={name:\"ProgressModal\",props:{visible:{type:Boolean,default:!1},currentTask:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,refreshLoading:!1,overallProgress:0,deviceProgressList:[],timer:null}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit(\"update:visible\",e)}}},watch:{visible:function(e){e?(this.loadProgress(),this.startTimer()):this.stopTimer()}},methods:{loadProgress:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.currentTask.id){t.next=2;break}return t.abrupt(\"return\");case 2:return e.loading=!0,t.prev=3,t.next=6,l({taskId:e.currentTask.id});case 6:a=t.sent,0===a.retcode?(e.overallProgress=a.data.overallProgress||0,e.deviceProgressList=a.data.deviceProgress||[]):e.$message.error(a.msg),t.next=13;break;case 10:t.prev=10,t.t0=t[\"catch\"](3),e.$message.error(\"获取升级进度失败\");case 13:return t.prev=13,e.loading=!1,t.finish(13);case 16:case\"end\":return t.stop()}}),t,null,[[3,10,13,16]])})))()},handleRefresh:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.refreshLoading=!0,t.next=3,e.loadProgress();case 3:e.refreshLoading=!1;case 4:case\"end\":return t.stop()}}),t)})))()},startTimer:function(){var e=this;this.timer=setInterval((function(){e.loadProgress()}),5e3)},stopTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},getProgressStatus:function(e){return\"success\"===e?\"success\":\"failed\"===e?\"exception\":null},getDeviceProgressStatus:function(e){return\"success\"===e?\"success\":\"failed\"===e?\"exception\":null},getStatusText:function(e){var t={pending:\"等待中\",upgrading:\"升级中\",success:\"成功\",failed:\"失败\"};return t[e]||\"-\"},getStatusClass:function(e){var t={pending:\"status-info\",upgrading:\"status-warning\",success:\"status-success\",failed:\"status-failed\"};return t[e]||\"\"},handleClose:function(){this.dialogVisible=!1,this.stopTimer()},beforeDestroy:function(){this.stopTimer()}}},_=$,x=(a(\"a909\"),Object(m[\"a\"])(_,k,y,!1,null,\"d25ca5b4\",null)),S=x.exports,C=a(\"5a0c\"),D=a.n(C),M={name:\"UpgradeManage\",components:{UpgradeModal:w,ProgressModal:S},data:function(){return{isShow:!1,loading:!1,queryInput:{taskName:\"\",status:\"\"},tableData:[],selectedRows:[],pagination:{total:0,pageSize:10,currentPage:1,visible:!0},upgradeModalVisible:!1,progressModalVisible:!1,currentTask:{}}},mounted:function(){this.getUpgradeList()},methods:{toggleShow:function(){this.isShow=!this.isShow},getUpgradeList:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,a=Object(s[\"a\"])({pageIndex:e.pagination.currentPage,pageSize:e.pagination.pageSize},e.buildQueryParams()),t.prev=2,t.next=5,c(a);case 5:r=t.sent,0===r.retcode?(e.tableData=r.data.rows||[],e.pagination.total=r.data.total||0,e.selectedRows=[]):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t[\"catch\"](2),e.$message.error(\"获取升级任务列表失败\");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[2,9,12,15]])})))()},buildQueryParams:function(){var e={};return this.queryInput.taskName&&(e.taskName=this.queryInput.taskName),this.queryInput.status&&(e.status=this.queryInput.status),e},handleQuery:function(){this.pagination.currentPage=1,this.getUpgradeList()},handleReset:function(){this.queryInput={taskName:\"\",status:\"\"},this.handleQuery()},handleUpgrade:function(){this.upgradeModalVisible=!0},handleViewProgress:function(e){this.currentTask=e,this.progressModalVisible=!0},handleDelete:function(e){var t=this;this.$confirm(\"确定要删除该升级任务吗?删除后不可恢复\",\"删除\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(i[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,d({ids:e.id});case 3:r=a.sent,0===r.retcode?(t.$message.success(\"删除成功\"),t.getUpgradeList()):t.$message.error(r.msg),a.next=10;break;case 7:a.prev=7,a.t0=a[\"catch\"](0),t.$message.error(\"删除失败\");case 10:case\"end\":return a.stop()}}),a,null,[[0,7]])})))).catch((function(){}))},handleBatchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm(\"确定要删除选中升级任务吗?删除后不可恢复\",\"删除\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a=e.selectedRows.map((function(e){return e.id})).join(\",\"),t.next=4,d({ids:a});case 4:r=t.sent,0===r.retcode?(e.$message.success(\"删除成功\"),e.getUpgradeList()):e.$message.error(r.msg),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](0),e.$message.error(\"删除失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,8]])})))).catch((function(){})):this.$message.error(\"至少选中一条数据\")},handleUpgradeSubmit:function(){this.upgradeModalVisible=!1,this.getUpgradeList()},handleSelectionChange:function(e){this.selectedRows=e},handleSizeChange:function(e){this.pagination.pageSize=e,this.getUpgradeList()},handlePageChange:function(e){this.pagination.currentPage=e,this.getUpgradeList()},getStatusText:function(e){return 0==e?\"成功\":\"失败\"},getStatusClass:function(e){return 0==e?\"status-success\":\"status-failed\"},formatTime:function(e){return e&&\"-\"!==e&&null!=e?D()(e).format(\"YYYY-MM-DD HH:mm:ss\"):\"\"}}},O=M,P=(a(\"5f1e\"),Object(m[\"a\"])(O,r,n,!1,null,\"0b3b32c3\",null));t[\"default\"]=P.exports},\"5a0c\":function(e,t,a){!function(t,a){e.exports=a()}(0,(function(){\"use strict\";var e=1e3,t=6e4,a=36e5,r=\"millisecond\",n=\"second\",s=\"minute\",i=\"hour\",o=\"day\",c=\"week\",u=\"month\",l=\"quarter\",d=\"year\",p=\"date\",h=\"Invalid Date\",f=/^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/,g=/\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(e){var t=[\"th\",\"st\",\"nd\",\"rd\"],a=e%100;return\"[\"+e+(t[(a-20)%10]||t[a]||t[0])+\"]\"}},m=function(e,t,a){var r=String(e);return!r||r.length>=t?e:\"\"+Array(t+1-r.length).join(a)+e},b={s:m,z:function(e){var t=-e.utcOffset(),a=Math.abs(t),r=Math.floor(a/60),n=a%60;return(t<=0?\"+\":\"-\")+m(r,2,\"0\")+\":\"+m(n,2,\"0\")},m:function e(t,a){if(t.date()<a.date())return-e(a,t);var r=12*(a.year()-t.year())+(a.month()-t.month()),n=t.clone().add(r,u),s=a-n<0,i=t.clone().add(r+(s?-1:1),u);return+(-(r+(a-n)/(s?n-i:i-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:d,w:c,d:o,D:p,h:i,m:s,s:n,ms:r,Q:l}[e]||String(e||\"\").toLowerCase().replace(/s$/,\"\")},u:function(e){return void 0===e}},w=\"en\",k={};k[w]=v;var y=\"$isDayjsObject\",$=function(e){return e instanceof C||!(!e||!e[y])},_=function e(t,a,r){var n;if(!t)return w;if(\"string\"==typeof t){var s=t.toLowerCase();k[s]&&(n=s),a&&(k[s]=a,n=s);var i=t.split(\"-\");if(!n&&i.length>1)return e(i[0])}else{var o=t.name;k[o]=t,n=o}return!r&&n&&(w=n),n||!r&&w},x=function(e,t){if($(e))return e.clone();var a=\"object\"==typeof t?t:{};return a.date=e,a.args=arguments,new C(a)},S=b;S.l=_,S.i=$,S.w=function(e,t){return x(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var C=function(){function v(e){this.$L=_(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[y]=!0}var m=v.prototype;return m.parse=function(e){this.$d=function(e){var t=e.date,a=e.utc;if(null===t)return new Date(NaN);if(S.u(t))return new Date;if(t instanceof Date)return new Date(t);if(\"string\"==typeof t&&!/Z$/i.test(t)){var r=t.match(f);if(r){var n=r[2]-1||0,s=(r[7]||\"0\").substring(0,3);return a?new Date(Date.UTC(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(t)}(e),this.init()},m.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},m.$utils=function(){return S},m.isValid=function(){return!(this.$d.toString()===h)},m.isSame=function(e,t){var a=x(e);return this.startOf(t)<=a&&a<=this.endOf(t)},m.isAfter=function(e,t){return x(e)<this.startOf(t)},m.isBefore=function(e,t){return this.endOf(t)<x(e)},m.$g=function(e,t,a){return S.u(e)?this[t]:this.set(a,e)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(e,t){var a=this,r=!!S.u(t)||t,l=S.p(e),h=function(e,t){var n=S.w(a.$u?Date.UTC(a.$y,t,e):new Date(a.$y,t,e),a);return r?n:n.endOf(o)},f=function(e,t){return S.w(a.toDate()[e].apply(a.toDate(\"s\"),(r?[0,0,0,0]:[23,59,59,999]).slice(t)),a)},g=this.$W,v=this.$M,m=this.$D,b=\"set\"+(this.$u?\"UTC\":\"\");switch(l){case d:return r?h(1,0):h(31,11);case u:return r?h(1,v):h(0,v+1);case c:var w=this.$locale().weekStart||0,k=(g<w?g+7:g)-w;return h(r?m-k:m+(6-k),v);case o:case p:return f(b+\"Hours\",0);case i:return f(b+\"Minutes\",1);case s:return f(b+\"Seconds\",2);case n:return f(b+\"Milliseconds\",3);default:return this.clone()}},m.endOf=function(e){return this.startOf(e,!1)},m.$set=function(e,t){var a,c=S.p(e),l=\"set\"+(this.$u?\"UTC\":\"\"),h=(a={},a[o]=l+\"Date\",a[p]=l+\"Date\",a[u]=l+\"Month\",a[d]=l+\"FullYear\",a[i]=l+\"Hours\",a[s]=l+\"Minutes\",a[n]=l+\"Seconds\",a[r]=l+\"Milliseconds\",a)[c],f=c===o?this.$D+(t-this.$W):t;if(c===u||c===d){var g=this.clone().set(p,1);g.$d[h](f),g.init(),this.$d=g.set(p,Math.min(this.$D,g.daysInMonth())).$d}else h&&this.$d[h](f);return this.init(),this},m.set=function(e,t){return this.clone().$set(e,t)},m.get=function(e){return this[S.p(e)]()},m.add=function(r,l){var p,h=this;r=Number(r);var f=S.p(l),g=function(e){var t=x(h);return S.w(t.date(t.date()+Math.round(e*r)),h)};if(f===u)return this.set(u,this.$M+r);if(f===d)return this.set(d,this.$y+r);if(f===o)return g(1);if(f===c)return g(7);var v=(p={},p[s]=t,p[i]=a,p[n]=e,p)[f]||1,m=this.$d.getTime()+r*v;return S.w(m,this)},m.subtract=function(e,t){return this.add(-1*e,t)},m.format=function(e){var t=this,a=this.$locale();if(!this.isValid())return a.invalidDate||h;var r=e||\"YYYY-MM-DDTHH:mm:ssZ\",n=S.z(this),s=this.$H,i=this.$m,o=this.$M,c=a.weekdays,u=a.months,l=a.meridiem,d=function(e,a,n,s){return e&&(e[a]||e(t,r))||n[a].slice(0,s)},p=function(e){return S.s(s%12||12,e,\"0\")},f=l||function(e,t,a){var r=e<12?\"AM\":\"PM\";return a?r.toLowerCase():r};return r.replace(g,(function(e,r){return r||function(e){switch(e){case\"YY\":return String(t.$y).slice(-2);case\"YYYY\":return S.s(t.$y,4,\"0\");case\"M\":return o+1;case\"MM\":return S.s(o+1,2,\"0\");case\"MMM\":return d(a.monthsShort,o,u,3);case\"MMMM\":return d(u,o);case\"D\":return t.$D;case\"DD\":return S.s(t.$D,2,\"0\");case\"d\":return String(t.$W);case\"dd\":return d(a.weekdaysMin,t.$W,c,2);case\"ddd\":return d(a.weekdaysShort,t.$W,c,3);case\"dddd\":return c[t.$W];case\"H\":return String(s);case\"HH\":return S.s(s,2,\"0\");case\"h\":return p(1);case\"hh\":return p(2);case\"a\":return f(s,i,!0);case\"A\":return f(s,i,!1);case\"m\":return String(i);case\"mm\":return S.s(i,2,\"0\");case\"s\":return String(t.$s);case\"ss\":return S.s(t.$s,2,\"0\");case\"SSS\":return S.s(t.$ms,3,\"0\");case\"Z\":return n}return null}(e)||n.replace(\":\",\"\")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,p,h){var f,g=this,v=S.p(p),m=x(r),b=(m.utcOffset()-this.utcOffset())*t,w=this-m,k=function(){return S.m(g,m)};switch(v){case d:f=k()/12;break;case u:f=k();break;case l:f=k()/3;break;case c:f=(w-b)/6048e5;break;case o:f=(w-b)/864e5;break;case i:f=w/a;break;case s:f=w/t;break;case n:f=w/e;break;default:f=w}return h?f:S.a(f)},m.daysInMonth=function(){return this.endOf(u).$D},m.$locale=function(){return k[this.$L]},m.locale=function(e,t){if(!e)return this.$L;var a=this.clone(),r=_(e,t,!0);return r&&(a.$L=r),a},m.clone=function(){return S.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},v}(),D=C.prototype;return x.prototype=D,[[\"$ms\",r],[\"$s\",n],[\"$m\",s],[\"$H\",i],[\"$W\",o],[\"$M\",u],[\"$y\",d],[\"$D\",p]].forEach((function(e){D[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),x.extend=function(e,t){return e.$i||(e(t,C,x),e.$i=!0),x},x.locale=_,x.isDayjs=$,x.unix=function(e){return x(1e3*e)},x.en=k[w],x.Ls=k,x.p={},x}))},\"5a34\":function(e,t,a){var r=a(\"44e7\");e.exports=function(e){if(r(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"5f1e\":function(e,t,a){\"use strict\";var r=a(\"8340\"),n=a.n(r);n.a},8340:function(e,t,a){},a909:function(e,t,a){\"use strict\";var r=a(\"2d24\"),n=a.n(r);n.a},ab13:function(e,t,a){var r=a(\"b622\"),n=r(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[n]=!1,\"/./\"[e](t)}catch(r){}}return!1}},c9d9:function(e,t,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var r=a(\"bc3a\"),n=a.n(r),s=a(\"4360\"),i=a(\"a18c\"),o=a(\"a47e\"),c=a(\"f7b5\"),u=a(\"f907\"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),l=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,p=r.VUE_APP_BASE_API,h=\"true\"===d?\"\":p;\"production\"===l&&(h=\"\");var f={baseURL:h,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===l&&(f.timeout=a),t){case\"upload\":f.headers[\"Content-Type\"]=\"multipart/form-data\",f[\"processData\"]=!1,f[\"contentType\"]=!1;break;case\"download\":f[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var g=n.a.create(f);return g.interceptors.request.use((function(e){var t=s[\"a\"].getters.token;return\"\"!==t&&(e.headers[\"access_token\"]=t,e.url.startsWith(\"/api2/\")&&(e.headers[\"Authorization\"]=\"Basic YWRtaW5pc3RyYXRvcjpBZG1pbjEyMw==\")),e}),(function(e){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:e,print:!0}),Promise.reject(\"response-err:\"+e)})),g.interceptors.response.use((function(e){var a=void 0===e.headers[\"code\"]?200:Number(e.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(s[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,n=\"\";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n=\"error\"),e.data.code>=2e3&&e.data.code<3e3&&(n=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(t),type:n}),Promise.reject(\"response-err-status:\".concat(r||u[\"a\"][a][t],\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.\".concat(a,\".\").concat(t))))};switch(e.data.code){case u[\"a\"].exception.system:t(\"system\");break;case u[\"a\"].exception.server:t(\"server\");break;case u[\"a\"].exception.session:r();break;case u[\"a\"].exception.access:r();break;case u[\"a\"].exception.certification:t(\"certification\");break;case u[\"a\"].exception.auth:t(\"auth\"),i[\"a\"].replace({path:\"/401\"});break;case u[\"a\"].exception.token:t(\"token\");break;case u[\"a\"].exception.param:t(\"param\");break;case u[\"a\"].exception.idempotency:t(\"idempotency\");break;case u[\"a\"].exception.ip:t(\"ip\"),s[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"});break;case u[\"a\"].exception.upload:t(\"upload\");break;case u[\"a\"].attack.xss:t(\"xss\",\"attack\");break;default:t(\"code\",\"exception\",-1);break}};switch(t){case\"upload\":if(0===a)return e.data.data;n();break;case\"download\":if(0===a)return{data:e.data,fileName:decodeURI(e.headers[\"file-name\"])};n();break;default:if(0===e.data.code||0===e.data.retcode)return e.data;n();break}}),(function(e){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(s[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===t?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),403==e.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(o[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),403==e.response.status&&a(),Promise.reject(\"response-err-status:\".concat(e,\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.service.timeout\"))))})),g(e)};t[\"a\"]=l},d81d:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").map,s=a(\"1dde\"),i=a(\"ae40\"),o=s(\"map\"),c=i(\"map\");r({target:\"Array\",proto:!0,forced:!o||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},f33e:function(e,t,a){\"use strict\";var r=a(\"4706\"),n=a.n(r);n.a}}]);", "extractedComments": []}