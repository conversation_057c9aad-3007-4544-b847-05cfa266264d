{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-55564c2b\"],{\"2ca0\":function(t,e,a){\"use strict\";var r=a(\"23e7\"),i=a(\"06cf\").f,n=a(\"50c4\"),s=a(\"5a34\"),o=a(\"1d80\"),c=a(\"ab13\"),u=a(\"c430\"),d=\"\".startsWith,l=Math.min,p=c(\"startsWith\"),h=!u&&!p&&!!function(){var t=i(String.prototype,\"startsWith\");return t&&!t.writable}();r({target:\"String\",proto:!0,forced:!h&&!p},{startsWith:function(t){var e=String(o(this));s(t);var a=n(l(arguments.length>1?arguments[1]:void 0,e.length)),r=String(t);return d?d.call(e,r,a):e.slice(a,a+r.length)===r}})},\"440d\":function(t,e,a){},\"5a34\":function(t,e,a){var r=a(\"44e7\");t.exports=function(t){if(r(t))throw TypeError(\"The method doesn't accept regular expressions\");return t}},\"62c1\":function(t,e,a){\"use strict\";var r=a(\"8207\"),i=a.n(r);i.a},\"7b06\":function(t,e,a){\"use strict\";var r=a(\"95ac\"),i=a.n(r);i.a},8207:function(t,e,a){},\"8ac5\":function(t,e,a){\"use strict\";var r=a(\"9954\"),i=a.n(r);i.a},\"8c3b\":function(t,e,a){\"use strict\";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"status-container\"},[a(\"div\",{staticClass:\"status-content\"},[a(\"div\",{staticClass:\"status-grid\",staticStyle:{height:\"340px\"}},[a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"CPU状态\")]),a(\"span\",[t._v(\"CPU信息：\"+t._s(t.percentageCpuList&&t.percentageCpuList[0].info))])]),a(\"div\",{staticStyle:{width:\"100%\",height:\"200px\",\"margin-top\":\"20px\"}},[t.percentageCpuList?a(\"SinglePieChart\",{attrs:{id:\"system-base-views-pie\",\"percentage-list\":t.percentageCpuList}}):t._e()],1)]),a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"CPU使用趋势\")]),a(\"el-radio-group\",{staticClass:\"self-radio-group\",attrs:{size:\"small\"},on:{change:function(e){return t.onChangPie(e,\"cpu\")}},model:{value:t.cpuRadioValue,callback:function(e){t.cpuRadioValue=e},expression:\"cpuRadioValue\"}},[a(\"el-radio-button\",{staticClass:\"radio-first\",attrs:{label:\"1\"}},[t._v(\" 按天 \")]),a(\"el-radio-button\",{staticClass:\"radio-second\",attrs:{label:\"7\"}},[t._v(\" 按周 \")]),a(\"el-radio-button\",{staticClass:\"radio-third\",attrs:{label:\"30\"}},[t._v(\" 按月 \")])],1)],1),a(\"div\",{staticStyle:{width:\"100%\",height:\"255px\",\"margin-top\":\"15px\"}},[t.cpuList.length>0?a(\"SingleLineChart\",{staticStyle:{width:\"100%\"},attrs:{id:\"system-base-views-cpu\",\"inline-style\":\"height: 255px\",color:\"#00AB7A\",\"bar-data\":t.cpuList,unit:t.cpuListUnit}}):a(\"NoDataViews\",{attrs:{height:\"255px\"}})],1)])]),a(\"div\",{staticClass:\"status-grid status-section\"},[a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"内存状态\")]),a(\"span\",[t._v(\"内存大小：\"+t._s(t.percentageMemoryList&&t.percentageMemoryList[0].info))])]),a(\"div\",{staticStyle:{width:\"100%\",height:\"200px\",\"margin-top\":\"20px\"}},[t.percentageMemoryList?a(\"SinglePieChart\",{attrs:{id:\"system-base-views-pie-memory\",\"percentage-list\":t.percentageMemoryList}}):t._e()],1)]),a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"内存使用趋势\")]),a(\"el-radio-group\",{staticClass:\"self-radio-group\",attrs:{size:\"small\"},on:{change:function(e){return t.onChangPie(e,\"memory\")}},model:{value:t.memoryRadioValue,callback:function(e){t.memoryRadioValue=e},expression:\"memoryRadioValue\"}},[a(\"el-radio-button\",{staticClass:\"radio-first\",attrs:{label:\"1\"}},[t._v(\" 按天 \")]),a(\"el-radio-button\",{staticClass:\"radio-second\",attrs:{label:\"7\"}},[t._v(\" 按周 \")]),a(\"el-radio-button\",{staticClass:\"radio-third\",attrs:{label:\"30\"}},[t._v(\" 按月 \")])],1)],1),a(\"div\",{staticStyle:{width:\"100%\",height:\"255px\",\"margin-top\":\"15px\"}},[t.memoryList.length>0?a(\"SingleLineChart\",{staticStyle:{width:\"100%\"},attrs:{id:\"system-base-views-memory\",\"inline-style\":\"height: 255px\",color:\"#00AB7A\",\"bar-data\":t.memoryList,unit:t.memoryListUnit}}):a(\"NoDataViews\",{attrs:{height:\"255px\"}})],1)])]),a(\"div\",{staticClass:\"status-grid status-section\"},[a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"磁盘状态\")]),a(\"span\",[t._v(\"磁盘大小：\"+t._s(t.percentageHarddiskList&&t.percentageHarddiskList[0].info))])]),a(\"div\",{staticStyle:{width:\"100%\",height:\"200px\",\"margin-top\":\"20px\"}},[t.percentageHarddiskList?a(\"SinglePieChart\",{attrs:{id:\"system-base-views-pie-harddisk\",\"percentage-list\":t.percentageHarddiskList}}):t._e()],1)]),a(\"div\",{staticClass:\"status-card\"},[a(\"div\",{staticClass:\"status-header\"},[a(\"span\",{staticClass:\"status-title\"},[t._v(\"磁盘使用趋势\")]),a(\"el-radio-group\",{staticClass:\"self-radio-group\",attrs:{size:\"small\"},on:{change:function(e){return t.onChangPie(e,\"harddisk\")}},model:{value:t.harddiskRadioValue,callback:function(e){t.harddiskRadioValue=e},expression:\"harddiskRadioValue\"}},[a(\"el-radio-button\",{staticClass:\"radio-first\",attrs:{label:\"1\"}},[t._v(\" 按天 \")]),a(\"el-radio-button\",{staticClass:\"radio-second\",attrs:{label:\"7\"}},[t._v(\" 按周 \")]),a(\"el-radio-button\",{staticClass:\"radio-third\",attrs:{label:\"30\"}},[t._v(\" 按月 \")])],1)],1),a(\"div\",{staticStyle:{width:\"100%\",height:\"255px\",\"margin-top\":\"15px\"}},[t.harddiskList.length>0?a(\"SingleLineChart\",{staticStyle:{width:\"100%\"},attrs:{id:\"system-base-views-harddisk\",\"inline-style\":\"height: 255px\",color:\"#00AB7A\",\"bar-data\":t.harddiskList,unit:t.harddiskListUnit}}):a(\"NoDataViews\",{attrs:{height:\"255px\"}})],1)])])])])},i=[],n=(a(\"a9e3\"),a(\"b6802\"),a(\"96cf\"),a(\"c964\")),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"pie-chart-container\"},[a(\"div\",{staticClass:\"pie-chart-content\"},[a(\"div\",{staticClass:\"legend-container\"},t._l(t.optionLegend,(function(e,r){return a(\"div\",{key:r,staticClass:\"legend-item\"},[a(\"i\",{style:{background:\"已使用\"===e.name?e.count>80?t.optionLegendColor[0]:t.optionLegendColor[1]:t.optionLegendColor[2]}}),a(\"span\",{staticClass:\"breakwords legend-text\",attrs:{title:e.name}},[t._v(\" \"+t._s(e.name)+\" \")]),a(\"span\",{staticClass:\"breakwords legend-text\",attrs:{title:e.value}},[t._v(\" \"+t._s(e.value)+\" \")]),a(\"span\",{staticClass:\"breakwords legend-text\",attrs:{title:e.count+\"%\"}},[t._v(t._s(e.count)+\"%\")])])})),0),a(\"div\",{staticClass:\"chart-container\",attrs:{id:t.id}})])])},o=[],c=(a(\"99af\"),a(\"4160\"),a(\"b0c0\"),a(\"159b\"),a(\"313e\")),u=[\"#FF4141\",\"#2FC49A\",\"rgba(0,0,0,0.15)\"],d=[\"#FF4141\",\"rgba(0,0,0,0.15)\"],l={name:\"SinglePieChart\",props:{id:{type:String,default:\"\"},tooltipName:{type:String,default:\"\"},percentageList:{type:Array,default:function(){return[]}}},data:function(){return{optionLegend:[],sum:0,highLightIndex:0,optionLegendColor:[\"#FF4141\",\"#2FC49A\",\"rgba(0,0,0,0.15)\"],title:\"已使用\",myChart:null,option:{backgroundColor:\"#FFF\",tooltip:{trigger:\"item\",formatter:function(t){return'<div>\\n            <i class=\"icon\" style=\"width: 10px;height: 10px;border-radius: 10px;display: inline-block;background: '.concat(\"已使用\"===t.name||\"未使用\"===t.name?\"已使用\"===t.name?t.percent>80?u[0]:u[1]:u[2]:d[t.dataIndex],'\"></i>\\n            <span>').concat(t.name,\":</span>\\n            <span>\").concat(t.value).concat(t.data.unit,\"</span>\\n            </div>\")}},title:[{text:\"已使用\",x:\"center\",top:\"52%\",textStyle:{fontFamily:\"PingFang SC\",color:\"rgba(0, 0, 0, 0.65)\",fontSize:\"14\",fontWeight:\"400\",lineHeight:\"14\"}},{text:\"\",x:\"center\",top:\"36%\",textStyle:{fontSize:\"26\",color:\"rgba(0,0,0,0.85)\",fontFamily:\"PingFang SC\",foontWeight:\"500\",lineHeight:\"26\"}}],legend:{show:!1},series:[{name:\"\",type:\"pie\",radius:[\"70%\",\"91%\"],center:[\"50%\",\"50%\"],avoidLabelOverlap:!1,label:{show:!1,position:\"center\"},emphasis:{scale:!1},legendHoverLink:!1,labelLine:{show:!1},data:[]}]}}},watch:{percentageList:{handler:function(t){var e=this;if(this.optionLegend=[],this.sum=0,this.highLightIndex=0,t&&t.length){for(var a=0;a<t.length;a++)if(0!==t[a].value){this.highLightIndex=a;break}t.forEach((function(t){e.sum+=Number(t.value)})),t.forEach((function(t){e.optionLegend.push({name:t.name,value:t.value+t.unit,count:(isNaN(t.value/e.sum*100)?0:t.value/e.sum*100).toFixed(1)})})),this.myChart&&(this.option.title[1].text=t[0]&&t[0].titleText,this.option.series[0].data=t,this.myChart.setOption(this.option))}},immediate:!0}},mounted:function(){var t=this;this.$nextTick((function(){t.myChart=c[\"b\"](document.getElementById(t.id)),t.option.title[1].text=t.percentageList[0]&&t.percentageList[0].titleText,t.option.series[0].data=t.percentageList,t.myChart.setOption(t.option),window.addEventListener(\"resize\",t.resize)}))},destroyed:function(){window.removeEventListener(\"resize\",this.resize),this.myChart&&this.myChart.dispose()},methods:{resize:function(){this.myChart&&this.myChart.resize()}}},p=l,h=(a(\"8ac5\"),a(\"2877\")),m=Object(h[\"a\"])(p,s,o,!1,null,\"2d1537da\",null),g=m.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"line-chart-container\"},[a(\"div\",{staticClass:\"chart-container\",attrs:{id:t.id}})])},v=[],b=(a(\"d81d\"),{name:\"SingleLineChart\",props:{inlineStyle:{type:String,default:\"\"},id:{type:String,default:\"\"},unit:{type:String,default:\"\"},color:{type:String,default:\"#FF4141\"},barData:{type:Array,default:function(){return[]}}},data:function(){var t=this;return{myChart:null,option:{backgroundColor:\"#FFF\",grid:{left:\"3%\",right:\"4%\",bottom:\"3%\",top:\"3%\",containLabel:!0},tooltip:{trigger:\"axis\",axisPointer:{type:\"cross\",label:{backgroundColor:\"#6a7985\"}},formatter:function(e){var a=e[0];return\"\".concat(a.name,'<br/><span style=\"display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:').concat(t.color,';\"></span>').concat(a.seriesName,\": \").concat(a.value).concat(t.unit)}},xAxis:{type:\"category\",boundaryGap:!1,data:[],axisLabel:{formatter:function(t){return t}}},yAxis:{type:\"value\",axisLabel:{formatter:function(e){return e+t.unit}},min:0},series:[{name:\"使用率\",type:\"line\",stack:\"Total\",data:[],itemStyle:{color:this.color},lineStyle:{color:this.color},areaStyle:{color:{type:\"linear\",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:this.color},{offset:1,color:\"rgba(255, 255, 255, 0.1)\"}]}},smooth:!0}]}}},watch:{barData:{handler:function(t){if(t&&t.length){var e=t.map((function(t){return t.time})),a=t.map((function(t){return t.value}));this.myChart&&(this.option.xAxis.data=e,this.option.series[0].data=a,this.myChart.setOption(this.option))}},immediate:!0},color:{handler:function(t){this.myChart&&(this.option.series[0].itemStyle.color=t,this.option.series[0].lineStyle.color=t,this.option.series[0].areaStyle.color.colorStops[0].color=t,this.myChart.setOption(this.option))}}},mounted:function(){var t=this;this.$nextTick((function(){if(t.myChart=c[\"b\"](document.getElementById(t.id)),t.barData&&t.barData.length){var e=t.barData.map((function(t){return t.time})),a=t.barData.map((function(t){return t.value}));t.option.xAxis.data=e,t.option.series[0].data=a}t.myChart.setOption(t.option),window.addEventListener(\"resize\",t.resize)}))},destroyed:function(){window.removeEventListener(\"resize\",this.resize),this.myChart&&this.myChart.dispose()},methods:{resize:function(){this.myChart&&this.myChart.resize()}}}),y=b,x=(a(\"f077\"),Object(h[\"a\"])(y,f,v,!1,null,\"f859f326\",null)),C=x.exports,k=a(\"9e1e\"),_=a(\"ee97\");function L(t){return Object(_[\"a\"])({url:\"/api/ieg/v1/system/monitoring_usage_trends\",method:\"get\",params:t})}function w(){return Object(_[\"a\"])({url:\"/api/ieg/v1/system/monitoring_info\",method:\"get\"})}var S=a(\"d2c9\"),N={name:\"SystemStatus\",components:{SinglePieChart:g,SingleLineChart:C,NoDataViews:k[\"a\"]},data:function(){return{percentageCpuList:null,percentageMemoryList:null,percentageHarddiskList:null,cpuList:[],cpuListUnit:\"\",memoryList:[],memoryListUnit:\"\",harddiskList:[],harddiskListUnit:\"\",cpuRadioValue:\"30\",memoryRadioValue:\"30\",harddiskRadioValue:\"30\"}},mounted:function(){var t=this;return Object(n[\"a\"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(S[\"a\"])();case 2:t.getSystemMonitoringInfo(),t.getCpu(30),t.getMemory(30),t.getHarddisk(30);case 6:case\"end\":return e.stop()}}),e)})))()},methods:{getSystemMonitoringInfo:function(){var t=this;return Object(n[\"a\"])(regeneratorRuntime.mark((function e(){var a,r,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,w();case 3:a=e.sent,0===a.code&&(r=Number(a.data.cpu.use)+Number(a.data.cpu.not_use),i=Number(a.data.memory.use)+Number(a.data.memory.not_use),n=Number(a.data.harddisk.use)+Number(a.data.harddisk.not_use),t.percentageCpuList=[{name:\"已使用\",value:Number(a.data.cpu.use),percentage:Number(a.data.cpu.use)/r,use_ch:a.data.cpu.use_ch,info:a.data.cpu.info,unit:\"\",titleText:\"\".concat((Number(a.data.cpu.use)/r*100).toFixed(1),\"%\"),itemStyle:{color:(Number(a.data.cpu.use)/r*100).toFixed(1)>80?\"#FF4141\":\"#2FC49A\"}},{name:\"未使用\",value:Number(a.data.cpu.not_use),percentage:Number(a.data.cpu.not_use)/r,use_ch:a.data.cpu.not_use_ch,info:a.data.cpu.info,unit:\"\",itemStyle:{color:\"rgba(0,0,0,0.15)\"}}],t.percentageMemoryList=[{name:\"已使用\",value:Number(a.data.memory.use),percentage:Number(a.data.memory.use)/i,use_ch:a.data.memory.use_ch,info:a.data.memory.info,unit:a.data.memory.use_unit,titleText:a.data.memory.use+a.data.memory.use_unit,itemStyle:{color:(Number(a.data.cpu.use)/r*100).toFixed(1)>80?\"#FF4141\":\"#2FC49A\"}},{name:\"未使用\",value:Number(a.data.memory.not_use),percentage:Number(a.data.memory.not_use)/i,use_ch:a.data.memory.not_use_ch,info:a.data.memory.info,unit:a.data.memory.not_use_unit,itemStyle:{color:\"rgba(0,0,0,0.15)\"}}],t.percentageHarddiskList=[{name:\"已使用\",value:Number(a.data.harddisk.use),percentage:Number(a.data.harddisk.use)/n,use_ch:a.data.harddisk.use_ch,info:a.data.harddisk.info,unit:a.data.harddisk.use_unit,titleText:a.data.harddisk.use+a.data.harddisk.use_unit,itemStyle:{color:(Number(a.data.cpu.use)/r*100).toFixed(1)>80?\"#FF4141\":\"#2FC49A\"}},{name:\"未使用\",value:Number(a.data.harddisk.not_use),percentage:Number(a.data.harddisk.not_use)/n,use_ch:a.data.harddisk.not_use_ch,info:a.data.harddisk.info,unit:a.data.harddisk.not_use_unit,itemStyle:{color:\"rgba(0,0,0,0.15)\"}}]),e.next=10;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取系统监控信息失败:\",e.t0);case 10:case\"end\":return e.stop()}}),e,null,[[0,7]])})))()},onChangPie:function(t,e){\"cpu\"===e?this.getCpu(t):\"memory\"===e?this.getMemory(t):this.getHarddisk(t)},getCpu:function(t){var e=this;return Object(n[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.cpuList=[],e.cpuListUnit=\"\",a.next=5,L({m_time:t,m_type:\"cpu\"});case 5:r=a.sent,0===r.code&&(e.cpuList=r.data.trend,e.cpuListUnit=r.data.unit),a.next=12;break;case 9:a.prev=9,a.t0=a[\"catch\"](0),console.error(\"获取CPU使用趋势失败:\",a.t0);case 12:case\"end\":return a.stop()}}),a,null,[[0,9]])})))()},getMemory:function(t){var e=this;return Object(n[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.memoryList=[],e.memoryListUnit=\"\",a.next=5,L({m_time:t,m_type:\"mem\"});case 5:r=a.sent,0===r.code&&(e.memoryList=r.data.trend,e.memoryListUnit=r.data.unit),a.next=12;break;case 9:a.prev=9,a.t0=a[\"catch\"](0),console.error(\"获取内存使用趋势失败:\",a.t0);case 12:case\"end\":return a.stop()}}),a,null,[[0,9]])})))()},getHarddisk:function(t){var e=this;return Object(n[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.harddiskList=[],e.harddiskListUnit=\"\",a.next=5,L({m_time:t,m_type:\"disk\"});case 5:r=a.sent,0===r.code&&(e.harddiskList=r.data.trend,e.harddiskListUnit=r.data.unit),a.next=12;break;case 9:a.prev=9,a.t0=a[\"catch\"](0),console.error(\"获取磁盘使用趋势失败:\",a.t0);case 12:case\"end\":return a.stop()}}),a,null,[[0,9]])})))()}}},O=N,j=(a(\"62c1\"),Object(h[\"a\"])(O,r,i,!1,null,\"163b5394\",null));e[\"default\"]=j.exports},\"95ac\":function(t,e,a){},9954:function(t,e,a){},\"9e1e\":function(t,e,a){\"use strict\";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"no-data-container\",style:{height:t.height}},[t._m(0)])},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"no-data-content\"},[a(\"i\",{staticClass:\"el-icon-warning-outline\"}),a(\"p\",[t._v(\"暂无数据\")])])}],n={name:\"NoDataViews\",props:{height:{type:String,default:\"160px\"}}},s=n,o=(a(\"7b06\"),a(\"2877\")),c=Object(o[\"a\"])(s,r,i,!1,null,\"20b4fd27\",null);e[\"a\"]=c.exports},ab13:function(t,e,a){var r=a(\"b622\"),i=r(\"match\");t.exports=function(t){var e=/./;try{\"/./\"[t](e)}catch(a){try{return e[i]=!1,\"/./\"[t](e)}catch(r){}}return!1}},bae8:function(t,e,a){\"use strict\";a.d(e,\"b\",(function(){return i})),a.d(e,\"a\",(function(){return n}));var r=a(\"ee97\");function i(t){return Object(r[\"a\"])({url:\"/api/ieg/v1/login/login_in\",method:\"post\",data:t})}function n(){return Object(r[\"a\"])({url:\"/api/ieg/v1/system/info\",method:\"get\"})}},d2c9:function(t,e,a){\"use strict\";a.d(e,\"a\",(function(){return o}));a(\"d3b7\"),a(\"25f0\"),a(\"96cf\");var r=a(\"c964\"),i=a(\"bae8\"),n=6e5;function s(){var t=localStorage.getItem(\"hg_token_timestamp\");if(!t)return!1;var e=(new Date).getTime(),a=parseInt(t),r=e-a;return r<n}function o(){return c.apply(this,arguments)}function c(){return c=Object(r[\"a\"])(regeneratorRuntime.mark((function t(){var e,a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=localStorage.getItem(\"hg_token\"),!e){t.next=7;break}if(a=s(),!a){t.next=6;break}return console.log(\"本地token有效，无需重新登录\"),t.abrupt(\"return\",e);case 6:console.log(\"本地token已过期，需要重新登录\");case 7:return t.prev=7,t.next=10,Object(i[\"b\"])({name:\"admin\",password:\"123456\"});case 10:if(r=t.sent,!(r&&0===r.code&&r.data&&r.data.token)){t.next=18;break}return console.log(\"隐式登录成功，获取到新token\"),localStorage.setItem(\"hg_token\",r.data.token),localStorage.setItem(\"hg_token_timestamp\",(new Date).getTime().toString()),t.abrupt(\"return\",r.data.token);case 18:return console.error(\"隐式登录失败:\",r),t.abrupt(\"return\",\"\");case 20:t.next=26;break;case 22:return t.prev=22,t.t0=t[\"catch\"](7),console.error(\"隐式登录出错:\",t.t0),t.abrupt(\"return\",\"\");case 26:case\"end\":return t.stop()}}),t,null,[[7,22]])}))),c.apply(this,arguments)}},d81d:function(t,e,a){\"use strict\";var r=a(\"23e7\"),i=a(\"b727\").map,n=a(\"1dde\"),s=a(\"ae40\"),o=n(\"map\"),c=s(\"map\");r({target:\"Array\",proto:!0,forced:!o||!c},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},ee97:function(t,e,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var r=a(\"bc3a\"),i=a.n(r),n=a(\"4360\"),s=a(\"a18c\"),o=a(\"a47e\"),c=a(\"f7b5\"),u=a(\"f907\"),d=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),d=r.NODE_ENV,l=r.VUE_APP_IS_MOCK,p=r.VUE_APP_BASE_API,h=\"true\"===l?\"\":p;\"production\"===d&&(h=\"\");var m={baseURL:h,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===d&&(m.timeout=a),e){case\"upload\":m.headers[\"Content-Type\"]=\"multipart/form-data\",m[\"processData\"]=!1,m[\"contentType\"]=!1;break;case\"download\":m[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var g=i.a.create(m);return g.interceptors.request.use((function(t){var e=n[\"a\"].getters.token;if(\"\"!==e&&t.url.startsWith(\"/api/ieg/\")){var a=localStorage.getItem(\"hg_token\");a&&(t.headers[\"authtoken\"]=a)}return t}),(function(t){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:t,print:!0}),Promise.reject(\"response-err:\"+t)})),g.interceptors.response.use((function(t){var a=void 0===t.headers[\"code\"]?200:Number(t.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){s[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(n[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"}))}))},i=function(){var e=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,i=\"\";return(500===t.data.code||t.data.code>=1e3&&t.data.code<2e3)&&(i=\"error\"),t.data.code>=2e3&&t.data.code<3e3&&(i=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(e),type:i}),Promise.reject(\"response-err-status:\".concat(r||u[\"a\"][a][e],\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.\".concat(a,\".\").concat(e))))};switch(t.data.code){case u[\"a\"].exception.system:e(\"system\");break;case u[\"a\"].exception.server:e(\"server\");break;case u[\"a\"].exception.session:r();break;case u[\"a\"].exception.access:r();break;case u[\"a\"].exception.certification:e(\"certification\");break;case u[\"a\"].exception.auth:e(\"auth\"),s[\"a\"].replace({path:\"/401\"});break;case u[\"a\"].exception.token:e(\"token\");break;case u[\"a\"].exception.param:e(\"param\");break;case u[\"a\"].exception.idempotency:e(\"idempotency\");break;case u[\"a\"].exception.ip:e(\"ip\"),n[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"});break;case u[\"a\"].exception.upload:e(\"upload\");break;case u[\"a\"].attack.xss:e(\"xss\",\"attack\");break;default:e(\"code\",\"exception\",-1);break}};switch(e){case\"upload\":if(0===a)return t.data.data;i();break;case\"download\":if(0===a)return{data:t.data,fileName:decodeURI(t.headers[\"file-name\"])};i();break;default:if(0===t.data.code)return t.data;i();break}}),(function(t){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){s[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(n[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===e?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),t.response&&403==t.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(o[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),t.response&&403==t.response.status&&a(),Promise.reject(\"response-err-status:\".concat(t,\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.service.timeout\"))))})),g(t)};e[\"a\"]=d},f077:function(t,e,a){\"use strict\";var r=a(\"440d\"),i=a.n(r);i.a}}]);", "extractedComments": []}