import request from '@util/requestForPy'

// 策略采集-查询
export async function tacticsSearch(params) {
  return request({
    url: '/home_dev/collection_tactics/search',
    method: 'post',
    data: params,
  })
}

// 策略采集-删除
export async function tacticsDelete(params) {
  return request({
    url: '/home_dev/collection_tactics/delete',
    method: 'post',
    data: params,
  })
}

// 策略采集-下发
export async function tacticsDistrib(params) {
  return request({
    url: '/home_dev/collection_tactics/distrib',
    method: 'post',
    data: params,
  })
}
