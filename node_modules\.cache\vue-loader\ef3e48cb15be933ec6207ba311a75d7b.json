{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\DeviceList\\index.vue", "mtime": 1750322914782}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0RGF0YSwKICBnZXRVc2VyLAogIGFkZFVzZXIsCiAgbW9kaWZ5VXNlciwKICBkZWxldGVVc2VyLAogIGxvZ2luRm9yTG9nLAogIGRldmljZVBpbmcsCiAgRGVsZXRlRGF0YSwKICBhZGRFcXVpcG1lbnQsCiAgZWRpdEVxdWlwbWVudCwKICBnZXRUb3BvRGF0YSwKICBzZXRUb3BvRGF0YSwKICBzZWFyY2hHcm91cCwKfSBmcm9tICdAYXBpL2F1ZGl0b2xkL2RldmljZUxpc3QnCmltcG9ydCBBZGREZXZpY2VNb2RhbCBmcm9tICcuL2NvbXBvbmVudHMvQWRkRGV2aWNlTW9kYWwnCmltcG9ydCBFZGl0Q29uZmlnTW9kYWwgZnJvbSAnLi9jb21wb25lbnRzL0VkaXRDb25maWdNb2RhbCcKaW1wb3J0IEVjaGFydHNUZXN0IGZyb20gJy4uL2hpZ2hjaGFydHMvY29tbWVuJwppbXBvcnQgb25saW5lSWNvbiBmcm9tICdAL2Fzc2V0cy9JY29uRm9udC9vbmxpbmUucG5nJwppbXBvcnQgdW5vbmxpbmVJY29uIGZyb20gJ0AvYXNzZXRzL0ljb25Gb250L3Vub25saW5lLnBuZycKaW1wb3J0IENyeXB0b0pTIGZyb20gJ2NyeXB0by1qcycKCi8vIOWuoeiuoeinkuiJsuaYoOWwhOWFs+ezuyAxPT7nrqHnkIblkZggMj0+5pON5L2c5ZGYIDM9PuWuoeiuoeWRmApjb25zdCBhdWRpdFJvbGVNYXAgPSB7CiAgbWFuYWdlOiAxLAogIG9wZXJhdG9yOiAyLAogIGF1ZGl0OiAzLAp9CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RldmljZUxpc3QnLAogIGNvbXBvbmVudHM6IHsKICAgIEFkZERldmljZU1vZGFsLAogICAgRWRpdENvbmZpZ01vZGFsLAogICAgRWNoYXJ0c1Rlc3QsCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlS2V5OiAnMCcsCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIHBhbmVzOiBbXSwKICAgICAgdGltZXI6IG51bGwsCiAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICBjdXJyZW50UGFnZVNpemU6IDEwLAogICAgICBjdXJyZW50VXNlclBhZ2U6IDEsCiAgICAgIGN1cnJlbnRVc2VyUGFnZVNpemU6IDEwLAogICAgICB1c2VyVG90YWw6IDAsCiAgICAgIHNlbGVjdGVkUm93S2V5czogW10sCiAgICAgIHNlbGVjdGVkRGV2TGlzdDogW10sCiAgICAgIHVzZXJTZWxlY3RlZFJvd0tleXM6IFtdLAogICAgICBzZWFyY2hGb3JtOiB7CiAgICAgICAgZmlyZU5hbWU6ICcnLAogICAgICAgIGlwOiAnJywKICAgICAgICBvbmxpblN0YXR1czogJycsCiAgICAgIH0sCiAgICAgIHNlYXJjaFZhbHVlOiB7fSwKICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgIHJlbWFya1Zpc2libGU6IGZhbHNlLAogICAgICB1c2VyVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnREYXRhX2lkOiBudWxsLAogICAgICBjdXJyZW50RGV2aWNlSWQ6IDAsCiAgICAgIGF1ZGl0UHdkTGlzdDogW10sCiAgICAgIGlzTWFudWFsRXJyOiBmYWxzZSwgLy8g5piv5ZCm5omL5bel55m75b2V6ZSZ6K+v77yM6ZSZ6K+v5YiZ5LiN5L+d5a2Y55So5oi35ZCN5a+G56CBCiAgICAgIGlzTG9ja2VkOiBmYWxzZSwgLy8g5piv5ZCm6ZSB5a6a77yM5aaC5p6c6ZSB5a6a6Ieq5Yqo5ZKM5omL5Yqo6YO95Lya6YCa55+l77yM5omL5Yqo6KGo56S65a+G56CB6ZSZ6K+v77yM6Ieq5Yqo6KGo56S66KaB5YGc5q2i6Ieq5Yqo55m75b2VCiAgICAgIGlmcmFtZVNob3c6IGZhbHNlLAogICAgICBjdXJyZW50Q29uZmlnOiBudWxsLAogICAgICBjdXJEZXZVc2VyTGlzdDogW10sCiAgICAgIHRvcG9EYXRhOiB7fSwgLy8g5a2Y5YKodG9wb+aVsOaNrgogICAgICBncm91cERhdGE6IFtdLCAvLyDorr7lpIfliIbnu4QKICAgICAgYXVkaXREYXRhOiBbXSwKICAgICAgYXVkaXRUb3RhbDogMCwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIG9ubGluZUljb24sCiAgICAgIHVub25saW5lSWNvbiwKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldERldmljZUxpc3QoKQogICAgdGhpcy5nZXRUb3BvRGF0YUZ1bmMoKQogICAgdGhpcy5nZXRHcm91cExpc3QoKQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLnJlY2VpdmVNZXNzYWdlRnJvbUluZGV4LCBmYWxzZSkKCiAgICAvLyDorr7nva7lrprml7blmagKICAgIHRoaXMudGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB0aGlzLmdldERldmljZUxpc3QoKSwgMzAwMDApCgogICAgLy8g6I635Y+W57yT5a2Y5omT5byA6K6+5aSH5qCH562+6aG1CiAgICBsZXQgcGFuZXMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXVkaXRwYW5lcycpCiAgICBsZXQgYXVkaXRVc2VyUHdkTGlzdCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdWRpdFVzZXJQd2RMaXN0JykKICAgIGlmIChwYW5lcykgewogICAgICBwYW5lcyA9IEpTT04ucGFyc2UocGFuZXMpCiAgICAgIHRoaXMucGFuZXMgPSBwYW5lcwogICAgfQogICAgaWYgKGF1ZGl0VXNlclB3ZExpc3QpIHsKICAgICAgYXVkaXRVc2VyUHdkTGlzdCA9IEpTT04ucGFyc2UoYXVkaXRVc2VyUHdkTGlzdCkKICAgICAgdGhpcy5hdWRpdFB3ZExpc3QgPSBhdWRpdFVzZXJQd2RMaXN0CiAgICB9CiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g56e76Zmk5LqL5Lu255uR5ZCsCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIHRoaXMucmVjZWl2ZU1lc3NhZ2VGcm9tSW5kZXgpCiAgICAvLyDmuIXnqbrlrprml7blmagKICAgIGlmICh0aGlzLnRpbWVyKSB7CiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lcikKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIHRvZ2dsZVNob3coKSB7CiAgICAgIHRoaXMuaXNTaG93ID0gIXRoaXMuaXNTaG93CiAgICB9LAoKICAgIC8vIOiuvuWkh+WIhue7hAogICAgZ2V0R3JvdXBMaXN0KGNhbGxiYWNrKSB7CiAgICAgIHNlYXJjaEdyb3VwKCkKICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgdGhpcy5ncm91cERhdGEgPSByZXMuZGF0YQogICAgICAgICAgICBpZiAoY2FsbGJhY2spIGNhbGxiYWNrKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bliIbnu4TliJfooajlpLHotKU6JywgZXJyb3IpCiAgICAgICAgfSkKICAgIH0sCgogICAgLy8g5o6l5Y+X5a2Q6aG16Z2icG9zdE1lc3NhZ2Xmtojmga/nm5HlkKzmlrnms5UKICAgIHJlY2VpdmVNZXNzYWdlRnJvbUluZGV4KGV2ZW50KSB7CiAgICAgIGxldCB7IGF1ZGl0UHdkTGlzdCwgaXNMb2NrZWQsIGN1ckRldlVzZXJMaXN0IH0gPSB0aGlzCiAgICAgIGlmIChldmVudCAhPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgY29uc3QgeyBzdGF0dXMgfSA9IGV2ZW50LmRhdGEKICAgICAgICBsZXQgYXVkaXRQd2RPYmogPSB7fQogICAgICAgIGlmIChzdGF0dXMgIT09ICdzdWNjZXNzJykgewogICAgICAgICAgaWYgKGV2ZW50LmRhdGEgPT09ICdsb2FkU3VjY2VzcycpIHsKICAgICAgICAgICAgLy8g6K6+5aSH55m75b2V6aG15Yqg6L295oiQ5Yqf6Ieq5Yqo55m75b2VCiAgICAgICAgICAgIC8vIOiOt+WPluWvueW6lGlw5Zyo5pWw57uE5Lit55qE5L2N572uCiAgICAgICAgICAgIGxldCByZXNJbmRleCA9IGF1ZGl0UHdkTGlzdC5maW5kSW5kZXgoCiAgICAgICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uaXAgPT09IGV2ZW50Lm9yaWdpbi5zdWJzdHJpbmcoOCkKICAgICAgICAgICAgKQogICAgICAgICAgICBpZiAocmVzSW5kZXggIT09IC0xICYmICFldmVudC5kYXRhWyd0eXBlJ10pIHsKICAgICAgICAgICAgICB0aGlzLmhhbmRsZUF1dG9Mb2dpbigKICAgICAgICAgICAgICAgIGV2ZW50Lm9yaWdpbi5zdWJzdHJpbmcoOCksCiAgICAgICAgICAgICAgICBhdWRpdFB3ZExpc3RbcmVzSW5kZXhdWydVc2VyTmFtZVB3ZCddCiAgICAgICAgICAgICAgKQogICAgICAgICAgICB9CiAgICAgICAgICB9IGVsc2UgaWYgKGV2ZW50LmRhdGEudHlwZSAmJiBldmVudC5kYXRhLnN0YXRlID09PSAnZmFpbCcpIHsKICAgICAgICAgICAgLy8g5aSE55CG55m75b2V5aSx6LSl5oOF5Ya1CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDmiYvlt6XnmbvlvZXorr7lpIforrDlvZXkv53lrZjnlKjmiLfkv6Hmga8KICAgICAgICAgICAgbGV0IGlzQWRkID0gYXVkaXRQd2RMaXN0LnNvbWUoKGl0ZW0pID0+IGl0ZW0uaXAgPT09IGV2ZW50Lm9yaWdpbi5zdWJzdHJpbmcoOCkpCiAgICAgICAgICAgIGlmICghaXNMb2NrZWQpIHsKICAgICAgICAgICAgICBpZiAoIWlzQWRkKSB7CiAgICAgICAgICAgICAgICBhdWRpdFB3ZE9ialsnaXAnXSA9IGV2ZW50Lm9yaWdpbi5zdWJzdHJpbmcoOCkKICAgICAgICAgICAgICAgIGF1ZGl0UHdkT2JqWydVc2VyTmFtZVB3ZCddID0gZXZlbnQuZGF0YQogICAgICAgICAgICAgICAgYXVkaXRQd2RMaXN0LnB1c2goYXVkaXRQd2RPYmopCiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIC8vIOiOt+WPluWvueW6lGlw5Zyo5pWw57uE5Lit55qE5L2N572uCiAgICAgICAgICAgICAgICBsZXQgcmVzSW5kZXggPSBhdWRpdFB3ZExpc3QuZmluZEluZGV4KAogICAgICAgICAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5pcCA9PT0gZXZlbnQub3JpZ2luLnN1YnN0cmluZyg4KQogICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgYXVkaXRQd2RMaXN0W3Jlc0luZGV4XVsnVXNlck5hbWVQd2QnXSA9IGV2ZW50LmRhdGEKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOe8k+WtmOWuoeiuoeWvhueggeWIl+ihqOeKtuaAgQogICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhdWRpdFVzZXJQd2RMaXN0JywgSlNPTi5zdHJpbmdpZnkoYXVkaXRQd2RMaXN0KSkKICAgICAgICAgICAgICB0aGlzLmF1ZGl0UHdkTGlzdCA9IGF1ZGl0UHdkTGlzdAogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOiuvuWkh+eZu+W9leaIkOWKn+a3u+WKoOinkuiJsuS/oeaBr+WIsOeUqOaIt+S/oeaBrwogICAgICAgICAgbGV0IHJlc0luZGV4ID0gYXVkaXRQd2RMaXN0LmZpbmRJbmRleCgKICAgICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uaXAgPT09IGV2ZW50Lm9yaWdpbi5zdWJzdHJpbmcoOCkKICAgICAgICAgICkKICAgICAgICAgIGxldCB7IGFjY291bnRJZCB9ID0gYXVkaXRQd2RMaXN0W3Jlc0luZGV4XQogICAgICAgICAgLy8g6I635Y+W56ys5LqM5qyh6K+35rGC5oiQ5Yqf55m76ZmG6KeS6Imy5ZCNCiAgICAgICAgICBpZiAoYXVkaXRQd2RMaXN0W3Jlc0luZGV4XSkgewogICAgICAgICAgICBhdWRpdFB3ZExpc3RbcmVzSW5kZXhdWydyb2xlTmFtZSddID0gZXZlbnQuZGF0YS5yb2xlTmFtZQogICAgICAgICAgICB0aGlzLmF1ZGl0UHdkTGlzdCA9IGF1ZGl0UHdkTGlzdAogICAgICAgICAgICB0aGlzLmlzTWFudWFsRXJyID0gZmFsc2UKICAgICAgICAgICAgdGhpcy5pc0xvY2tlZCA9IGZhbHNlCiAgICAgICAgICAgIHRoaXMuZ2V0SWZyYW1lRGF0YShldmVudC5kYXRhLnJvbGVOYW1lLCBldmVudC5vcmlnaW4uc3Vic3RyaW5nKDgpKQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOaYr+WQpuaYr+esrOS6jOasoeeZu+W9lSDmmK/mn6Xmib5pZOS4jeaYr+S4uuWOn2lkCiAgICAgICAgICBsZXQgYWNjb3VudCA9IGN1ckRldlVzZXJMaXN0LmZpbmQoCiAgICAgICAgICAgIChpdGVtKSA9PiBpdGVtLmFjY291bnRfbmFtZSA9PT0gZXZlbnQuZGF0YS51c2VyTmFtZQogICAgICAgICAgKQoKICAgICAgICAgIGlmIChhY2NvdW50KSB7CiAgICAgICAgICAgIGFjY291bnRJZCA9IGFjY291bnQuaWQKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDosIPnlKjnmbvlvZXml6Xlv5fmjqXlj6PorrDlvZXml6Xlv5cKICAgICAgICAgIGlmIChhY2NvdW50SWQpIHsKICAgICAgICAgICAgdGhpcy5sb2dpblN1Y2Nlc3NTYXZlTG9nKGFjY291bnRJZCkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLy8g6Ieq5Yqo55m75b2V5oiQ5Yqf5Y+R6YCB6K+35rGC5L+d5a2Y5pel5b+XCiAgICBsb2dpblN1Y2Nlc3NTYXZlTG9nKGFjY291bnRfaWQpIHsKICAgICAgbG9naW5Gb3JMb2coeyBhY2NvdW50X2lkIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgY29uc29sZS5sb2coJ+eZu+W9leaXpeW/l+S/neWtmOaIkOWKnycpCiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfnmbvlvZXml6Xlv5fkv53lrZjlpLHotKU6JywgZXJyb3IpCiAgICAgICAgfSkKICAgIH0sCgogICAgLy8g5aSE55CG6Ieq5Yqo55m75b2V6YC76L6RCiAgICBoYW5kbGVBdXRvTG9naW4oZGV2aWNlSXAsIHRva2VuRGF0YSkgewogICAgICBjb25zdCBjaGlsZEZyYW1lT2JqID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnaWZyYW1lJykKICAgICAgY2hpbGRGcmFtZU9iai5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgIGxldCBzcmMgPSBpdGVtLmdldEF0dHJpYnV0ZSgnc3JjJykKICAgICAgICBsZXQgaXAgPSBzcmMuc3Vic3RyaW5nKDgpCiAgICAgICAgaWYgKGRldmljZUlwID09PSBpcCkgewogICAgICAgICAgY29uc3QgdG9rZW5BcnIgPSB0b2tlbkRhdGEuc3BsaXQoJzonKQogICAgICAgICAgaXRlbS5jb250ZW50V2luZG93LnBvc3RNZXNzYWdlKAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgdXNlcm5hbWU6IHRva2VuQXJyWzBdLAogICAgICAgICAgICAgIHB3ZDogdG9rZW5BcnJbMV0sCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGBodHRwczovLyR7ZGV2aWNlSXB9YAogICAgICAgICAgKQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g5bCB6KOF55m75b2V6K6+5aSH5oiQ5Yqf5aSE55CG6YC76L6RCiAgICBnZXRJZnJhbWVEYXRhKHJvbGVOYW1lLCBkZXZpY2VJcCkgewogICAgICBjb25zdCBhdWRpdFVzZXJQd2RMaXN0ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1ZGl0VXNlclB3ZExpc3QnKQogICAgICBpZiAoYXVkaXRVc2VyUHdkTGlzdCkgewogICAgICAgIGNvbnN0IHJlc0luZGV4ID0gSlNPTi5wYXJzZShhdWRpdFVzZXJQd2RMaXN0KS5maW5kSW5kZXgoCiAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5pcCA9PT0gZGV2aWNlSXAKICAgICAgICApCiAgICAgICAgbGV0IGxvZ2luVmFsdWUgPSBDcnlwdG9KUy5lbmMuQmFzZTY0LnN0cmluZ2lmeSgKICAgICAgICAgIENyeXB0b0pTLmVuYy5VdGY4LnBhcnNlKEpTT04ucGFyc2UoYXVkaXRVc2VyUHdkTGlzdClbcmVzSW5kZXhdWydVc2VyTmFtZVB3ZCddKQogICAgICAgICkKICAgICAgICBjb25zdCB7IGN1cnJlbnREZXZpY2VJZCB9ID0gdGhpcwogICAgICAgIGlmIChjdXJyZW50RGV2aWNlSWQgIT09IDApIHsKICAgICAgICAgIGFkZFVzZXIoewogICAgICAgICAgICBhdXRoOiBsb2dpblZhbHVlLAogICAgICAgICAgICBkZXZpY2VfaWQ6IGN1cnJlbnREZXZpY2VJZCwKICAgICAgICAgICAgYWNjb3VudF9yb2xlOiBhdWRpdFJvbGVNYXBbcm9sZU5hbWVdLAogICAgICAgICAgfSkKICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+a3u+WKoOeUqOaIt+aIkOWKnycpCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5re75Yqg55So5oi35aSx6LSlOicsIGVycm9yKQogICAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDojrflj5borr7lpIfliJfooajmlbDmja4KICAgIGdldERldmljZUxpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgY29uc3QgeyBjdXJyZW50UGFnZVNpemUsIGN1cnJlbnRQYWdlLCBzZWFyY2hWYWx1ZSB9ID0gdGhpcwogICAgICBnZXREYXRhKHsKICAgICAgICBfbGltaXQ6IGN1cnJlbnRQYWdlU2l6ZSwKICAgICAgICBfcGFnZTogY3VycmVudFBhZ2UsCiAgICAgICAgcXVlcnlQYXJhbXM6IHNlYXJjaFZhbHVlLAogICAgICAgIHR5cGU6IDIsCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgIC8vIOa3u+WKoOW6j+WPtwogICAgICAgICAgICBjb25zdCBhdWRpdERhdGEgPSByZXMuZGF0YS5pdGVtcyB8fCBbXQogICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGF1ZGl0RGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgIGF1ZGl0RGF0YVtpXSA9IHsKICAgICAgICAgICAgICAgIG51bWJlcjogKGN1cnJlbnRQYWdlIC0gMSkgKiBjdXJyZW50UGFnZVNpemUgKyBpICsgMSwKICAgICAgICAgICAgICAgIC4uLmF1ZGl0RGF0YVtpXSwKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5hdWRpdERhdGEgPSBhdWRpdERhdGEKICAgICAgICAgICAgdGhpcy5hdWRpdFRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMAogICAgICAgICAgICB0aGlzLnNlbGVjdGVkUm93S2V5cyA9IFtdCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlKQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6K6+5aSH5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICB9KQogICAgfSwKCiAgICAvLyDojrflj5bnlKjmiLfliJfooajmlbDmja4KICAgIGdldFVzZXJMaXN0RGF0YShzZWxmY2FsbGJhY2spIHsKICAgICAgY29uc3QgeyBjdXJyZW50VXNlclBhZ2UsIGN1cnJlbnRVc2VyUGFnZVNpemUsIGN1cnJlbnREZXZpY2VJZCB9ID0gdGhpcwogICAgICBnZXRVc2VyKHsKICAgICAgICBkZXZpY2VfaWQ6IGN1cnJlbnREZXZpY2VJZCwKICAgICAgICBwYWdlOiBjdXJyZW50VXNlclBhZ2UsCiAgICAgICAgcGVyX3BhZ2U6IGN1cnJlbnRVc2VyUGFnZVNpemUsCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgIGlmIChyZXMuZGF0YS50b3RhbCkgewogICAgICAgICAgICAgIHRoaXMudXNlclRvdGFsID0gcmVzLmRhdGEudG90YWwKICAgICAgICAgICAgICB0aGlzLmN1ckRldlVzZXJMaXN0ID0gcmVzLmRhdGEuaXRlbXMKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgaWYgKHNlbGZjYWxsYmFjaykgc2VsZmNhbGxiYWNrKHJlcykKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgICB9KQogICAgfSwKCiAgICAvLyDorr7lpIfpgInmi6nmlLnlj5jkuovku7YKICAgIG9uU2VsZWN0Q2hhbmdlKHNlbGVjdGVkUm93cykgewogICAgICB0aGlzLnNlbGVjdGVkUm93S2V5cyA9IHNlbGVjdGVkUm93cy5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2VsZWN0ZWREZXZMaXN0ID0gc2VsZWN0ZWRSb3dzLm1hcCgoaXRlbSkgPT4gaXRlbS5pcCkKICAgIH0sCgogICAgLy8g55So5oi36YCJ5oup5pS55Y+Y5LqL5Lu25aSE55CGCiAgICBvblVzZXJTZWxlY3RDaGFuZ2Uoc2VsZWN0ZWRSb3dzKSB7CiAgICAgIHRoaXMudXNlclNlbGVjdGVkUm93S2V5cyA9IHNlbGVjdGVkUm93cy5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpCiAgICB9LAoKICAgIC8vIOiuvuWkh+WIl+ihqOWIhumhteWkp+Wwj+aUueWPmOS6i+S7tgogICAgb25TaG93U2l6ZUNoYW5nZShwYWdlU2l6ZSwgY3VycmVudCkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gY3VycmVudAogICAgICB0aGlzLmN1cnJlbnRQYWdlU2l6ZSA9IHBhZ2VTaXplCiAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpCiAgICB9LAoKICAgIC8vIOeUqOaIt+WIl+ihqOWIhumhteWkp+Wwj+aUueWPmOS6i+S7tgogICAgb25Vc2VyU2hvd1NpemVDaGFuZ2UocGFnZVNpemUsIGN1cnJlbnQpIHsKICAgICAgdGhpcy5jdXJyZW50VXNlclBhZ2UgPSBjdXJyZW50CiAgICAgIHRoaXMuY3VycmVudFVzZXJQYWdlU2l6ZSA9IHBhZ2VTaXplCiAgICAgIHRoaXMuZ2V0VXNlckxpc3REYXRhKCkKICAgIH0sCgogICAgLy8g6aG16Z2i5pS55Y+Y5aSE55CG5LqL5Lu2CiAgICBoYW5kbGVQYWdlQ2hhbmdlKHBhZ2VOdW1iZXIpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IHBhZ2VOdW1iZXIKICAgICAgdGhpcy5nZXREZXZpY2VMaXN0KCkKICAgIH0sCgogICAgLy8g55So5oi35YiX6KGo5YiG6aG15pS55Y+Y5LqL5Lu25aSE55CGCiAgICBoYW5kbGVVc2VyUGFnZUNoYW5nZShwYWdlTnVtYmVyKSB7CiAgICAgIHRoaXMuY3VycmVudFVzZXJQYWdlID0gcGFnZU51bWJlcgogICAgICB0aGlzLmdldFVzZXJMaXN0RGF0YSgpCiAgICB9LAoKICAgIC8vIOaMieadoeS7tuafpeivouiuvuWkhwogICAgaGFuZGxlQWR2YW5jZWRTZWFyY2goKSB7CiAgICAgIGxldCBzZWFyY2hWYWx1ZSA9IHt9CiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uZmlyZU5hbWUpIHNlYXJjaFZhbHVlLmZpcmVOYW1lID0gdGhpcy5zZWFyY2hGb3JtLmZpcmVOYW1lCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uaXApIHNlYXJjaFZhbHVlLm9yaWdpbklwID0gdGhpcy5zZWFyY2hGb3JtLmlwCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0ub25saW5TdGF0dXMpIHNlYXJjaFZhbHVlLm9ubGluU3RhdHVzID0gdGhpcy5zZWFyY2hGb3JtLm9ubGluU3RhdHVzCiAgICAgIHRoaXMuc2VhcmNoVmFsdWUgPSBzZWFyY2hWYWx1ZQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMQogICAgICB0aGlzLmdldERldmljZUxpc3QoKQogICAgfSwKCiAgICAvLyDph43nva7mkJzntKLmnaHku7YKICAgIGhhbmRsZVJlc2V0KCkgewogICAgICB0aGlzLnNlYXJjaEZvcm0gPSB7CiAgICAgICAgZmlyZU5hbWU6ICcnLAogICAgICAgIGlwOiAnJywKICAgICAgICBvbmxpblN0YXR1czogJycsCiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2hWYWx1ZSA9IHt9CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxCiAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpCiAgICB9LAoKICAgIC8vIOeCueWHu+a3u+WKoOaMiemSruaYvuekuuaooeaAgeahhumAu+i+kQogICAgaGFuZGxlQWRkQ2xpY2soKSB7CiAgICAgIHRoaXMuJHJlZnMuYWRkTW9kYWwub25TaG93KCkKICAgIH0sCgogICAgLy8g5p+l55yL6K6+5aSH5LqL5Lu25aSE55CGCiAgICBoYW5kbGVMb29rKHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLnN0YXR1cyA9PT0gMSkgewogICAgICAgIHdpbmRvdy5vcGVuKGBodHRwczovLyR7cmVjb3JkLmlwfS8jL2xvZ2luP3R5cGU9c21wYCkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCforr7lpIfkuI3lnKjnur/vvIzml6Dms5Xmn6XnnIshJykKICAgICAgfQogICAgfSwKCiAgICAvLyBQaW5nCiAgICBoYW5kbGVQaW5nKHJlY29yZCkgewogICAgICBkZXZpY2VQaW5nKHsgaXA6IHJlY29yZC5pcCB9KQogICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICBpZiAocmVzLmRhdGEgPT09IDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ1BpbmfmiJDlip8nKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOS4jemAmicpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdQaW5n5aSx6LSlOicsIGVycm9yKQogICAgICAgIH0pCiAgICB9LAoKICAgIC8vIOagueaNruiuvuWkh2lk6I635Y+W6K6+5aSH55So5oi35YiX6KGoCiAgICBnZXRVc2VyTGlzdChkZXZpY2VJZCwgZGV2aWNlSXApIHsKICAgICAgbGV0IHsgYXVkaXRQd2RMaXN0IH0gPSB0aGlzCiAgICAgIGdldFVzZXIoewogICAgICAgIGRldmljZV9pZDogZGV2aWNlSWQsCiAgICAgICAgcGFnZTogMSwKICAgICAgICBwZXJfcGFnZTogMTAsCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5pdGVtcyAmJiByZXMuZGF0YS5pdGVtcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgbGV0IHsgZGVmYXVsdF9hY2NvdW50X2F1dGggfSA9IHJlcy5kYXRhCiAgICAgICAgICAgICAgaWYgKGRlZmF1bHRfYWNjb3VudF9hdXRoICYmIGRlZmF1bHRfYWNjb3VudF9hdXRoLmF1dGgpIHsKICAgICAgICAgICAgICAgIGRlZmF1bHRfYWNjb3VudF9hdXRoLmF1dGggPSBDcnlwdG9KUy5lbmMuQmFzZTY0LnBhcnNlKAogICAgICAgICAgICAgICAgICBkZWZhdWx0X2FjY291bnRfYXV0aC5hdXRoCiAgICAgICAgICAgICAgICApLnRvU3RyaW5nKENyeXB0b0pTLmVuYy5VdGY4KQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyDojrflj5blr7nlupRpcOWcqOaVsOe7hOS4reeahOS9jee9rgogICAgICAgICAgICAgIGxldCByZXNJbmRleCA9IGF1ZGl0UHdkTGlzdC5maW5kSW5kZXgoKGl0ZW0pID0+IGl0ZW0uaXAgPT09IGRldmljZUlwKQogICAgICAgICAgICAgIGlmIChyZXNJbmRleCA9PT0gLTEpIHsKICAgICAgICAgICAgICAgIGxldCBhdWRpdFB3ZE9iaiA9IHt9CiAgICAgICAgICAgICAgICBhdWRpdFB3ZE9ialsnaXAnXSA9IGRldmljZUlwCiAgICAgICAgICAgICAgICBhdWRpdFB3ZE9ialsnVXNlck5hbWVQd2QnXSA9IGRlZmF1bHRfYWNjb3VudF9hdXRoLmF1dGgKICAgICAgICAgICAgICAgIGF1ZGl0UHdkT2JqWydhY2NvdW50SWQnXSA9IGRlZmF1bHRfYWNjb3VudF9hdXRoLmFjY291bnRfaWQKICAgICAgICAgICAgICAgIGF1ZGl0UHdkTGlzdC5wdXNoKGF1ZGl0UHdkT2JqKQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBpZiAoZGVmYXVsdF9hY2NvdW50X2F1dGgpIHsKICAgICAgICAgICAgICAgICAgYXVkaXRQd2RMaXN0W3Jlc0luZGV4XVsnVXNlck5hbWVQd2QnXSA9IGRlZmF1bHRfYWNjb3VudF9hdXRoLmF1dGgKICAgICAgICAgICAgICAgICAgYXVkaXRQd2RMaXN0W3Jlc0luZGV4XVsnYWNjb3VudElkJ10gPSBkZWZhdWx0X2FjY291bnRfYXV0aC5hY2NvdW50X2lkCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRoaXMuYXVkaXRQd2RMaXN0ID0gYXVkaXRQd2RMaXN0CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfliJfooajlpLHotKU6JywgZXJyb3IpCiAgICAgICAgfSkKICAgIH0sCgogICAgLy8g5Y+W5raI5oyJ6ZKu5LqL5Lu25aSE55CGCiAgICBoYW5kbGVDYW5jZWxDbGljaygpIHsKICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5yZW1hcmtWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy51c2VyVmlzaWJsZSA9IGZhbHNlCiAgICB9LAoKICAgIC8vIOS/ruaUuem7mOiupOeZu+W9lei0puWPt+ivt+axggogICAgbW9kaWZ5RGVmTG9naW5BY2NvdW50KHBhcmFtcykgewogICAgICBtb2RpZnlVc2VyKHBhcmFtcykKICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjpu5jorqTnmbvlvZXnlKjmiLfphY3nva7miJDlip8nKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/ruaUuem7mOiupOeZu+W9lei0puWPt+Wksei0pTonLCBlcnJvcikKICAgICAgICB9KQogICAgfSwKCiAgICAvLyDkv53lrZjpu5jorqTnmbvlvZXnlKjmiLfphY3nva7mjInpkq7kuovku7blpITnkIYKICAgIGhhbmRsZU9rVXNlcigpIHsKICAgICAgY29uc3QgeyBjdXJyZW50RGV2aWNlSWQsIHVzZXJTZWxlY3RlZFJvd0tleXMgfSA9IHRoaXMKICAgICAgY29uc3QgYWNjb3VudElkID0gdXNlclNlbGVjdGVkUm93S2V5c1swXQogICAgICBpZiAoYWNjb3VudElkKSB7CiAgICAgICAgdGhpcy5tb2RpZnlEZWZMb2dpbkFjY291bnQoewogICAgICAgICAgZGV2aWNlSWQ6IGN1cnJlbnREZXZpY2VJZCwKICAgICAgICAgIGFjY291bnRJZCwKICAgICAgICB9KQogICAgICB9CiAgICAgIHRoaXMudmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMucmVtYXJrVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMudXNlclZpc2libGUgPSBmYWxzZQogICAgfSwKCiAgICAvLyDphY3nva7mqKHmgIHmoYbmmL7npLrpgLvovpEKICAgIGhhbmRsZVNob3dDb25maWcocmVjb3JkKSB7CiAgICAgIHRoaXMuY3VycmVudERhdGFfaWQgPSByZWNvcmQuaWQKICAgICAgdGhpcy5jdXJyZW50Q29uZmlnID0gcmVjb3JkCiAgICAgIHRoaXMuJHJlZnMuY29uZmlnTW9kYWwub25TaG93KCkKICAgIH0sCgogICAgLy8g5aSE55CG55So5oi35oyJ6ZKu54K55Ye75LqL5Lu2CiAgICBoYW5kbGVVc2VyKHJlY29yZCkgewogICAgICB0aGlzLnVzZXJWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmN1cnJlbnREZXZpY2VJZCA9IHJlY29yZC5pZAogICAgICB0aGlzLmdldFVzZXJMaXN0RGF0YSgocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhICYmIHJlcy5kYXRhLmRlZmF1bHRfYWNjb3VudF9hdXRoKSB7CiAgICAgICAgICBjb25zdCB7IGFjY291bnRfaWQgfSA9IHJlcy5kYXRhLmRlZmF1bHRfYWNjb3VudF9hdXRoCiAgICAgICAgICB0aGlzLnVzZXJTZWxlY3RlZFJvd0tleXMgPSBbYWNjb3VudF9pZF0KICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWIoOmZpOeUqOaIt+aMiemSrueCueWHu+S6i+S7tuWkhOeQhgogICAgaGFuZGxlRGVsZXRlVXNlcihyZWNvcmQpIHsKICAgICAgbGV0IHsgYXVkaXRQd2RMaXN0LCB1c2VyU2VsZWN0ZWRSb3dLZXlzIH0gPSB0aGlzCiAgICAgIGRlbGV0ZVVzZXIoeyBhY2NvdW50X2lkOiBOdW1iZXIocmVjb3JkLmlkKSB9KQogICAgICAgIC50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpCiAgICAgICAgICAgIGF1ZGl0UHdkTGlzdCA9IGF1ZGl0UHdkTGlzdC5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uYWNjb3VudElkICE9PSByZWNvcmQuaWQpCiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhdWRpdFVzZXJQd2RMaXN0JywgSlNPTi5zdHJpbmdpZnkoYXVkaXRQd2RMaXN0KSkKICAgICAgICAgICAgbGV0IGFjY291bnRJZCA9IHVzZXJTZWxlY3RlZFJvd0tleXMgPyB1c2VyU2VsZWN0ZWRSb3dLZXlzWzBdIDogbnVsbAogICAgICAgICAgICB1c2VyU2VsZWN0ZWRSb3dLZXlzID0KICAgICAgICAgICAgICBhY2NvdW50SWQgJiYgYWNjb3VudElkID09PSByZWNvcmQuaWQgPyBbXSA6IHVzZXJTZWxlY3RlZFJvd0tleXMKICAgICAgICAgICAgdGhpcy5hdWRpdFB3ZExpc3QgPSBhdWRpdFB3ZExpc3QKICAgICAgICAgICAgdGhpcy51c2VyU2VsZWN0ZWRSb3dLZXlzID0gdXNlclNlbGVjdGVkUm93S2V5cwogICAgICAgICAgICB0aGlzLmdldFVzZXJMaXN0RGF0YSgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk55So5oi35aSx6LSlOicsIGVycm9yKQogICAgICAgIH0pCiAgICB9LAoKICAgIC8vIHRhYuagh+etvumhteWIh+aNouS6i+S7tgogICAgb25DaGFuZ2UodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlS2V5ID0gdGFiLm5hbWUKICAgIH0sCgogICAgLy8g5qCH562+6aG157yW6L6R5LqL5Lu2CiAgICBvbkVkaXQodGFyZ2V0S2V5LCBhY3Rpb24pIHsKICAgICAgaWYgKGFjdGlvbiA9PT0gJ3JlbW92ZScpIHsKICAgICAgICBsZXQgeyBwYW5lcywgYXVkaXRQd2RMaXN0IH0gPSB0aGlzCiAgICAgICAgcGFuZXMgPSBwYW5lcy5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0ua2V5ICE9PSB0YXJnZXRLZXkpCiAgICAgICAgYXVkaXRQd2RMaXN0ID0gYXVkaXRQd2RMaXN0LmZpbHRlcigoaXRlbSkgPT4gaXRlbS5pcCAhPT0gdGFyZ2V0S2V5KQogICAgICAgIHRoaXMuaGFuZGxlQXV0b0xvZ2luT3V0KHRhcmdldEtleSkKICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYXVkaXRVc2VyUHdkTGlzdCcsIEpTT04uc3RyaW5naWZ5KGF1ZGl0UHdkTGlzdCkpCiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1ZGl0cGFuZXMnLCBKU09OLnN0cmluZ2lmeShwYW5lcykpCgogICAgICAgIHRoaXMucGFuZXMgPSBwYW5lcwogICAgICAgIHRoaXMuYXVkaXRQd2RMaXN0ID0gYXVkaXRQd2RMaXN0CiAgICAgICAgdGhpcy5yZW1vdmUodGFyZ2V0S2V5KQogICAgICB9CiAgICB9LAoKICAgIC8vIOWkhOeQhuiHquWKqOeZu+WHuumAu+i+kQogICAgaGFuZGxlQXV0b0xvZ2luT3V0KGRldmljZUlwKSB7CiAgICAgIGNvbnN0IGNoaWxkRnJhbWVPYmogPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdpZnJhbWUnKQogICAgICBjaGlsZEZyYW1lT2JqLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgbGV0IHNyYyA9IGl0ZW0uZ2V0QXR0cmlidXRlKCdzcmMnKQogICAgICAgIGxldCBpcCA9IHNyYy5zdWJzdHJpbmcoOCkKICAgICAgICBpZiAoZGV2aWNlSXAgPT09IGlwKSB7CiAgICAgICAgICBpdGVtLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoJ2xvZ291dCcsICcqJykKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOenu+mZpOagh+etvumhtQogICAgcmVtb3ZlKHRhcmdldEtleSkgewogICAgICBsZXQgYWN0aXZlS2V5ID0gdGhpcy5hY3RpdmVLZXkKICAgICAgbGV0IGxhc3RJbmRleAogICAgICB0aGlzLnBhbmVzLmZvckVhY2goKHBhbmUsIGkpID0+IHsKICAgICAgICBpZiAocGFuZS5rZXkgPT09IHRhcmdldEtleSkgewogICAgICAgICAgbGFzdEluZGV4ID0gaSAtIDEKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGNvbnN0IHBhbmVzID0gdGhpcy5wYW5lcy5maWx0ZXIoKHBhbmUpID0+IHBhbmUua2V5ICE9PSB0YXJnZXRLZXkpCiAgICAgIGlmIChwYW5lcy5sZW5ndGggJiYgYWN0aXZlS2V5ID09PSB0YXJnZXRLZXkpIHsKICAgICAgICBpZiAobGFzdEluZGV4ID49IDApIHsKICAgICAgICAgIGFjdGl2ZUtleSA9IHBhbmVzW2xhc3RJbmRleF0ua2V5CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGFjdGl2ZUtleSA9IHBhbmVzWzBdLmtleQogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnBhbmVzID0gcGFuZXMKICAgICAgdGhpcy5hY3RpdmVLZXkgPSB0aGlzLnBhbmVzLmxlbmd0aCA9PT0gMSA/ICcwJyA6IGFjdGl2ZUtleQogICAgfSwKCiAgICAvLyDorr7lpIfphY3nva7mj5DkuqTkuovku7blpITnkIYKICAgIGhhbmRsZUNvbmlmZ1NhdmUoZm9ybSkgewogICAgICBjb25zdCB7IGN1cnJlbnREYXRhX2lkLCBjdXJyZW50Q29uZmlnIH0gPSB0aGlzCiAgICAgIGlmICgKICAgICAgICBjdXJyZW50Q29uZmlnLmltcG9ydGFuY2UgIT09IGZvcm0uaW1wb3J0YW5jZSB8fAogICAgICAgIGN1cnJlbnRDb25maWcucG9zaXRpb24gIT09IGZvcm0ucG9zaXRpb24gfHwKICAgICAgICBjdXJyZW50Q29uZmlnLnBlcnNvbl9saWFibGUgIT09IGZvcm0ucGVyc29uX2xpYWJsZSB8fAogICAgICAgIGN1cnJlbnRDb25maWcuY29udGFjdCAhPT0gZm9ybS5jb250YWN0IHx8CiAgICAgICAgY3VycmVudENvbmZpZy5ncm91cF9pZCAhPT0gZm9ybS5ncm91cF9pZAogICAgICApIHsKICAgICAgICBjb25zdCBwYXlsb2FkID0gewogICAgICAgICAgZGV2aWNlX2lkOiBjdXJyZW50RGF0YV9pZCwKICAgICAgICAgIGltcG9ydGFuY2U6IHBhcnNlSW50KGZvcm0uaW1wb3J0YW5jZSksCiAgICAgICAgICBncm91cF9pZDogZm9ybS5ncm91cF9pZCA9PT0gdW5kZWZpbmVkID8gJycgOiBmb3JtLmdyb3VwX2lkLAogICAgICAgICAgcG9zaXRpb246IGZvcm0ucG9zaXRpb24gPT09IHVuZGVmaW5lZCA/ICcnIDogZm9ybS5wb3NpdGlvbiwKICAgICAgICAgIHBlcnNvbl9saWFibGU6IGZvcm0ucGVyc29uX2xpYWJsZSA9PT0gdW5kZWZpbmVkID8gJycgOiBmb3JtLnBlcnNvbl9saWFibGUsCiAgICAgICAgICBjb250YWN0OiBmb3JtLmNvbnRhY3QgPT09IHVuZGVmaW5lZCA/ICcnIDogZm9ybS5jb250YWN0LAogICAgICAgIH0KICAgICAgICBlZGl0RXF1aXBtZW50KHBheWxvYWQpCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn57yW6L6R5oiQ5YqfJykKICAgICAgICAgICAgICB0aGlzLnJlbWFya1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSkKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLiRyZWZzLmNvbmZpZ01vZGFsLm9uSGlkZSgpCiAgICAgICAgICB9KQogICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfnvJbovpHorr7lpIflpLHotKU6JywgZXJyb3IpCiAgICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mFjee9ruacquabtOaUue+8gScpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5om56YeP5Yig6Zmk6K6+5aSHCiAgICBoYW5kbGVEZWxldGVDbGljayhyZWNvcmQpIHsKICAgICAgbGV0IHNlbGVjdGVkUm93S2V5cyA9IFtdCiAgICAgIGxldCBzZWxlY3RlZERldkxpc3QgPSBbXQogICAgICBpZiAocmVjb3JkICYmIHJlY29yZC5pZCkgewogICAgICAgIHNlbGVjdGVkUm93S2V5cy5wdXNoKHJlY29yZC5pZCkKICAgICAgICBzZWxlY3RlZERldkxpc3QucHVzaChyZWNvcmQuaXApCiAgICAgIH0gZWxzZSB7CiAgICAgICAgc2VsZWN0ZWRSb3dLZXlzID0gdGhpcy5zZWxlY3RlZFJvd0tleXMKICAgICAgICBzZWxlY3RlZERldkxpc3QgPSB0aGlzLnNlbGVjdGVkRGV2TGlzdAogICAgICB9CiAgICAgIGlmIChzZWxlY3RlZFJvd0tleXMubGVuZ3RoKSB7CiAgICAgICAgdGhpcy4kY29uZmlybSgKICAgICAgICAgIGDnoa7orqTliKDpmaTov5kke3NlbGVjdGVkUm93S2V5cy5sZW5ndGh95p2h5pWw5o2u5ZCX77yfYCwKICAgICAgICAgICfliKDpmaQnLAogICAgICAgICAgewogICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruiupCcsCiAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICB9CiAgICAgICAgKQogICAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgICBEZWxldGVEYXRhKHsgZGV2aWNlX2lkczogc2VsZWN0ZWRSb3dLZXlzIH0pCiAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpCiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTorr7lpIflpLHotKU6JywgZXJyb3IpCiAgICAgICAgICAgICAgfSkKICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICBjb25zb2xlLmxvZygn5Y+W5raI5Yig6ZmkJykKICAgICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6Iez5bCR6YCJ5Lit5LiA5p2h5pWw5o2uJykKICAgICAgfQogICAgfSwKCiAgICAvLyDlpITnkIbmt7vliqDorr7lpIfnoa7lrprkuovku7YKICAgIGhhbmRsZUFkZERldmljZShmb3JtKSB7CiAgICAgIGNvbnN0IHBheWxvYWQgPSB7CiAgICAgICAgLi4uZm9ybSwKICAgICAgICBjYXRlZ29yeTogMiwKICAgICAgfQogICAgICBhZGRFcXVpcG1lbnQocGF5bG9hZCkKICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlrDlop7miJDlip8nKQogICAgICAgICAgICB0aGlzLmdldERldmljZUxpc3QoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a3u+WKoOiuvuWkh+Wksei0pTonLCBlcnJvcikKICAgICAgICB9KQogICAgfSwKCiAgICAvLyDojrflj5bmi5PmiZHmlbDmja4KICAgIGdldFRvcG9EYXRhRnVuYyhjYWxsYmFjaykgewogICAgICBnZXRUb3BvRGF0YSgpCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMudG9wb0RhdGEgPSByZXMuZGF0YQogICAgICAgICAgICBpZiAoY2FsbGJhY2spIGNhbGxiYWNrKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmi5PmiZHmlbDmja7lpLHotKU6JywgZXJyb3IpCiAgICAgICAgfSkKICAgIH0sCgogICAgLy8g55Sf5oiQ6K6+5aSH6IqC54K5CiAgICBnZW5lcmF0ZU5vZGUob3B0aW9ucykgewogICAgICByZXR1cm4gewogICAgICAgIGlkOiB0aGlzLmd1aWQoKSwKICAgICAgICB0eXBlOiAnbm9kZScsCiAgICAgICAgc2l6ZTogJzUwJywKICAgICAgICBzaGFwZTogJ2tvbmktY3VzdG9tLW5vZGUnLAogICAgICAgIGNvbG9yOiAnIzY5QzBGRicsCiAgICAgICAgbGFiZWxPZmZzZXRZOiAzOCwKICAgICAgICAuLi5vcHRpb25zLAogICAgICB9CiAgICB9LAoKICAgIC8vIOeUn+aIkEdVSUQKICAgIGd1aWQoKSB7CiAgICAgIHJldHVybiAneHh4eHh4eHgteHh4eC00eHh4LXl4eHgteHh4eHh4eHh4eHh4Jy5yZXBsYWNlKC9beHldL2csIGZ1bmN0aW9uIChjKSB7CiAgICAgICAgdmFyIHIgPSAoTWF0aC5yYW5kb20oKSAqIDE2KSB8IDAsCiAgICAgICAgICB2ID0gYyA9PT0gJ3gnID8gciA6IChyICYgMHgzKSB8IDB4OAogICAgICAgIHJldHVybiB2LnRvU3RyaW5nKDE2KQogICAgICB9KQogICAgfSwKCiAgICAvLyDosIPnlKjkv53lrZjmi5PmiZHmlbDmja7mjqXlj6MKICAgIHNhdmVUb3BvRGF0YSh0b3BvRGF0YSkgewogICAgICBzZXRUb3BvRGF0YSh7IHRvcG9sb2d5X3RleHQ6IHRvcG9EYXRhIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuZ2V0RGV2aWNlTGlzdCgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5ouT5omR5pWw5o2u5aSx6LSlOicsIGVycm9yKQogICAgICAgIH0pCiAgICB9LAogIH0sCn0K"}, null]}