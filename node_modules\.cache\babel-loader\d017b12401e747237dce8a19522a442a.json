{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyRecord\\index.vue", "mtime": 1750149146356}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbiI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIiOwppbXBvcnQgInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwppbXBvcnQgeyBnZXRTdHJhdGVneVJlY29yZExpc3QgYXMgX2dldFN0cmF0ZWd5UmVjb3JkTGlzdCwgZGVsZXRlU3RyYXRlZ3lSZWNvcmQgfSBmcm9tICdAL2FwaS9maXJld2FsbC9zdHJhdGVneVJlY29yZCc7CmltcG9ydCBkYXlqcyBmcm9tICdkYXlqcyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3RyYXRlZ3lSZWNvcmQnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcXVlcnlJbnB1dDogewogICAgICAgIG5hbWU6ICcnLAogICAgICAgIHJlY29yZFRpbWU6IG51bGwsCiAgICAgICAgb3BlcmF0ZVR5cGU6ICcnLAogICAgICAgIHN0YXR1czogJycKICAgICAgfSwKICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgc2VsZWN0ZWRSb3dzOiBbXSwKICAgICAgcGFnaW5hdGlvbjogewogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0KICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXRTdHJhdGVneVJlY29yZExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHRvZ2dsZVNob3c6IGZ1bmN0aW9uIHRvZ2dsZVNob3coKSB7CiAgICAgIHRoaXMuaXNTaG93ID0gIXRoaXMuaXNTaG93OwogICAgfSwKICAgIGdldFN0cmF0ZWd5UmVjb3JkTGlzdDogZnVuY3Rpb24gZ2V0U3RyYXRlZ3lSZWNvcmRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcGF5bG9hZCwgcmVzOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgcGF5bG9hZCA9IF9vYmplY3RTcHJlYWQoewogICAgICAgICAgICAgICAgICBwYWdlSW5kZXg6IF90aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UsCiAgICAgICAgICAgICAgICAgIHBhZ2VTaXplOiBfdGhpcy5wYWdpbmF0aW9uLnBhZ2VTaXplCiAgICAgICAgICAgICAgICB9LCBfdGhpcy5idWlsZFF1ZXJ5UGFyYW1zKCkpOwogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDI7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNTsKICAgICAgICAgICAgICAgIHJldHVybiBfZ2V0U3RyYXRlZ3lSZWNvcmRMaXN0KHBheWxvYWQpOwoKICAgICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dC5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXMucmV0Y29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICAvLyDmoLnmja7ljp/lp4tSZWFjdOeJiOacrO+8jOaVsOaNruebtOaOpeWcqHJlcy5kYXRh5Lit77yM5L2G6ZyA6KaB5qOA5p+l5piv5ZCm5pyJ5YiG6aG157uT5p6ECiAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlcy5kYXRhKSkgewogICAgICAgICAgICAgICAgICAgIC8vIOWmguaenGRhdGHmmK/mlbDnu4TvvIznm7TmjqXkvb/nlKgKICAgICAgICAgICAgICAgICAgICBfdGhpcy50YWJsZURhdGEgPSByZXMuZGF0YTsKICAgICAgICAgICAgICAgICAgICBfdGhpcy5wYWdpbmF0aW9uLnRvdGFsID0gcmVzLmRhdGEubGVuZ3RoOwogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlcy5kYXRhICYmIHJlcy5kYXRhLnJvd3MpIHsKICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpxkYXRh5pyJcm93c+WxnuaAp++8jOS9v+eUqHJvd3Plkox0b3RhbAogICAgICAgICAgICAgICAgICAgIF90aGlzLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJvd3MgfHwgW107CiAgICAgICAgICAgICAgICAgICAgX3RoaXMucGFnaW5hdGlvbi50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgLy8g5YWc5bqV5aSE55CGCiAgICAgICAgICAgICAgICAgICAgX3RoaXMudGFibGVEYXRhID0gW107CiAgICAgICAgICAgICAgICAgICAgX3RoaXMucGFnaW5hdGlvbi50b3RhbCA9IDA7CiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIF90aGlzLnNlbGVjdGVkUm93cyA9IFtdOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEyOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSA5OwogICAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgyKTsKCiAgICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W562W55Wl6K6w5b2V5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMTI7CiAgICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuZmluaXNoKDEyKTsKCiAgICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbMiwgOSwgMTIsIDE1XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBidWlsZFF1ZXJ5UGFyYW1zOiBmdW5jdGlvbiBidWlsZFF1ZXJ5UGFyYW1zKCkgewogICAgICB2YXIgcGFyYW1zID0ge307CiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQubmFtZSkgcGFyYW1zLm5hbWUgPSB0aGlzLnF1ZXJ5SW5wdXQubmFtZTsKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5vcGVyYXRlVHlwZSAhPT0gJycpIHBhcmFtcy5vcGVyYXRlVHlwZSA9IHRoaXMucXVlcnlJbnB1dC5vcGVyYXRlVHlwZTsKICAgICAgaWYgKHRoaXMucXVlcnlJbnB1dC5zdGF0dXMgIT09ICcnKSBwYXJhbXMuc3RhdHVzID0gdGhpcy5xdWVyeUlucHV0LnN0YXR1czsKCiAgICAgIGlmICh0aGlzLnF1ZXJ5SW5wdXQucmVjb3JkVGltZSAmJiB0aGlzLnF1ZXJ5SW5wdXQucmVjb3JkVGltZS5sZW5ndGggPiAwKSB7CiAgICAgICAgcGFyYW1zLmJlZ2luRGF0ZSA9IHRoaXMucXVlcnlJbnB1dC5yZWNvcmRUaW1lWzBdICsgJyAwMDowMDowMCc7CiAgICAgICAgcGFyYW1zLmVuZERhdGUgPSB0aGlzLnF1ZXJ5SW5wdXQucmVjb3JkVGltZVsxXSArICcgMjM6NTk6NTknOwogICAgICB9CgogICAgICByZXR1cm4gcGFyYW1zOwogICAgfSwKICAgIGhhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5wYWdpbmF0aW9uLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5nZXRTdHJhdGVneVJlY29yZExpc3QoKTsKICAgIH0sCiAgICBoYW5kbGVSZXNldDogZnVuY3Rpb24gaGFuZGxlUmVzZXQoKSB7CiAgICAgIHRoaXMucXVlcnlJbnB1dCA9IHsKICAgICAgICBuYW1lOiAnJywKICAgICAgICByZWNvcmRUaW1lOiBudWxsLAogICAgICAgIG9wZXJhdGVUeXBlOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH07CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBoYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyZWNvcmQpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTpgInkuK3ljY/orq7orrDlvZXlkJc/5Yig6Zmk5ZCO5LiN5Y+v5oGi5aSNJywgJ+WIoOmZpCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruiupCcsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oIC8qI19fUFVSRV9fKi9fYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciByZXM7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMDsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIHJldHVybiBkZWxldGVTdHJhdGVneVJlY29yZCh7CiAgICAgICAgICAgICAgICAgIGlkczogcmVjb3JkLmlkCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQyLnNlbnQ7CgogICAgICAgICAgICAgICAgaWYgKHJlcy5yZXRjb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgIF90aGlzMi5nZXRTdHJhdGVneVJlY29yZExpc3QoKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gNzsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi50MCA9IF9jb250ZXh0MlsiY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGhhbmRsZUJhdGNoRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVCYXRjaERlbGV0ZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6Iez5bCR6YCJ5Lit5LiA5p2h5pWw5o2uJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKCJcdTc4NkVcdTVCOUFcdTg5ODFcdTUyMjBcdTk2NjRcdTkwMDlcdTRFMkRcdTdCNTZcdTc1NjVcdThCQjBcdTVGNTVcdTU0MTc/XHU1MjIwXHU5NjY0XHU1NDBFXHU0RTBEXHU1M0VGXHU2MDYyXHU1OTBEIiwgJ+WIoOmZpCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruiupCcsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oIC8qI19fUFVSRV9fKi9fYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciByZWNvcmRJZHMsIHJlczsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSAwOwogICAgICAgICAgICAgICAgcmVjb3JkSWRzID0gX3RoaXMzLnNlbGVjdGVkUm93cy5tYXAoZnVuY3Rpb24gKHJvdykgewogICAgICAgICAgICAgICAgICByZXR1cm4gcm93LmlkOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gZGVsZXRlU3RyYXRlZ3lSZWNvcmQoewogICAgICAgICAgICAgICAgICBpZHM6IHJlY29yZElkcy5qb2luKCcsJykKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDMuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzLnJldGNvZGUgPT09IDApIHsKICAgICAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwoKICAgICAgICAgICAgICAgICAgX3RoaXMzLmdldFN0cmF0ZWd5UmVjb3JkTGlzdCgpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSA4OwogICAgICAgICAgICAgICAgX2NvbnRleHQzLnQwID0gX2NvbnRleHQzWyJjYXRjaCJdKDApOwoKICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMsIG51bGwsIFtbMCwgOF1dKTsKICAgICAgfSkpKS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uOwogICAgfSwKICAgIGhhbmRsZVNpemVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNpemVDaGFuZ2Uoc2l6ZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24ucGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLmdldFN0cmF0ZWd5UmVjb3JkTGlzdCgpOwogICAgfSwKICAgIGhhbmRsZVBhZ2VDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVBhZ2VDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UgPSBwYWdlOwogICAgICB0aGlzLmdldFN0cmF0ZWd5UmVjb3JkTGlzdCgpOwogICAgfSwKICAgIGZvcm1hdFRpbWU6IGZ1bmN0aW9uIGZvcm1hdFRpbWUodGltZSkgewogICAgICBpZiAodGltZSA9PT0gJy0nIHx8ICF0aW1lKSB7CiAgICAgICAgcmV0dXJuIHRpbWU7CiAgICAgIH0KCiAgICAgIHJldHVybiBkYXlqcyh0aW1lKS5mb3JtYXQoJ1lZWVktTU0tREQgSEg6bW06c3MnKTsKICAgIH0sCiAgICBnZXRTdGF0dXNUZXh0OiBmdW5jdGlvbiBnZXRTdGF0dXNUZXh0KHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLnN0YXR1cyA9PSAiMCIgJiYgcmVjb3JkLm9wZXJhdGVUeXBlID09ICIwIikgewogICAgICAgIHJldHVybiAi5LiL5Y+R5aSx6LSlIjsKICAgICAgfSBlbHNlIGlmIChyZWNvcmQuc3RhdHVzID09ICIxIiAmJiByZWNvcmQub3BlcmF0ZVR5cGUgPT0gIjAiKSB7CiAgICAgICAgcmV0dXJuICLkuIvlj5HmiJDlip8iOwogICAgICB9IGVsc2UgaWYgKHJlY29yZC5zdGF0dXMgPT0gIjAiICYmIHJlY29yZC5vcGVyYXRlVHlwZSA9PSAiMSIpIHsKICAgICAgICByZXR1cm4gIuWQjOatpeWksei0pSI7CiAgICAgIH0gZWxzZSBpZiAocmVjb3JkLnN0YXR1cyA9PSAiMSIgJiYgcmVjb3JkLm9wZXJhdGVUeXBlID09ICIxIikgewogICAgICAgIHJldHVybiAi5ZCM5q2l5oiQ5YqfIjsKICAgICAgfQoKICAgICAgcmV0dXJuICcnOwogICAgfQogIH0KfTs="}, null]}