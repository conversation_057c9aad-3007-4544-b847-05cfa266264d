{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\src\\api\\sentine\\authManagement.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\api\\sentine\\authManagement.js", "mtime": 1749783509012}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQHV0aWwvcmVxdWVzdEZvclB5JzsKLyoqCiAqIOWTqOWFteeuoeeQhi3mjojmnYPnrqHnkIbmn6Xor6IKICogQHBhcmFtIHBhcmFtcwogKiBAcmV0dXJucyB7UHJvbWlzZX0KICovCgpleHBvcnQgZnVuY3Rpb24gZ2V0QXV0aG9yaXphdGlvbkxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2hvbWVfZGV2L3NlbnRpbmVsX2xpY2Vuc2UvbGlzdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IHBhcmFtcyB8fCB7fQogIH0pOwp9Ci8qKgogKiDmlrDlop7mjojmnYMKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2V9CiAqLwoKZXhwb3J0IGZ1bmN0aW9uIGFkZEF1dGhvcml6YXRpb24oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9ob21lX2Rldi9zZW50aW5lbF9saWNlbnNlL2FkZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEgfHwge30KICB9KTsKfQovKioKICog5pu05paw5o6I5p2DCiAqIEBwYXJhbSBkYXRhCiAqIEByZXR1cm5zIHtQcm9taXNlfQogKi8KCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVBdXRob3JpemF0aW9uKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvaG9tZV9kZXYvc2VudGluZWxfbGljZW5zZS91cGRhdGUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhIHx8IHt9CiAgfSk7Cn0KLyoqCiAqIOWIoOmZpOaOiOadgwogKiBAcGFyYW0gZGF0YQogKiBAcmV0dXJucyB7UHJvbWlzZX0KICovCgpleHBvcnQgZnVuY3Rpb24gZGVsZXRlQXV0aG9yaXphdGlvbihkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2hvbWVfZGV2L3NlbnRpbmVsX2xpY2Vuc2UvZGVsZXRlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YSB8fCB7fQogIH0pOwp9Ci8qKgogKiDlkIzmraXorr7lpIfmjojmnYMKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2V9CiAqLwoKZXhwb3J0IGZ1bmN0aW9uIHN5bmNEZXZpY2VBdXRoKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvaG9tZV9kZXYvc2VudGluZWxfbGljZW5zZS9zeW5jJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YSB8fCB7fQogIH0pOwp9Ci8qKgogKiDkuIrkvKDmjojmnYPmlofku7YKICogQHBhcmFtIGZvcm1EYXRhCiAqIEByZXR1cm5zIHtQcm9taXNlfQogKi8KCmV4cG9ydCBmdW5jdGlvbiB1cGxvYWRBdXRoRmlsZShmb3JtRGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9ob21lX2Rldi9zZW50aW5lbF9saWNlbnNlL3VwbG9hZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGZvcm1EYXRhCiAgfSk7Cn0="}, null]}