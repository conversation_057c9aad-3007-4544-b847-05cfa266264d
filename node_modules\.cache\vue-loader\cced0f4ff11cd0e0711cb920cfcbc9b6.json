{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\visualization\\dashboard\\TheDashboardDataCompDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\visualization\\dashboard\\TheDashboardDataCompDialog.vue", "mtime": 1721181012096}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBDb25kaXRpb25TZXR0aW5nQ29tcCBmcm9tICcuL1RoZUNvbmRpdGlvblNldHRpbmdDb21wJwppbXBvcnQgQ3VzdG9tRGlhbG9nIGZyb20gJ0Bjb21wL0N1c3RvbURpYWxvZycKaW1wb3J0IExpbmVDaGFydCBmcm9tICdAY29tcC9DaGFydEZhY3RvcnkvZGFzaGJvYXJkL0xpbmVDaGFydCcKaW1wb3J0IFBpZUNoYXJ0IGZyb20gJ0Bjb21wL0NoYXJ0RmFjdG9yeS9kYXNoYm9hcmQvUGllQ2hhcnQnCmltcG9ydCBCYXJDaGFydCBmcm9tICdAY29tcC9DaGFydEZhY3RvcnkvZGFzaGJvYXJkL0JhckNoYXJ0JwppbXBvcnQgeyBjaGFydCB9IGZyb20gJ0Bhc3NldC9qcy9jb2RlL29wdGlvbicKaW1wb3J0IHsgcHJvbXB0IH0gZnJvbSAnQHV0aWwvcHJvbXB0JwppbXBvcnQgeyBpc0VtcHR5IH0gZnJvbSAnQHV0aWwvY29tbW9uJwppbXBvcnQgeyBxdWVyeUNoYXJ0RGF0YSB9IGZyb20gJ0BhcGkvdmlzdWFsaXphdGlvbi9kYXNoYm9hcmQtYXBpJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEN1c3RvbURpYWxvZywKICAgIExpbmVDaGFydCwKICAgIFBpZUNoYXJ0LAogICAgQmFyQ2hhcnQsCiAgICBDb25kaXRpb25TZXR0aW5nQ29tcCwKICB9LAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICB0eXBlOiBCb29sZWFuLAogICAgfSwKICAgIHRpdGxlOiB7CiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICB0eXBlOiBTdHJpbmcsCiAgICB9LAogICAgd2lkdGg6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnNjAwJywKICAgIH0sCiAgICB0eXBlRGF0YTogewogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgdHlwZTogQXJyYXksCiAgICB9LAogICAgY2h1bmtEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge30KICAgICAgfSwKICAgIH0sCiAgICBkYXNoYm9hcmRJZDogewogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgdHlwZTogU3RyaW5nLAogICAgfSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiB0aGlzLnZpc2libGUsCiAgICAgIGRhdGE6IHsKICAgICAgICB0eXBlOiB0aGlzLnR5cGVEYXRhLAogICAgICAgIGFjdGl2ZTogewogICAgICAgICAgdHlwZTogW10sIC8vIOW9k+WJjemAieS4reWkp+exu+eahOWwj+exuwogICAgICAgICAgY2hhcnQ6IHt9LCAvLyDlvZPliY3pgInkuK3nmoTlsI/nsbsKICAgICAgICAgIGNoYXJ0RGF0YToge30sIC8vIOW9k+WJjemAieS4reWwj+exu+eahOWbvuihqOeahOafpeivouaVsOaNrgogICAgICAgICAgY29uZGl0aW9uOiB7fSwgLy8g6L+H5ruk5p2h5Lu2CiAgICAgICAgfSwKICAgICAgfSwKICAgICAgZm9ybTogewogICAgICAgIG1vZGVsOiB7CiAgICAgICAgICB0eXBlOiAnJywKICAgICAgICB9LAogICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICB0eXBlOiBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgICBtZXNzYWdlOiB0aGlzLiR0KCd2YWxpZGF0ZS5lbXB0eScpLAogICAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJywKICAgICAgICAgICAgfSwKICAgICAgICAgIF0sCiAgICAgICAgfSwKICAgICAgfSwKICAgICAgY2hhcnQ6IHsKICAgICAgICB0aXRsZTogewogICAgICAgICAgY2hhcnROYW1lOiAnJywKICAgICAgICB9LAogICAgICAgIGxpbmU6IHsKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgIHR5cGU6IDEsCiAgICAgICAgICAgIGF4aXM6IDEsCiAgICAgICAgICAgIGZvcm1hdFR5cGU6IG51bGwsCiAgICAgICAgICAgIHVuaXQ6IG51bGwsCiAgICAgICAgICB9LAogICAgICAgICAgZGF0YTogWwogICAgICAgICAgICBbJyAnLCAnMDA6MDAnLCAnMDE6MDAnLCAnMDI6MDAnXSwKICAgICAgICAgICAgWydhJywgMjAsIDEwLCAzMF0sCiAgICAgICAgICAgIFsnYicsIDI1LCAyMCwgMTVdLAogICAgICAgICAgICBbJ2MnLCAxMCwgMjAsIDI1XSwKICAgICAgICAgIF0sCiAgICAgICAgfSwKICAgICAgICBwaWU6IHsKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgIHR5cGU6IDEsCiAgICAgICAgICB9LAogICAgICAgICAgZGF0YTogWwogICAgICAgICAgICB7IG5hbWU6ICdhJywgdmFsdWU6IDIwIH0sCiAgICAgICAgICAgIHsgbmFtZTogJ2InLCB2YWx1ZTogMTAgfSwKICAgICAgICAgICAgeyBuYW1lOiAnYycsIHZhbHVlOiAzMCB9LAogICAgICAgICAgXSwKICAgICAgICB9LAogICAgICAgIGJhcjogewogICAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICAgdHlwZTogMSwKICAgICAgICAgICAgYXhpczogMSwKICAgICAgICAgICAgZm9ybWF0VHlwZTogbnVsbCwKICAgICAgICAgICAgdW5pdDogbnVsbCwKICAgICAgICAgIH0sCiAgICAgICAgICBkYXRhOiBbCiAgICAgICAgICAgIFsnICcsICcwMDowMCcsICcwMTowMCcsICcwMjowMCddLAogICAgICAgICAgICBbJ2EnLCAyMCwgMTAsIDMwXSwKICAgICAgICAgICAgWydiJywgMjUsIDIwLCAxNV0sCiAgICAgICAgICAgIFsnYycsIDEwLCAyMCwgMjVdLAogICAgICAgICAgXSwKICAgICAgICB9LAogICAgICAgIHRleHQ6IHsKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAzMCwKICAgICAgICAgICAgZm9udENvbG9yOiAnIzAwMCcsCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDQwMCwKICAgICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJywKICAgICAgICAgIH0sCiAgICAgICAgICBkYXRhOiB0aGlzLiR0KCd2aXN1YWxpemF0aW9uLmRhc2hib2FyZC5kaWFsb2cuY29tcC5wbGFjZWhvbGRlcicpLAogICAgICAgIH0sCiAgICAgIH0sCiAgICAgIG9wdGlvbjogewogICAgICAgIGxpbmU6IGNoYXJ0LmxpbmUsCiAgICAgICAgcGllOiBjaGFydC5waWUsCiAgICAgICAgYmFyOiBjaGFydC5iYXIsCiAgICAgICAgYXhpczogY2hhcnQuYXhpcywKICAgICAgICBmb3JtYXRUeXBlOiBjaGFydC5mb3JtYXRUeXBlLAogICAgICAgIHByZWRlZmluZUNvbG9yczogWwogICAgICAgICAgJyNmZmYnLAogICAgICAgICAgJyMwMDAnLAogICAgICAgICAgJyNmZjQ1MDAnLAogICAgICAgICAgJyNmZjhjMDAnLAogICAgICAgICAgJyNmZmQ3MDAnLAogICAgICAgICAgJyM5MGVlOTAnLAogICAgICAgICAgJyMwMGNlZDEnLAogICAgICAgICAgJyMxZTkwZmYnLAogICAgICAgICAgJyNjNzE1ODUnLAogICAgICAgICAgJ3JnYmEoMjU1LCA2OSwgMCwgMC42OCknLAogICAgICAgICAgJ3JnYigyNTUsIDEyMCwgMCknLAogICAgICAgICAgJ2hzdig1MSwgMTAwLCA5OCknLAogICAgICAgICAgJ2hzdmEoMTIwLCA0MCwgOTQsIDAuNSknLAogICAgICAgICAgJ2hzbCgxODEsIDEwMCUsIDM3JSknLAogICAgICAgICAgJ2hzbGEoMjA5LCAxMDAlLCA1NiUsIDAuNzMpJywKICAgICAgICAgICcjYzcxNTg1NzcnLAogICAgICAgIF0sCiAgICAgICAgdGV4dEFsaWduOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCd2aXN1YWxpemF0aW9uLmRhc2hib2FyZC5hbGlnbi5sZWZ0JyksCiAgICAgICAgICAgIHZhbHVlOiAnbGVmdCcsCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogdGhpcy4kdCgndmlzdWFsaXphdGlvbi5kYXNoYm9hcmQuYWxpZ24uY2VudGVyJyksCiAgICAgICAgICAgIHZhbHVlOiAnY2VudGVyJywKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCd2aXN1YWxpemF0aW9uLmRhc2hib2FyZC5hbGlnbi5yaWdodCcpLAogICAgICAgICAgICB2YWx1ZTogJ3JpZ2h0JywKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBmb250V2VpZ2h0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCd2aXN1YWxpemF0aW9uLmRhc2hib2FyZC5mb250V2VpZ2h0LnRoaW4nKSwKICAgICAgICAgICAgdmFsdWU6IDIwMCwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCd2aXN1YWxpemF0aW9uLmRhc2hib2FyZC5mb250V2VpZ2h0Lm5vcm1hbCcpLAogICAgICAgICAgICB2YWx1ZTogNDAwLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6IHRoaXMuJHQoJ3Zpc3VhbGl6YXRpb24uZGFzaGJvYXJkLmZvbnRXZWlnaHQuYm9sZCcpLAogICAgICAgICAgICB2YWx1ZTogNjAwLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBhY3RpdmVOYW1lczogWycxJ10sCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgY29tcFR5cGUoKSB7CiAgICAgIC8vIGNvbnN0IGNvbXBUeXBlRGF0YSA9IFtdOwogICAgICAvLyB0aGlzLmRhdGEudHlwZS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgIC8vICAgICBjb25zdCB2aXNpYmxlID0gW107CiAgICAgIC8vICAgICBpdGVtLmNoYXJ0LmZvckVhY2goY2hhcnQgPT4gewogICAgICAvLyAgICAgICAgIHZpc2libGUucHVzaChjaGFydC52aXNpYmxlKTsKICAgICAgLy8gICAgIH0pOwogICAgICAvLyAgICAgaWYgKHZpc2libGUuZmlsdGVyKGJvb2wgPT4gYm9vbCA9PT0gdHJ1ZSkubGVuZ3RoID4gMCkgewogICAgICAvLyAgICAgICAgIGNvbXBUeXBlRGF0YS5wdXNoKGl0ZW0pOwogICAgICAvLyAgICAgfQogICAgICAvLyB9KTsKICAgICAgLy8gcmV0dXJuIGNvbXBUeXBlRGF0YTsKICAgICAgcmV0dXJuIHRoaXMuZGF0YS50eXBlCiAgICB9LAogICAgbGluZU9wdGlvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBkYXRhOiB0aGlzLmNoYXJ0LmxpbmUuZGF0YSwKICAgICAgICB0eXBlOiB0aGlzLmNoYXJ0LmxpbmUuc3R5bGUudHlwZSwKICAgICAgICBheGlzOiB0aGlzLmNoYXJ0LmxpbmUuc3R5bGUuYXhpcywKICAgICAgfQogICAgfSwKICAgIHBpZU9wdGlvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBkYXRhOiB0aGlzLmNoYXJ0LnBpZS5kYXRhLAogICAgICAgIHR5cGU6IHRoaXMuY2hhcnQucGllLnN0eWxlLnR5cGUsCiAgICAgIH0KICAgIH0sCiAgICBiYXJPcHRpb24oKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgZGF0YTogdGhpcy5jaGFydC5iYXIuZGF0YSwKICAgICAgICB0eXBlOiB0aGlzLmNoYXJ0LmJhci5zdHlsZS50eXBlLAogICAgICAgIGF4aXM6IHRoaXMuY2hhcnQuYmFyLnN0eWxlLmF4aXMsCiAgICAgIH0KICAgIH0sCiAgICBnZXRUZXh0U3R5bGUoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLAogICAgICAgIHdpZHRoOiAnMTAwJScsCiAgICAgICAgaGVpZ2h0OiAnMTAwJScsCiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsCiAgICAgICAgZm9udFNpemU6IHRoaXMuY2hhcnQudGV4dC5zdHlsZS5mb250U2l6ZSArICdweCcsCiAgICAgICAgZm9udFdlaWdodDogdGhpcy5jaGFydC50ZXh0LnN0eWxlLmZvbnRXZWlnaHQsCiAgICAgICAgY29sb3I6IHRoaXMuY2hhcnQudGV4dC5zdHlsZS5mb250Q29sb3IsCiAgICAgICAganVzdGlmeUNvbnRlbnQ6IHRoaXMuY2hhcnQudGV4dC5zdHlsZS50ZXh0QWxpZ24sCiAgICAgIH0KICAgIH0sCiAgfSwKICB3YXRjaDogewogICAgdmlzaWJsZSh2aXNpYmxlKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZpc2libGUKICAgICAgaWYgKHZpc2libGUpIHsKICAgICAgICB0aGlzLmluaXRTZWxlY3RlZENoYXJ0KHRoaXMuY2h1bmtEYXRhKQogICAgICAgIC8vIHRoaXMuZm9ybS5tb2RlbC50eXBlID0gdGhpcy5jb21wVHlwZVswXS52YWx1ZTsKICAgICAgICAvLyB0aGlzLmNoYW5nZURhdGFDb21wKHRoaXMuZm9ybS5tb2RlbC50eXBlKTsKICAgICAgfQogICAgfSwKICAgIGRpYWxvZ1Zpc2libGUodmlzaWJsZSkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZpc2libGUpCiAgICB9LAogICAgdHlwZURhdGE6IHsKICAgICAgaGFuZGxlcih0eXBlKSB7CiAgICAgICAgdGhpcy5kYXRhLnR5cGUgPSB0eXBlCiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICB9LAogICAgJ2RhdGEudHlwZSc6IHsKICAgICAgaGFuZGxlcih0eXBlKSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnR5cGVEYXRhJywgdHlwZSkKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSwKICAgIH0sCiAgICAvLyAiZm9ybS5tb2RlbC50eXBlIjogewogICAgLy8gICAgIGhhbmRsZXIodHlwZSkgewogICAgLy8gICAgICAgICB0aGlzLmRhdGEudHlwZS5tYXAoaXRlbSA9PiB7CiAgICAvLyAgICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gdHlwZSAmJiBpdGVtLmNoYXJ0Lmxlbmd0aCA+IDApIHsKICAgIC8vICAgICAgICAgICAgICAgICBjb25zdCBhY3RpdmVDaGFydCA9IGl0ZW0uY2hhcnQuZmlsdGVyKGNoYXJ0ID0+IGNoYXJ0LnZpc2libGUgPT09IHRydWUpOwogICAgLy8gICAgICAgICAgICAgICAgIGlmIChhY3RpdmVDaGFydC5sZW5ndGggPiAwKSB7CiAgICAvLyAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGF0YS5hY3RpdmUuY2hhcnQgPSBhY3RpdmVDaGFydFswXTsKICAgIC8vICAgICAgICAgICAgICAgICB9CiAgICAvLyAgICAgICAgICAgICB9CiAgICAvLyAgICAgICAgIH0pOwogICAgLy8gICAgICAgICAvLyB0aGlzLmRhdGEuYWN0aXZlLmNoYXJ0ID0ge307CiAgICAvLyAgICAgfSwKICAgIC8vICAgICBpbW1lZGlhdGU6IHRydWUKICAgIC8vIH0sCiAgICAvLyAiZGF0YS5hY3RpdmUuY2hhcnQudHlwZSIoKSB7CiAgICAvLyAgICAgdGhpcy5yZXNldENoYXJ0VHlwZSgpOwogICAgLy8gfQogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2tDYW5jZWxEaWFsb2coKSB7CiAgICAgIHRoaXMuZm9ybS5tb2RlbC50eXBlID0gJycKICAgICAgdGhpcy5kYXRhLmFjdGl2ZS50eXBlID0gW10KICAgICAgdGhpcy5kYXRhLmFjdGl2ZS5jaGFydERhdGEgPSB7fQogICAgICB0aGlzLiRyZWZzLmRpYWxvZy5lbmQoKQogICAgICB0aGlzLmNoYXJ0LmxpbmUuc3R5bGUuZm9ybWF0VHlwZSA9IG51bGwKICAgICAgdGhpcy5jaGFydC5saW5lLnN0eWxlLnVuaXQgPSBudWxsCiAgICAgIHRoaXMuY2hhcnQuYmFyLnN0eWxlLmZvcm1hdFR5cGUgPSBudWxsCiAgICAgIHRoaXMuY2hhcnQuYmFyLnN0eWxlLnVuaXQgPSBudWxsCiAgICAgIHRoaXMuY2hhcnQudGV4dC5zdHlsZS5mb250U2l6ZSA9IDMwCiAgICAgIHRoaXMuY2hhcnQudGV4dC5zdHlsZS5mb250Q29sb3IgPSAnIzAwMCcKICAgICAgdGhpcy5jaGFydC50ZXh0LnN0eWxlLmZvbnRXZWlnaHQgPSA0MDAKICAgICAgdGhpcy5jaGFydC50ZXh0LnN0eWxlLnRleHRBbGlnbiA9ICdjZW50ZXInCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuJHJlZnMuY29uZGl0aW9uUmVmLnJlc2V0Rm9ybSgpCiAgICB9LAogICAgY2xpY2tTdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkICYmIE9iamVjdC5rZXlzKHRoaXMuZGF0YS5hY3RpdmUuY2hhcnQpLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuJGNvbmZpcm0odGhpcy4kdCgndGlwLmNvbmZpcm0uc3VibWl0JyksIHRoaXMuJHQoJ3RpcC5jb25maXJtLnRpcCcpLCB7CiAgICAgICAgICAgIGNsb3NlT25DbGlja01vZGFsOiBmYWxzZSwKICAgICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICBjb25zdCBjb25kaXRpb24gPSB0aGlzLmdldEFjdGl2ZUNoYXJ0Q29uZGl0aW9uKCkKICAgICAgICAgICAgdGhpcy5nZXRDaGFydERhdGEodGhpcy5kYXRhLmFjdGl2ZS5jaGFydC5pZCwgKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuaGFuZGxlU3dpdGNoVHlwZUNoYXJ0KHRoaXMuZGF0YS5hY3RpdmUuY2hhcnQuaWQpCiAgICAgICAgICAgICAgdGhpcy4kZW1pdCgKICAgICAgICAgICAgICAgICdvbi1zdWJtaXQnLAogICAgICAgICAgICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmRhdGEuYWN0aXZlLmNoYXJ0RGF0YSwgewogICAgICAgICAgICAgICAgICBpZDogdGhpcy5kYXRhLmFjdGl2ZS5jaGFydC5pZCwKICAgICAgICAgICAgICAgICAgY2hhcnRJZDogdGhpcy5kYXRhLmFjdGl2ZS5jaGFydC5pZCwKICAgICAgICAgICAgICAgICAgY29uZGl0aW9uOiBjb25kaXRpb24sCiAgICAgICAgICAgICAgICAgIHRpdGxlOiB0aGlzLmNoYXJ0LnRpdGxlLmNoYXJ0TmFtZSwKICAgICAgICAgICAgICAgICAgZGF0YVNldDogdGhpcy5mb3JtLm1vZGVsLnR5cGUsCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICkKICAgICAgICAgICAgICB0aGlzLmNsaWNrQ2FuY2VsRGlhbG9nKCkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHByb21wdCgKICAgICAgICAgICAgewogICAgICAgICAgICAgIGkxOG5Db2RlOiAndmFsaWRhdGUuZm9ybS53YXJuaW5nJywKICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgICAgcHJpbnQ6IHRydWUsCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgICgpID0+IHsKICAgICAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICAgICAgfQogICAgICAgICAgKQogICAgICAgIH0KICAgICAgfSkKICAgICAgdGhpcy4kcmVmcy5kaWFsb2cuZW5kKCkKICAgIH0sCiAgICBpbml0U2VsZWN0ZWRDaGFydChvYmopIHsKICAgICAgaWYgKG9iaiAhPT0gbnVsbCAmJiBvYmouaGFzT3duUHJvcGVydHkoJ2RhdGFTZXQnKSkgewogICAgICAgIHRoaXMuZGF0YS50eXBlLmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09PSBvYmouZGF0YVNldCkgewogICAgICAgICAgICB0aGlzLmZvcm0ubW9kZWwudHlwZSA9IGl0ZW0udmFsdWUKICAgICAgICAgICAgdGhpcy5kYXRhLmFjdGl2ZS50eXBlID0gaXRlbS5jaGFydAogICAgICAgICAgICBjb25zdCBhY3RpdmVDaGFydCA9IGl0ZW0uY2hhcnQuZmlsdGVyKChjaGFydCkgPT4gY2hhcnQuaWQgPT09IG9iai5jaGFydElkKQogICAgICAgICAgICBpZiAoYWN0aXZlQ2hhcnQubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIHRoaXMuY2xpY2tTZWxlY3RlZENoYXJ0KGFjdGl2ZUNoYXJ0WzBdLCBvYmoudGl0bGUpCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5kYXRhLmFjdGl2ZS5jb25kaXRpb24gPSB0aGlzLnJlbmRlckNvbmRpdGlvbihvYmouY29uZGl0aW9uKQogICAgICAgICAgICB0aGlzLmNoYXJ0W29iai50eXBlXVsnc3R5bGUnXSA9IHsKICAgICAgICAgICAgICB0eXBlOiBvYmoub3B0aW9uLnN0eWxlLnR5cGUsCiAgICAgICAgICAgICAgYXhpczogb2JqLm9wdGlvbi5zdHlsZS5heGlzLAogICAgICAgICAgICAgIHVuaXQ6IG9iai5vcHRpb24uc3R5bGUudW5pdCwKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLm1vZGVsLnR5cGUgPSB0aGlzLmRhdGEudHlwZVswXS52YWx1ZQogICAgICAgIHRoaXMuY2hhbmdlRGF0YUNvbXAodGhpcy5mb3JtLm1vZGVsLnR5cGUpCiAgICAgIH0KICAgIH0sCiAgICBjaGFuZ2VEYXRhQ29tcCh2YWx1ZSkgewogICAgICB0aGlzLmRhdGEuYWN0aXZlLmNvbmRpdGlvbiA9IHt9CiAgICAgIHRoaXMuZGF0YS50eXBlLmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gdmFsdWUpIHsKICAgICAgICAgIHRoaXMuZGF0YS5hY3RpdmUudHlwZSA9IGl0ZW0uY2hhcnQKICAgICAgICAgIGlmIChpdGVtLmNoYXJ0Lmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdGhpcy5jbGlja1NlbGVjdGVkQ2hhcnQoaXRlbS5jaGFydFswXSkKICAgICAgICAgIH0KICAgICAgICAgIC8vIGNvbnN0IGFjdGl2ZUNoYXJ0ID0gaXRlbS5jaGFydC5maWx0ZXIoY2hhcnQgPT4gY2hhcnQudmlzaWJsZSA9PT0gdHJ1ZSk7CiAgICAgICAgICAvLyBpZiAoYWN0aXZlQ2hhcnQubGVuZ3RoID4gMCkgewogICAgICAgICAgLy8gICAgIHRoaXMuY2xpY2tTZWxlY3RlZENoYXJ0KGFjdGl2ZUNoYXJ0WzBdKTsKICAgICAgICAgIC8vIH0KICAgICAgICB9CiAgICAgIH0pCiAgICAgIHRoaXMucmVzZXRDaGFydFR5cGUoKQogICAgfSwKICAgIHJlc2V0Q2hhcnRUeXBlKCkgewogICAgICB0aGlzLmNoYXJ0LmJhci5zdHlsZS50eXBlID0gMQogICAgICB0aGlzLmNoYXJ0LmJhci5zdHlsZS5heGlzID0gMQogICAgICB0aGlzLmNoYXJ0LmxpbmUuc3R5bGUudHlwZSA9IDEKICAgICAgdGhpcy5jaGFydC5saW5lLnN0eWxlLmF4aXMgPSAxCiAgICAgIHRoaXMuY2hhcnQucGllLnN0eWxlLnR5cGUgPSAxCiAgICB9LAogICAgY2xpY2tTZWxlY3RlZENoYXJ0KGl0ZW0sIHRpdGxlTmFtZSkgewogICAgICB0aGlzLmRhdGEuYWN0aXZlLmNoYXJ0ID0gaXRlbQogICAgICB0aGlzLmNoYXJ0LnRpdGxlLmNoYXJ0TmFtZSA9IGlzRW1wdHkodGl0bGVOYW1lKSA/IGl0ZW0udGl0bGUgOiB0aXRsZU5hbWUKICAgICAgdGhpcy5yZXNldENoYXJ0VHlwZSgpCiAgICB9LAogICAgcmVuZGVyQ29uZGl0aW9uKGNvbmRpdGlvbikgewogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXMoY29uZGl0aW9uKS5yZWR1Y2UoKGFjYywgW2tleSwgdmFsdWVdKSA9PiB7CiAgICAgICAgaWYgKHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSAnJykgewogICAgICAgICAgaWYgKHZhbHVlLmluZGV4T2YoJywnKSA+IC0xKSB7CiAgICAgICAgICAgIGFjY1trZXldID0gdmFsdWUuc3BsaXQoJywnKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWNjW2tleV0gPSBuZXcgQXJyYXkodmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIHJldHVybiBhY2MKICAgICAgfSwge30pCiAgICB9LAogICAgaGFuZGxlU3dpdGNoVHlwZUNoYXJ0KGlkKSB7CiAgICAgIHRoaXMuZGF0YS50eXBlLmZvckVhY2goKHJvdykgPT4gewogICAgICAgIHJvdy5jaGFydC5mb3JFYWNoKChjb2wpID0+IHsKICAgICAgICAgIGlmIChjb2wuaWQgPT09IGlkKSB7CiAgICAgICAgICAgIGNvbC52aXNpYmxlID0gZmFsc2UKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5hY3RpdmVOYW1lcyA9IHZhbAogICAgfSwKICAgIGdldEFjdGl2ZUNoYXJ0Q29uZGl0aW9uKCkgewogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXModGhpcy5kYXRhLmFjdGl2ZS5jb25kaXRpb24pLnJlZHVjZSgoYWNjLCBba2V5LCB2YWx1ZV0pID0+IHsKICAgICAgICBpZiAodmFsdWUgIT09ICcnICYmIHZhbHVlICE9PSBudWxsKSB7CiAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgewogICAgICAgICAgICBhY2Nba2V5XSA9IHZhbHVlCiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgaWYgKGtleS5pbmRleE9mKCdSYW5nZScpID4gLTEpIHsKICAgICAgICAgICAgICBhY2Nba2V5XSA9IHRoaXMuaXBSYW5nZSh2YWx1ZSkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBhY2Nba2V5XSA9IHZhbHVlLmpvaW4oJywnKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIHJldHVybiBhY2MKICAgICAgfSwge30pCiAgICB9LAogICAgaXBSYW5nZShpcEFycikgewogICAgICBsZXQgaXAgPSAnJwogICAgICBjb25zdCBpcFJhbmdlID0gaXBBcnIuZmlsdGVyKChpdGVtKSA9PiAhaXNFbXB0eShpdGVtKSkKICAgICAgaWYgKGlwUmFuZ2UubGVuZ3RoID4gMCkgewogICAgICAgIGlwID0gaXBBcnIuam9pbignLCcpCiAgICAgIH0KICAgICAgcmV0dXJuIGlwCiAgICB9LAogICAgZ2V0Q2hhcnREYXRhKGNoYXJ0SWQsIGZuKSB7CiAgICAgIHF1ZXJ5Q2hhcnREYXRhKGNoYXJ0SWQsIHRoaXMuZGFzaGJvYXJkSWQpLnRoZW4oKHJlcykgPT4gewogICAgICAgIHJlcy5vcHRpb24uc3R5bGUgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNoYXJ0W3Jlcy50eXBlXS5zdHlsZSkKICAgICAgICB0aGlzLmRhdGEuYWN0aXZlLmNoYXJ0RGF0YSA9IHJlcwogICAgICAgIGZuKCkKICAgICAgfSkKICAgIH0sCiAgfSwKfQo="}, null]}