{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-63d74eb1\"],{\"277f\":function(e,t,a){\"use strict\";var r=a(\"6724\"),n=a.n(r);n.a},\"2ca0\":function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"06cf\").f,i=a(\"50c4\"),s=a(\"5a34\"),o=a(\"1d80\"),c=a(\"ab13\"),u=a(\"c430\"),l=\"\".startsWith,d=Math.min,h=c(\"startsWith\"),f=!u&&!h&&!!function(){var e=n(String.prototype,\"startsWith\");return e&&!e.writable}();r({target:\"String\",proto:!0,forced:!f&&!h},{startsWith:function(e){var t=String(o(this));s(e);var a=i(d(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return l?l.call(t,r,a):t.slice(a,a+r.length)===r}})},3075:function(e,t,a){\"use strict\";var r=a(\"6b31\"),n=a.n(r);n.a},\"3f35\":function(e,t,a){},\"5a0c\":function(e,t,a){!function(t,a){e.exports=a()}(0,(function(){\"use strict\";var e=1e3,t=6e4,a=36e5,r=\"millisecond\",n=\"second\",i=\"minute\",s=\"hour\",o=\"day\",c=\"week\",u=\"month\",l=\"quarter\",d=\"year\",h=\"date\",f=\"Invalid Date\",p=/^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/,m=/\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(e){var t=[\"th\",\"st\",\"nd\",\"rd\"],a=e%100;return\"[\"+e+(t[(a-20)%10]||t[a]||t[0])+\"]\"}},v=function(e,t,a){var r=String(e);return!r||r.length>=t?e:\"\"+Array(t+1-r.length).join(a)+e},b={s:v,z:function(e){var t=-e.utcOffset(),a=Math.abs(t),r=Math.floor(a/60),n=a%60;return(t<=0?\"+\":\"-\")+v(r,2,\"0\")+\":\"+v(n,2,\"0\")},m:function e(t,a){if(t.date()<a.date())return-e(a,t);var r=12*(a.year()-t.year())+(a.month()-t.month()),n=t.clone().add(r,u),i=a-n<0,s=t.clone().add(r+(i?-1:1),u);return+(-(r+(a-n)/(i?n-s:s-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:d,w:c,d:o,D:h,h:s,m:i,s:n,ms:r,Q:l}[e]||String(e||\"\").toLowerCase().replace(/s$/,\"\")},u:function(e){return void 0===e}},y=\"en\",w={};w[y]=g;var $=\"$isDayjsObject\",S=function(e){return e instanceof _||!(!e||!e[$])},x=function e(t,a,r){var n;if(!t)return y;if(\"string\"==typeof t){var i=t.toLowerCase();w[i]&&(n=i),a&&(w[i]=a,n=i);var s=t.split(\"-\");if(!n&&s.length>1)return e(s[0])}else{var o=t.name;w[o]=t,n=o}return!r&&n&&(y=n),n||!r&&y},k=function(e,t){if(S(e))return e.clone();var a=\"object\"==typeof t?t:{};return a.date=e,a.args=arguments,new _(a)},D=b;D.l=x,D.i=S,D.w=function(e,t){return k(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var _=function(){function g(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[$]=!0}var v=g.prototype;return v.parse=function(e){this.$d=function(e){var t=e.date,a=e.utc;if(null===t)return new Date(NaN);if(D.u(t))return new Date;if(t instanceof Date)return new Date(t);if(\"string\"==typeof t&&!/Z$/i.test(t)){var r=t.match(p);if(r){var n=r[2]-1||0,i=(r[7]||\"0\").substring(0,3);return a?new Date(Date.UTC(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(t)}(e),this.init()},v.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},v.$utils=function(){return D},v.isValid=function(){return!(this.$d.toString()===f)},v.isSame=function(e,t){var a=k(e);return this.startOf(t)<=a&&a<=this.endOf(t)},v.isAfter=function(e,t){return k(e)<this.startOf(t)},v.isBefore=function(e,t){return this.endOf(t)<k(e)},v.$g=function(e,t,a){return D.u(e)?this[t]:this.set(a,e)},v.unix=function(){return Math.floor(this.valueOf()/1e3)},v.valueOf=function(){return this.$d.getTime()},v.startOf=function(e,t){var a=this,r=!!D.u(t)||t,l=D.p(e),f=function(e,t){var n=D.w(a.$u?Date.UTC(a.$y,t,e):new Date(a.$y,t,e),a);return r?n:n.endOf(o)},p=function(e,t){return D.w(a.toDate()[e].apply(a.toDate(\"s\"),(r?[0,0,0,0]:[23,59,59,999]).slice(t)),a)},m=this.$W,g=this.$M,v=this.$D,b=\"set\"+(this.$u?\"UTC\":\"\");switch(l){case d:return r?f(1,0):f(31,11);case u:return r?f(1,g):f(0,g+1);case c:var y=this.$locale().weekStart||0,w=(m<y?m+7:m)-y;return f(r?v-w:v+(6-w),g);case o:case h:return p(b+\"Hours\",0);case s:return p(b+\"Minutes\",1);case i:return p(b+\"Seconds\",2);case n:return p(b+\"Milliseconds\",3);default:return this.clone()}},v.endOf=function(e){return this.startOf(e,!1)},v.$set=function(e,t){var a,c=D.p(e),l=\"set\"+(this.$u?\"UTC\":\"\"),f=(a={},a[o]=l+\"Date\",a[h]=l+\"Date\",a[u]=l+\"Month\",a[d]=l+\"FullYear\",a[s]=l+\"Hours\",a[i]=l+\"Minutes\",a[n]=l+\"Seconds\",a[r]=l+\"Milliseconds\",a)[c],p=c===o?this.$D+(t-this.$W):t;if(c===u||c===d){var m=this.clone().set(h,1);m.$d[f](p),m.init(),this.$d=m.set(h,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},v.set=function(e,t){return this.clone().$set(e,t)},v.get=function(e){return this[D.p(e)]()},v.add=function(r,l){var h,f=this;r=Number(r);var p=D.p(l),m=function(e){var t=k(f);return D.w(t.date(t.date()+Math.round(e*r)),f)};if(p===u)return this.set(u,this.$M+r);if(p===d)return this.set(d,this.$y+r);if(p===o)return m(1);if(p===c)return m(7);var g=(h={},h[i]=t,h[s]=a,h[n]=e,h)[p]||1,v=this.$d.getTime()+r*g;return D.w(v,this)},v.subtract=function(e,t){return this.add(-1*e,t)},v.format=function(e){var t=this,a=this.$locale();if(!this.isValid())return a.invalidDate||f;var r=e||\"YYYY-MM-DDTHH:mm:ssZ\",n=D.z(this),i=this.$H,s=this.$m,o=this.$M,c=a.weekdays,u=a.months,l=a.meridiem,d=function(e,a,n,i){return e&&(e[a]||e(t,r))||n[a].slice(0,i)},h=function(e){return D.s(i%12||12,e,\"0\")},p=l||function(e,t,a){var r=e<12?\"AM\":\"PM\";return a?r.toLowerCase():r};return r.replace(m,(function(e,r){return r||function(e){switch(e){case\"YY\":return String(t.$y).slice(-2);case\"YYYY\":return D.s(t.$y,4,\"0\");case\"M\":return o+1;case\"MM\":return D.s(o+1,2,\"0\");case\"MMM\":return d(a.monthsShort,o,u,3);case\"MMMM\":return d(u,o);case\"D\":return t.$D;case\"DD\":return D.s(t.$D,2,\"0\");case\"d\":return String(t.$W);case\"dd\":return d(a.weekdaysMin,t.$W,c,2);case\"ddd\":return d(a.weekdaysShort,t.$W,c,3);case\"dddd\":return c[t.$W];case\"H\":return String(i);case\"HH\":return D.s(i,2,\"0\");case\"h\":return h(1);case\"hh\":return h(2);case\"a\":return p(i,s,!0);case\"A\":return p(i,s,!1);case\"m\":return String(s);case\"mm\":return D.s(s,2,\"0\");case\"s\":return String(t.$s);case\"ss\":return D.s(t.$s,2,\"0\");case\"SSS\":return D.s(t.$ms,3,\"0\");case\"Z\":return n}return null}(e)||n.replace(\":\",\"\")}))},v.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},v.diff=function(r,h,f){var p,m=this,g=D.p(h),v=k(r),b=(v.utcOffset()-this.utcOffset())*t,y=this-v,w=function(){return D.m(m,v)};switch(g){case d:p=w()/12;break;case u:p=w();break;case l:p=w()/3;break;case c:p=(y-b)/6048e5;break;case o:p=(y-b)/864e5;break;case s:p=y/a;break;case i:p=y/t;break;case n:p=y/e;break;default:p=y}return f?p:D.a(p)},v.daysInMonth=function(){return this.endOf(u).$D},v.$locale=function(){return w[this.$L]},v.locale=function(e,t){if(!e)return this.$L;var a=this.clone(),r=x(e,t,!0);return r&&(a.$L=r),a},v.clone=function(){return D.w(this.$d,this)},v.toDate=function(){return new Date(this.valueOf())},v.toJSON=function(){return this.isValid()?this.toISOString():null},v.toISOString=function(){return this.$d.toISOString()},v.toString=function(){return this.$d.toUTCString()},g}(),C=_.prototype;return k.prototype=C,[[\"$ms\",r],[\"$s\",n],[\"$m\",i],[\"$H\",s],[\"$W\",o],[\"$M\",u],[\"$y\",d],[\"$D\",h]].forEach((function(e){C[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),k.extend=function(e,t){return e.$i||(e(t,_,k),e.$i=!0),k},k.locale=x,k.isDayjs=S,k.unix=function(e){return k(1e3*e)},k.en=w[y],k.Ls=w,k.p={},k}))},\"5a34\":function(e,t,a){var r=a(\"44e7\");e.exports=function(e){if(r(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},6724:function(e,t,a){},\"6b31\":function(e,t,a){},\"860a\":function(e,t,a){\"use strict\";var r=a(\"3f35\"),n=a.n(r);n.a},\"8a79\":function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"06cf\").f,i=a(\"50c4\"),s=a(\"5a34\"),o=a(\"1d80\"),c=a(\"ab13\"),u=a(\"c430\"),l=\"\".endsWith,d=Math.min,h=c(\"endsWith\"),f=!u&&!h&&!!function(){var e=n(String.prototype,\"endsWith\");return e&&!e.writable}();r({target:\"String\",proto:!0,forced:!f&&!h},{endsWith:function(e){var t=String(o(this));s(e);var a=arguments.length>1?arguments[1]:void 0,r=i(t.length),n=void 0===a?r:d(i(a),r),c=String(e);return l?l.call(t,c,n):t.slice(n-c.length,n)===c}})},ab13:function(e,t,a){var r=a(\"b622\"),n=r(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[n]=!1,\"/./\"[e](t)}catch(r){}}return!1}},c9d9:function(e,t,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var r=a(\"bc3a\"),n=a.n(r),i=a(\"4360\"),s=a(\"a18c\"),o=a(\"a47e\"),c=a(\"f7b5\"),u=a(\"f907\"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),l=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,h=r.VUE_APP_BASE_API,f=\"true\"===d?\"\":h;\"production\"===l&&(f=\"\");var p={baseURL:f,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===l&&(p.timeout=a),t){case\"upload\":p.headers[\"Content-Type\"]=\"multipart/form-data\",p[\"processData\"]=!1,p[\"contentType\"]=!1;break;case\"download\":p[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var m=n.a.create(p);return m.interceptors.request.use((function(e){var t=i[\"a\"].getters.token;return\"\"!==t&&(e.headers[\"access_token\"]=t,e.url.startsWith(\"/api2/\")&&(e.headers[\"Authorization\"]=\"Basic YWRtaW5pc3RyYXRvcjpBZG1pbjEyMw==\")),e}),(function(e){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:e,print:!0}),Promise.reject(\"response-err:\"+e)})),m.interceptors.response.use((function(e){var a=void 0===e.headers[\"code\"]?200:Number(e.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){s[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,n=\"\";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n=\"error\"),e.data.code>=2e3&&e.data.code<3e3&&(n=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(t),type:n}),Promise.reject(\"response-err-status:\".concat(r||u[\"a\"][a][t],\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.\".concat(a,\".\").concat(t))))};switch(e.data.code){case u[\"a\"].exception.system:t(\"system\");break;case u[\"a\"].exception.server:t(\"server\");break;case u[\"a\"].exception.session:r();break;case u[\"a\"].exception.access:r();break;case u[\"a\"].exception.certification:t(\"certification\");break;case u[\"a\"].exception.auth:t(\"auth\"),s[\"a\"].replace({path:\"/401\"});break;case u[\"a\"].exception.token:t(\"token\");break;case u[\"a\"].exception.param:t(\"param\");break;case u[\"a\"].exception.idempotency:t(\"idempotency\");break;case u[\"a\"].exception.ip:t(\"ip\"),i[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"});break;case u[\"a\"].exception.upload:t(\"upload\");break;case u[\"a\"].attack.xss:t(\"xss\",\"attack\");break;default:t(\"code\",\"exception\",-1);break}};switch(t){case\"upload\":if(0===a)return e.data.data;n();break;case\"download\":if(0===a)return{data:e.data,fileName:decodeURI(e.headers[\"file-name\"])};n();break;default:if(0===e.data.code||0===e.data.retcode)return e.data;n();break}}),(function(e){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){s[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),s[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===t?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),403==e.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(o[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),403==e.response.status&&a(),Promise.reject(\"response-err-status:\".concat(e,\" \\nerr-question: \").concat(o[\"a\"].t(\"ajax.service.timeout\"))))})),m(e)};t[\"a\"]=l},d81d:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").map,i=a(\"1dde\"),s=a(\"ae40\"),o=i(\"map\"),c=s(\"map\");r({target:\"Array\",proto:!0,forced:!o||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},ec3b:function(e,t,a){\"use strict\";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"router-wrap-table\"},[a(\"header\",{staticClass:\"table-header\"},[a(\"section\",{staticClass:\"table-header-main\"},[a(\"section\",{staticClass:\"table-header-search\"},[a(\"section\",{directives:[{name:\"show\",rawName:\"v-show\",value:!e.isShow,expression:\"!isShow\"}],staticClass:\"table-header-search-input\"},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:\"设备名称\",\"prefix-icon\":\"soc-icon-search\"},on:{change:e.handleQuery},model:{value:e.queryInput.deviceName,callback:function(t){e.$set(e.queryInput,\"deviceName\",t)},expression:\"queryInput.deviceName\"}})],1),a(\"section\",{staticClass:\"table-header-search-button\"},[e.isShow?e._e():a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleQuery}},[e._v(\"查询\")]),a(\"el-button\",{on:{click:e.toggleShow}},[e._v(\" 高级搜索 \"),a(\"i\",{staticClass:\"el-icon--right\",class:e.isShow?\"el-icon-arrow-up\":\"el-icon-arrow-down\"})])],1)]),a(\"section\",{staticClass:\"table-header-button\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleAdd}},[e._v(\"新建授权\")]),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleSync}},[e._v(\"设备同步\")]),a(\"el-button\",{attrs:{type:\"danger\"},on:{click:e.handleBatchDelete}},[e._v(\"批量删除\")])],1)]),a(\"section\",{staticClass:\"table-header-extend\"},[a(\"el-collapse-transition\",[a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.isShow,expression:\"isShow\"}]},[a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:6}},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:\"设备名称\"},on:{change:e.handleQuery},model:{value:e.queryInput.deviceName,callback:function(t){e.$set(e.queryInput,\"deviceName\",t)},expression:\"queryInput.deviceName\"}})],1),a(\"el-col\",{attrs:{span:6}},[a(\"el-select\",{attrs:{clearable:\"\",placeholder:\"授权状态\"},on:{change:e.handleQuery},model:{value:e.queryInput.authStatus,callback:function(t){e.$set(e.queryInput,\"authStatus\",t)},expression:\"queryInput.authStatus\"}},[a(\"el-option\",{attrs:{label:\"全部\",value:\"\"}}),a(\"el-option\",{attrs:{label:\"已授权\",value:\"1\"}}),a(\"el-option\",{attrs:{label:\"未授权\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"过期\",value:\"2\"}})],1)],1),a(\"el-col\",{attrs:{span:6}},[a(\"el-select\",{attrs:{clearable:\"\",placeholder:\"许可证类型\"},on:{change:e.handleQuery},model:{value:e.queryInput.licenseType,callback:function(t){e.$set(e.queryInput,\"licenseType\",t)},expression:\"queryInput.licenseType\"}},[a(\"el-option\",{attrs:{label:\"全部\",value:\"\"}}),a(\"el-option\",{attrs:{label:\"IPS\",value:\"IPS\"}}),a(\"el-option\",{attrs:{label:\"SOFT\",value:\"SOFT\"}}),a(\"el-option\",{attrs:{label:\"AntiVirus\",value:\"AntiVirus\"}})],1)],1)],1),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:24,align:\"right\"}},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleQuery}},[e._v(\"查询\")]),a(\"el-button\",{on:{click:e.handleReset}},[e._v(\"重置\")]),a(\"el-button\",{attrs:{icon:\"soc-icon-scroller-top-all\"},on:{click:e.toggleShow}})],1)],1)],1)])],1)]),a(\"main\",{staticClass:\"table-body\"},[e._m(0),a(\"section\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"table-body-main\"},[a(\"el-table\",{attrs:{data:e.tableData,\"element-loading-background\":\"rgba(0, 0, 0, 0.3)\",size:\"mini\",\"highlight-current-row\":\"\",\"tooltip-effect\":\"light\",height:\"100%\"},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"序号\",width:\"80\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s((e.pagination.currentPage-1)*e.pagination.pageSize+t.$index+1)+\" \")]}}])}),a(\"el-table-column\",{attrs:{prop:\"liceneStatus\",label:\"授权状态\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",{class:e.getStatusClass(t.row.liceneStatus)},[e._v(\" \"+e._s(e.getStatusText(t.row.liceneStatus))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"deviceName\",label:\"设备名称\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"licenceCategory\",label:\"许可证类型\"}}),a(\"el-table-column\",{attrs:{prop:\"expireDate\",label:\"到期时间\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.formatTime(t.row.expireDate))+\" \")]}}])}),a(\"el-table-column\",{attrs:{prop:\"createTime\",label:\"获取设备许可证时间\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.formatTime(t.row.createTime))+\" \")]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"200\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"action-buttons\"},[a(\"el-button\",{staticClass:\"el-button--blue\",attrs:{type:\"text\"},on:{click:function(a){return e.handleReauthorize(t.row)}}},[e._v(\"重新授权\")]),a(\"el-button\",{staticClass:\"el-button--blue\",attrs:{type:\"text\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])],1)]}}])})],1)],1)]),a(\"footer\",{staticClass:\"table-footer\"},[e.pagination.visible?a(\"el-pagination\",{attrs:{small:\"\",background:\"\",align:\"right\",\"current-page\":e.pagination.currentPage,\"page-sizes\":[10,20,50,100],\"page-size\":e.pagination.pageSize,layout:\"total, sizes, prev, pager, next, jumper\",total:e.pagination.total},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.handlePageChange}}):e._e()],1),a(\"add-auth-modal\",{attrs:{visible:e.addModalVisible},on:{\"update:visible\":function(t){e.addModalVisible=t},\"on-submit\":e.handleAddSubmit}}),a(\"device-sync-component\",{ref:\"deviceSyncComponent\",on:{\"on-submit\":e.handleSyncSubmit}})],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"section\",{staticClass:\"table-body-header\"},[a(\"h2\",{staticClass:\"table-body-title\"},[e._v(\"授权管理\")])])}],i=(a(\"a15b\"),a(\"d81d\"),a(\"f3f3\")),s=(a(\"96cf\"),a(\"c964\")),o=a(\"c9d9\");function c(e){return Object(o[\"a\"])({url:\"/dev/authorize/pages\",method:\"post\",data:e||{}})}function u(e){return Object(o[\"a\"])({url:\"/dev/authorize/add\",method:\"post\",data:e||{}})}function l(e){return Object(o[\"a\"])({url:\"/dev/authorize/delete\",method:\"post\",data:e||{}})}function d(e){return Object(o[\"a\"])({url:\"/dev/authorize/reauthorize\",method:\"post\",data:e||{}})}function h(e){return Object(o[\"a\"])({url:\"/dev/authorize/syncFromDevice\",method:\"post\",data:e||{}})}function f(e){return Object(o[\"a\"])({url:\"/dev/device/all\",method:\"post\",data:e||{}})}var p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-dialog\",{attrs:{title:\"新建授权\",visible:e.dialogVisible,width:\"650px\",\"before-close\":e.handleClose,\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[a(\"el-form\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],ref:\"form\",attrs:{model:e.formData,rules:e.rules,\"label-width\":\"120px\"}},[a(\"el-form-item\",{attrs:{label:\"选择设备\",prop:\"deviceId\"}},[a(\"el-tree-select\",{staticStyle:{width:\"100%\"},attrs:{data:e.deviceData,props:e.treeProps,placeholder:\"请选择设备\",\"check-strictly\":!0},on:{change:e.handleDeviceChange},model:{value:e.formData.deviceId,callback:function(t){e.$set(e.formData,\"deviceId\",t)},expression:\"formData.deviceId\"}})],1),a(\"el-form-item\",{attrs:{label:\"许可证类型\",prop:\"licenceCategory\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择许可证类型\"},model:{value:e.formData.licenceCategory,callback:function(t){e.$set(e.formData,\"licenceCategory\",t)},expression:\"formData.licenceCategory\"}},[a(\"el-option\",{attrs:{label:\"IPS\",value:\"IPS\"}}),a(\"el-option\",{attrs:{label:\"AntiVirus\",value:\"AntiVirus\"}}),a(\"el-option\",{attrs:{label:\"SOFT\",value:\"SOFT\"}})],1)],1),a(\"el-form-item\",{attrs:{label:\"选择授权文件\",prop:\"file\"}},[a(\"el-upload\",{ref:\"upload\",attrs:{action:e.uploadAction,headers:e.uploadHeaders,\"on-success\":e.handleUploadSuccess,\"on-error\":e.handleUploadError,\"before-upload\":e.beforeUpload,\"file-list\":e.fileList,\"auto-upload\":!0,accept:\".lic,.license\"}},[a(\"el-button\",[a(\"i\",{staticClass:\"el-icon-upload\"}),e._v(\" 导入 \")]),a(\"div\",{staticClass:\"el-upload__tip\",attrs:{slot:\"tip\"},slot:\"tip\"},[e._v(\"只能上传 .lic/.license 文件\")])],1)],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.handleClose}},[e._v(\"取消\")]),a(\"el-button\",{attrs:{type:\"primary\",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v(\"确认\")])],1)],1)},m=[],g=(a(\"b0c0\"),a(\"a9e3\"),a(\"8a79\"),{name:\"AddAuthModal\",props:{visible:{type:Boolean,default:!1},typeAuth:{type:Number,default:0}},data:function(){return{loading:!1,submitLoading:!1,formData:{deviceId:\"\",licenceCategory:\"IPS\",file:null},rules:{deviceId:[{required:!0,message:\"请选择设备\",trigger:\"change\"}],licenceCategory:[{required:!0,message:\"请选择许可证类型\",trigger:\"change\"}],file:[{required:!0,message:\"请选择授权文件\",trigger:\"change\"}]},deviceData:[],fileList:[],uploadedFile:null,treeProps:{children:\"childList\",label:\"name\",value:\"srcId\",disabled:function(e){return\"0\"===e.type}},uploadAction:\"/ap/v1/licenseManage/move\",uploadHeaders:{authorization:\"authorization-text\"}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit(\"update:visible\",e)}}},watch:{visible:function(e){e&&(this.initForm(),this.loadDeviceData())}},methods:{initForm:function(){var e=this;this.formData={deviceId:\"\",licenceCategory:\"IPS\",file:null},this.fileList=[],this.uploadedFile=null,this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},loadDeviceData:function(){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,f({});case 4:a=t.sent,0===a.retcode?e.deviceData=e.transformTreeData(a.data||[]):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](1),e.$message.error(\"获取设备列表失败\");case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case\"end\":return t.stop()}}),t,null,[[1,8,11,14]])})))()},transformTreeData:function(e){var t=this;return e.map((function(e){var a=Object(i[\"a\"])(Object(i[\"a\"])({},e),{},{value:e.srcId,disabled:\"0\"===e.type});return e.childList&&e.childList.length>0&&(a.childList=t.transformTreeData(e.childList)),a}))},handleDeviceChange:function(e){},beforeUpload:function(e){var t=e.name.endsWith(\".lic\")||e.name.endsWith(\".license\");if(!t)return this.$message.error(\"只能上传 .lic 或 .license 格式的文件!\"),!1;var a=e.size/1024/1024<10;return!!a||(this.$message.error(\"文件大小不能超过 10MB!\"),!1)},handleUploadSuccess:function(e,t){this.uploadedFile=t,this.formData.file=t.name,this.$message.success(\"\".concat(t.name,\" 文件上传成功\")),this.$refs.form.validateField(\"file\")},handleUploadError:function(e,t){this.$message.error(\"\".concat(t.name,\" 文件上传失败\"))},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(s[\"a\"])(regeneratorRuntime.mark((function t(a){var r,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=16;break}return e.submitLoading=!0,t.prev=2,r={licenceCategory:e.formData.licenceCategory,deviceIds:e.formData.deviceId,fileName:e.formData.file},t.next=6,u(r);case 6:n=t.sent,0===n.retcode?(e.$message.success(\"添加成功!\"),e.$emit(\"on-submit\"),e.handleClose()):e.$message.error(n.msg),t.next=13;break;case 10:t.prev=10,t.t0=t[\"catch\"](2),e.$message.error(\"操作失败\");case 13:return t.prev=13,e.submitLoading=!1,t.finish(13);case 16:case\"end\":return t.stop()}}),t,null,[[2,10,13,16]])})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){this.dialogVisible=!1}}}),v=g,b=(a(\"860a\"),a(\"2877\")),y=Object(b[\"a\"])(v,p,m,!1,null,\"2c615a7c\",null),w=y.exports,$=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-drawer\",{attrs:{title:\"设备同步\",visible:e.drawerVisible,direction:\"rtl\",size:\"600px\",\"before-close\":e.handleClose},on:{\"update:visible\":function(t){e.drawerVisible=t}}},[a(\"div\",{staticClass:\"drawer-content\"},[a(\"el-form\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],ref:\"form\",attrs:{model:e.formData,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"选择设备\",prop:\"deviceIds\"}},[a(\"el-tree\",{ref:\"deviceTree\",attrs:{data:e.deviceData,\"show-checkbox\":\"\",\"node-key\":\"srcId\",props:e.treeProps,\"check-strictly\":!1,\"default-checked-keys\":e.selectedDeviceIds},on:{check:e.handleTreeCheck}})],1)],1),a(\"div\",{staticClass:\"drawer-footer\"},[a(\"el-button\",{on:{click:e.handleClose}},[e._v(\"关闭\")]),a(\"el-button\",{attrs:{type:\"primary\",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v(\"同步\")])],1)],1)])},S=[],x=(a(\"4160\"),a(\"159b\"),{name:\"DeviceSyncComponent\",data:function(){return{drawerVisible:!1,loading:!1,submitLoading:!1,formData:{deviceIds:[]},rules:{deviceIds:[{required:!0,message:\"请选择设备\",trigger:\"change\"}]},deviceData:[],selectedDeviceIds:[],treeProps:{children:\"childList\",label:\"name\",disabled:function(e){return\"0\"===e.type}}}},methods:{showDrawer:function(){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.drawerVisible=!0,t.next=3,e.loadDeviceData();case 3:case\"end\":return t.stop()}}),t)})))()},loadDeviceData:function(){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,f({});case 4:a=t.sent,0===a.retcode?e.deviceData=e.transformTreeData(a.data||[]):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](1),e.$message.error(\"获取设备列表失败\");case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case\"end\":return t.stop()}}),t,null,[[1,8,11,14]])})))()},transformTreeData:function(e){var t=this;return e.map((function(e){var a=Object(i[\"a\"])(Object(i[\"a\"])({},e),{},{disabled:\"0\"===e.type});return e.childList&&e.childList.length>0&&(a.childList=t.transformTreeData(e.childList)),a}))},handleTreeCheck:function(e,t){var a=[],r=t.checkedNodes||[];r.forEach((function(e){\"1\"===e.type&&a.push(e.srcId)})),this.formData.deviceIds=a},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(s[\"a\"])(regeneratorRuntime.mark((function t(a){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a&&e.$confirm(\"确认同步选中设备的授权信息吗？\",\"确认同步\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(s[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.submitLoading=!0,t.prev=1,a={deviceIds:e.formData.deviceIds.join(\",\")},t.next=5,h(a);case 5:r=t.sent,0===r.retcode?(e.$message.success(r.msg||\"同步成功\"),e.$emit(\"on-submit\"),e.handleClose()):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t[\"catch\"](1),e.$message.error(\"同步失败\");case 12:return t.prev=12,e.submitLoading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[1,9,12,15]])})))).catch((function(){}));case 1:case\"end\":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){var e=this;this.drawerVisible=!1,this.formData={deviceIds:[]},this.selectedDeviceIds=[],this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))}}}),k=x,D=(a(\"277f\"),Object(b[\"a\"])(k,$,S,!1,null,\"02a36e14\",null)),_=D.exports,C=a(\"5a0c\"),O=a.n(C),I={name:\"AuthManage\",components:{AddAuthModal:w,DeviceSyncComponent:_},data:function(){return{isShow:!1,loading:!1,queryInput:{deviceName:\"\",authStatus:\"\",licenseType:\"\"},tableData:[],selectedRows:[],pagination:{total:0,pageSize:10,currentPage:1,visible:!0},addModalVisible:!1}},mounted:function(){this.getAuthList()},methods:{toggleShow:function(){this.isShow=!this.isShow},getAuthList:function(){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,a=Object(i[\"a\"])({pageIndex:e.pagination.currentPage,pageSize:e.pagination.pageSize},e.buildQueryParams()),t.prev=2,t.next=5,c(a);case 5:r=t.sent,0===r.retcode?(e.tableData=r.data.rows||[],e.pagination.total=r.data.total||0,e.selectedRows=[]):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t[\"catch\"](2),e.$message.error(\"获取授权列表失败\");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[2,9,12,15]])})))()},buildQueryParams:function(){var e={};return this.queryInput.deviceName&&(e.deviceName=this.queryInput.deviceName),\"\"!==this.queryInput.authStatus&&(e.authStatus=this.queryInput.authStatus),this.queryInput.licenseType&&(e.licenseType=this.queryInput.licenseType),e},handleQuery:function(){this.pagination.currentPage=1,this.getAuthList()},handleReset:function(){this.queryInput={deviceName:\"\",authStatus:\"\",licenseType:\"\"},this.handleQuery()},handleAdd:function(){this.addModalVisible=!0},handleSync:function(){this.$refs.deviceSyncComponent.showDrawer()},handleReauthorize:function(e){var t=this;this.$confirm(\"确定要重新授权该设备吗?\",\"重新授权\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(s[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,d({id:e.id});case 3:r=a.sent,0===r.retcode?(t.$message.success(\"重新授权成功\"),t.getAuthList()):t.$message.error(r.msg),a.next=10;break;case 7:a.prev=7,a.t0=a[\"catch\"](0),t.$message.error(\"重新授权失败\");case 10:case\"end\":return a.stop()}}),a,null,[[0,7]])})))).catch((function(){}))},handleDelete:function(e){var t=this;this.$confirm(\"确定要删除该授权吗?删除后不可恢复\",\"删除\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(s[\"a\"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,l({ids:e.id});case 3:r=a.sent,0===r.retcode?(t.$message.success(\"删除成功\"),t.getAuthList()):t.$message.error(r.msg),a.next=10;break;case 7:a.prev=7,a.t0=a[\"catch\"](0),t.$message.error(\"删除失败\");case 10:case\"end\":return a.stop()}}),a,null,[[0,7]])})))).catch((function(){}))},handleBatchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm(\"确定要删除选中授权吗?删除后不可恢复\",\"删除\",{confirmButtonText:\"确认\",cancelButtonText:\"取消\",type:\"warning\"}).then(Object(s[\"a\"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a=e.selectedRows.map((function(e){return e.id})).join(\",\"),t.next=4,l({ids:a});case 4:r=t.sent,0===r.retcode?(e.$message.success(\"删除成功\"),e.getAuthList()):e.$message.error(r.msg),t.next=11;break;case 8:t.prev=8,t.t0=t[\"catch\"](0),e.$message.error(\"删除失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,8]])})))).catch((function(){})):this.$message.error(\"至少选中一条数据\")},handleAddSubmit:function(){this.addModalVisible=!1,this.getAuthList()},handleSyncSubmit:function(){this.getAuthList()},handleSelectionChange:function(e){this.selectedRows=e},handleSizeChange:function(e){this.pagination.pageSize=e,this.getAuthList()},handlePageChange:function(e){this.pagination.currentPage=e,this.getAuthList()},formatTime:function(e){return e&&\"-\"!==e?O()(e).format(\"YYYY/MM/DD\"):\"-\"},getStatusText:function(e){var t={0:\"未授权\",1:\"已授权\",2:\"过期\"};return t[e]||\"未知\"},getStatusClass:function(e){var t={0:\"status-failed\",1:\"status-success\",2:\"status-warning\"};return t[e]||\"\"}}},M=I,T=(a(\"3075\"),Object(b[\"a\"])(M,r,n,!1,null,\"c02bb664\",null));t[\"default\"]=T.exports}}]);", "extractedComments": []}