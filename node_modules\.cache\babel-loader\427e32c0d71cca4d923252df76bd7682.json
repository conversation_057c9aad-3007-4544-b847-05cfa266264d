{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\index.vue", "mtime": 1750323702934}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}