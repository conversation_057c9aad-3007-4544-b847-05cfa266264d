import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";


//过滤策略-查询接口
export async function tacticsSearch(params) {
  return request(`/home_dev/filter_tactics/list`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//过滤策略-新增接口
export async function tacticsAdd(params) {
  return request(`/home_dev/filter_tactics/add_filter_tactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//过滤策略-编辑接口
export async function tacticsUpdate(params) {
  return request(`/home_dev/filter_tactics/update_filter_tactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//过滤策略-删除接口
export async function tacticsDelete(params) {
  return request(`/home_dev/filter_tactics/delete_filter_tactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//过滤策略-批量下发
export async function tacticsDistrib(params) {
  return request(`/home_dev/filter_tactics/batch_distrib`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//过滤策略-设备集合
export async function addInspectionData(params) {
  return request(`/home_dev/audit_device/all`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}









