{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\index.vue", "mtime": 1750150748253}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}