<template>
  <div class="echarts-container" style="width: 100%; height: 60px;">
    <div ref="chart" style="width: 100%; height: 100%;"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EchartsTest',
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    record: {
      handler() {
        this.updateChart()
      },
      deep: true,
    },
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },

    updateChart() {
      if (!this.chart) return

      const option = {
        grid: {
          left: 0,
          right: 0,
          top: 5,
          bottom: 5,
        },
        xAxis: {
          type: 'category',
          show: false,
          data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
        },
        yAxis: {
          type: 'value',
          show: false,
        },
        series: [
          {
            data: this.generateData(),
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#1890ff',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(24, 144, 255, 0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(24, 144, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      this.chart.setOption(option)
    },

    generateData() {
      // 生成模拟数据
      if (this.record && this.record.data) {
        return this.record.data
      }
      return Array.from({ length: 10 }, () => Math.floor(Math.random() * 100))
    },
  },
}
</script>

<style scoped lang="scss">
.echarts-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
