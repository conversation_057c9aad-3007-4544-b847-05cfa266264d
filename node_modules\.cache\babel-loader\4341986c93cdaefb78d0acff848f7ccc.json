{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\view\\auditold\\StrategyFilter\\index.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\auditold\\StrategyFilter\\index.js", "mtime": 1715135000000}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}