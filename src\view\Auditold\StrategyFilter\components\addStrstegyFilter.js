import React, {
    useState,
    useEffect,
    forwardRef,
    createRef,
    useImperativeHandle,
  } from "react";
  import {
    message,
    Form,
    Row,
    Spin,
    Button,
    Input,
    Radio,
    Tooltip,
    Icon,
  } from "antd";
  import SbrDrawer from "@/components/SbrDrawer";
  import {tacticsAdd,  tacticsUpdate } from "../services";
  import ProtocolSelectModal from '../../StrategyCollection/components/protocolSelectModal'

  const protocolSelectModalRef = createRef();

  let Add = (props) => {
    const { refInstance, getSourceData } = props;
    const { getFieldDecorator, getFieldsValue, validateFields, setFieldsValue } = props.form;
    // 是否展示抽屉
    const [visible, setVisible] = useState(false);
    //标题
    const [title, setTitle] = useState('');
    // 查看数据
    const [record, setRecord] = useState({});
    const [loading, setLoading] = useState(false);
    // 协议集合
    const [protocolNames, setProtocolNames] = useState([]);
    const [protocolIds, setProtocolIds] = useState([]);

    useImperativeHandle(refInstance, () => ({
      showDrawer,
    }));
  
    useEffect(() => {
  
    }, []);
  
  
    // 打开抽屉
    const showDrawer = (record = {}) => {
      console.log()
      setVisible(true);
      if (record.id) {
        setTitle('编辑过滤策略');
        setTimeout(()=>{
          setFieldsValue({
            id: record.id,
            protocolId: record.protocolId,
            protocolName: record.protocolName,
            firstIp: record.firstIp,
            ipType: record.ipType,
          });
          // setProtocolIds([{
          //   protocolId: record.protocolId,
          //   protocolName: record.protocolName,
          // }])
          record.protocolId && setProtocolIds(record.protocolId.split(',').map(Number))
          record.protocolName && setProtocolNames(record.protocolName.split(','))
          setFieldsValue({
            secondIp: record.secondIp,
          })
        })
      }else{
        setTitle('新增过滤策略');
        setProtocolIds([])
        setProtocolNames([])
      }
    };

    const validatorIP  =(rule, value, callback) =>{
      const IP = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const Mac = /[A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}/
      if (value && !IP.test(value) && !Mac.test(value)) {
        return callback(new Error("请输入正确格式的过滤地址"));
      }
      if (value && getFieldsValue().secondIp) {
        if (IP.test(value) !== IP.test(getFieldsValue().secondIp)) {
          callback(new Error("请输入同IP/MAC过滤地址"));
        }else if (Mac.test(value) !== Mac.test(getFieldsValue().secondIp)) {
          callback(new Error("请输入同IP/MAC过滤地址"));
        }
        callback()
      }
      callback()
    }
    const validatorIP2 = (rule, value, callback) => {
      const IP = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const Mac =  /[A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}/;
      if (value && getFieldsValue().ipType === 1) {
        if (!IP.test(value) && !Mac.test(value)) {
          return callback(new Error("请输入正确格式的过滤地址"));
        }
        if (value && getFieldsValue().firstIp) {
          if (IP.test(value) !== IP.test(getFieldsValue().firstIp)) {
            callback(new Error("请输入同IP/MAC过滤地址"));
          }else if (Mac.test(value) !== Mac.test(getFieldsValue().firstIp)) {
            callback(new Error("请输入同IP/MAC过滤地址"));
          }
          callback()
        }
        callback()
      }
      callback()
    }
    // 触发校验
    const validEvent = () => {
      validateFields()
    }
    //关闭抽屉
    const onDrawerClose = () => {
      setVisible(false)
    };
    // 提交
    const handleSubmit = (e) => {
      e.preventDefault();
      validateFields(async (err, values) => {
        if (!err) {
          let res 
          if(title.indexOf('新增')!=-1){
            res = await tacticsAdd(values);
          }else{
            res = await tacticsUpdate(values);
          }      
          if (res.retcode == 0) {
            message.success('操作成功');
            getSourceData();
            onDrawerClose();
          } else {
            message.error(res.msg);
          }
          setLoading(false);
        }
      });
    };
  
    // 打开协议弹框
    const handleSelectProtocols = () => {
      protocolSelectModalRef.current.showModal()
    }
     // 已选指定协议数据
    const saveData = (obj) => {
      // setProtocolList(arr)
      setFieldsValue({
        protocolId: obj.ids[0],
        protocolName: obj.names[0],
      })
      // let ids = arr.map(item=>{
      //   return item.protocolId
      // })

      setProtocolIds(obj.ids)
      setProtocolNames(obj.names)
    }
    return (
      <>
      <SbrDrawer title={title} width={800} onClose={onDrawerClose} visible={visible}>
        <Form onSubmit={handleSubmit} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <div className="public-block">
            <Spin spinning={loading}>
            <Form.Item hidden>
              {getFieldDecorator("id", {})(
                <Input placeholder="请输入名称" />
              )}
            </Form.Item>
              <Form.Item label="地址类型">
                {getFieldDecorator("ipType", {
                  initialValue: 0,
                })(<Radio.Group>
                    <Radio value={0} key={"0"}>
                    单个地址
                    </Radio>
                    <Radio value={1} key={"1"}>
                    一对地址
                    </Radio>
                  </Radio.Group>)}
              </Form.Item>
              <Form.Item label="过滤地址1">
                {getFieldDecorator("firstIp", {
                    rules: [
                      {
                        required: true,
                        message: "请输入过滤地址1",
                      },
                      { validator: validatorIP }            
                    ],
                })(
                  <Input placeholder="请输入IP/MAC地址" style={{width: '95%'}} onBlur={validEvent}></Input>
                )}
                <Tooltip title={getFieldsValue().ipType === 0 ? <span>1.当协议为二层协议时，只能填MAC。<br/> 2.当协议为三层协议时，可以IP可以MAC</span> : <span>1.地址1与地址2必须同为IP或同为MAC <br/> 2.当协议为二层协议时，只能填MAC。 <br/> 3.当协议为三层协议时，可以IP可以MAC</span>}>
                  <Icon type="info-circle-o" style={{marginTop: '11px',position: 'absolute',right:-27}}/>
                </Tooltip>
              </Form.Item>
              {
                getFieldsValue().ipType === 1 ?  <Form.Item label="过滤地址2">
                 {getFieldDecorator("secondIp", {
                     rules: [
                       {
                         required: true,
                         message: "请输入过滤地址2",
                       },
                       { validator: validatorIP2 }
                     ],
                 })(
                    <Input placeholder="请输入IP/MAC地址" style={{width: '95%'}} onBlur={validEvent}></Input>
                 )}
                 <Tooltip title={<span>1.地址1与地址2必须同为IP或同为MAC <br/> 2.当协议为二层协议时，只能填MAC。 <br/> 3.当协议为三层协议时，可以IP可以MAC</span>}>
                  <Icon type="info-circle-o" style={{marginTop: '11px',position: 'absolute',right:-27}}/>
                </Tooltip>
               </Form.Item> : <></>
              }
              <Form.Item label="过滤协议">
                {getFieldDecorator("protocolName", {
                    rules: [
                      {
                        required: true,
                        message: "请输入过滤协议",
                      }                 
                    ],
                })(
                    <Input placeholder="请选择协议" addonAfter={<span onClick={handleSelectProtocols}>选择协议</span>} readOnly style={{width: '95%'}}></Input>
                )}
              </Form.Item>
              <Form.Item hidden>
                {getFieldDecorator("protocolId")(
                  <Input></Input>
                )}
              </Form.Item>
            </Spin>
          </div>
  
          <Row type="flex" justify="center" className="drawer_btns">
            <Button htmlType="submit" type="primary">
              保存
            </Button>
            <Button className="spacing_btn" onClick={onDrawerClose}>
              关闭
            </Button>
          </Row>
        </Form>
      </SbrDrawer>
      <ProtocolSelectModal ref={protocolSelectModalRef} type='radio' defaultProtocolIds={protocolIds} defaultProtocolNames={protocolNames} saveData={saveData}></ProtocolSelectModal>
      </>
      );
  };
  Add = Form.create()(Add);
  export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
  