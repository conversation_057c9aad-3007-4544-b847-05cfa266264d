<template>
  <el-drawer
    :visible.sync="visible"
    title="设备选择"
    size="50%"
    direction="rtl"
    @close="onClose"
  >
    <div style="padding: 20px;">
      <p>设备组件 - 待实现</p>
      <div style="text-align: right; margin-top: 20px;">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'DeviceComponent',
  props: {
    typeButton: {
      type: String,
      default: '',
    },
    tacticsDistrib: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      record: {},
      selectedIds: [],
    }
  },
  methods: {
    showDrawer(record = {}, selectedIds = []) {
      this.record = record
      this.selectedIds = selectedIds
      this.visible = true
    },

    onClose() {
      this.visible = false
      this.record = {}
      this.selectedIds = []
    },

    handleConfirm() {
      this.$emit('getSourceData')
      this.onClose()
    },
  },
}
</script>
