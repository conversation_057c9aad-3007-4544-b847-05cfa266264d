{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue", "mtime": 1750384136847}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}