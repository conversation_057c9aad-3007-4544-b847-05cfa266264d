import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";


//采集策略-查询接口
export async function tacticsSearch(params) {
  return request(`/pro/maaGatherTactics/maaGatherTacticsList?pageIndex=${params.pageIndex}&pageSize=${params.pageSize}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    // data: JSON.stringify(params),
  });
}

//采集策略-新增接口
export async function tacticsAdd(params) {
  return request(`/pro/maaGatherTactics/addMaaGatherTactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//采集策略-编辑接口
export async function tacticsUpdate(params) {
  return request(`/pro/maaGatherTactics/updateMaaGatherTactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}

//采集策略-删除接口
export async function tacticsDelete(params) {
  return request(`/pro/maaGatherTactics/deleteGatherTactics?ids=${params.idList}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    // data: JSON.stringify(params.idList),
  });
}
//采集策略-批量下发
export async function tacticsDistrib(params) {
  return request(`/pro/maaGatherTactics/distributeMaaGatherTactics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}










