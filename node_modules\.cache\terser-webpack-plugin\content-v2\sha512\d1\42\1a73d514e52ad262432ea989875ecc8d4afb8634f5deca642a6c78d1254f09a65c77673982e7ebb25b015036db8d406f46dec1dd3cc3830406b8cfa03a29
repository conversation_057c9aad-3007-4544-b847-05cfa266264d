{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-625cecf8\"],{\"1d5e\":function(e,t,a){\"use strict\";var n=a(\"5a2a\"),r=a.n(n);r.a},\"2ca0\":function(e,t,a){\"use strict\";var n=a(\"23e7\"),r=a(\"06cf\").f,i=a(\"50c4\"),o=a(\"5a34\"),s=a(\"1d80\"),c=a(\"ab13\"),u=a(\"c430\"),l=\"\".startsWith,p=Math.min,d=c(\"startsWith\"),g=!u&&!d&&!!function(){var e=r(String.prototype,\"startsWith\");return e&&!e.writable}();n({target:\"String\",proto:!0,forced:!g&&!d},{startsWith:function(e){var t=String(s(this));o(e);var a=i(p(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return l?l.call(t,n,a):t.slice(a,a+n.length)===n}})},\"3a86\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return i}));var n=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.offsetTop),t},r=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.clientHeight),t},i=function(e){try{var t=r(\".audit-container\")||r(\".router-container\"),a=n(\".el-table\"),i=48,o=t-a-i-20;return e>0?Math.max(o,300):300}catch(s){return console.error(\"计算表格高度出错:\",s),500}}},\"5a2a\":function(e,t,a){},\"5a34\":function(e,t,a){var n=a(\"44e7\");e.exports=function(e){if(n(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"68c3\":function(e,t,a){\"use strict\";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"log-container\"},[a(\"div\",{staticClass:\"log-content self-page self-position\"},[a(\"list-filter\",{ref:\"listFilter\",attrs:{items:e.filterItems,\"search-width\":\"25%\"},on:{reset:e.handleReset,search:e.handleSearch}}),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{data:e.list,height:e.tableHeight}},[a(\"el-table-column\",{attrs:{type:\"index\",label:\"序号\",width:\"62\"}}),a(\"el-table-column\",{attrs:{prop:\"create_time\",label:\"上报时间\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"function_name\",label:\"操作内容\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"user_id\",label:\"操作人\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.userId.filter((function(e){return e.key===t.row.user_id}))[0]?e.userId.filter((function(e){return e.key===t.row.user_id}))[0].value:\"\")+\" \")]}}])})],1),a(\"pagination\",e._b({staticClass:\"self-pagination\",attrs:{\"show-last-page\":!0,loading:e.countLoading},on:{change:e.handlePaginationChange,showSizeChange:e.handleShowSizeChange}},\"pagination\",e.pagination,!1))],1)])},r=[],i=(a(\"4de4\"),a(\"f3f3\")),o=(a(\"96cf\"),a(\"c964\")),s=a(\"20ae\"),c=a(\"6544\"),u=a(\"ee97\");function l(e){return Object(u[\"a\"])({url:\"/api/ieg/v1/system/user_operation_log\",method:\"get\",params:e})}function p(){return Object(u[\"a\"])({url:\"/api/ieg/v1/user/select_list_all\",method:\"get\"})}var d=a(\"3a86\"),g=a(\"d2c9\"),h=[{label:\"用户\",type:\"select\",formIndex:\"id\",options:[]},{label:\"搜索时间\",type:\"range-picker\",formIndex:\"timestamp\"}],f={name:\"SystemLog\",components:{ListFilter:s[\"a\"],Pagination:c[\"a\"]},data:function(){return{filterItems:h,list:[],countLoading:!1,total:1,searchList:{},loading:!1,userId:[],tableHeight:0,pagination:{current:1,pageSize:20,total:0,showSizeChanger:!0,pageSizeOptions:[\"10\",\"20\",\"50\",\"100\"]}}},mounted:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(g[\"a\"])();case 2:e.getSelectList(),e.getList(),window.addEventListener(\"resize\",(function(){e.tableHeight=Object(d[\"a\"])(e.pagination.total)}));case 5:case\"end\":return t.stop()}}),t)})))()},beforeDestroy:function(){var e=this;window.removeEventListener(\"resize\",(function(){e.tableHeight=Object(d[\"a\"])(e.pagination.total)}))},methods:{handleReset:function(){this.getList(),this.searchList={},this.pagination.current=1,this.pagination.pageSize=20},handleSearch:function(e){this.searchList={id:e.id,start_time:e.timestamp?e.timestamp[0]:void 0,end_time:e.timestamp?e.timestamp[1]:void 0},this.getList({page:1,limit:20,id:e.id,start_time:e.timestamp?e.timestamp[0]:void 0,end_time:e.timestamp?e.timestamp[1]:void 0}),this.pagination.current=1,this.pagination.pageSize=20},getSelectList:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,p();case 3:a=t.sent,0===a.code&&(e.filterItems[0].options=a.data.list.filter((function(e){return\"admin\"!==e.value})),e.userId=a.data.list),t.next=11;break;case 7:t.prev=7,t.t0=t[\"catch\"](0),console.error(\"获取用户列表失败:\",t.t0),e.$message.error(\"获取用户列表失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,7]])})))()},getList:function(e){var t=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function a(){var n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.loading=!0,a.prev=1,a.next=4,l(Object(i[\"a\"])({page:t.pagination.current,limit:t.pagination.pageSize},e||{}));case 4:n=a.sent,0===n.code&&(t.total=n.data.pagination.total,t.list=n.data.list,t.pagination=Object(i[\"a\"])(Object(i[\"a\"])({},t.pagination),{},{total:n.data.pagination.total}),t.$nextTick((function(){t.tableHeight=Object(d[\"a\"])(t.pagination.total)}))),a.next=12;break;case 8:a.prev=8,a.t0=a[\"catch\"](1),console.error(\"获取系统日志失败:\",a.t0),t.$message.error(\"获取系统日志失败\");case 12:return a.prev=12,t.loading=!1,a.finish(12);case 15:case\"end\":return a.stop()}}),a,null,[[1,8,12,15]])})))()},handlePaginationChange:function(e,t){this.pagination.current=e,this.pagination.pageSize=t,this.getList(this.searchList)},handleShowSizeChange:function(e,t){this.pagination.current=1,this.pagination.pageSize=t,this.getList(this.searchList)}}},m=f,b=(a(\"1d5e\"),a(\"2877\")),v=a(\"948f\"),k=a.n(v),w=Object(b[\"a\"])(m,n,r,!1,null,\"57b74960\",null);\"function\"===typeof k.a&&k()(w);t[\"default\"]=w.exports},\"948f\":function(e,t){},ab13:function(e,t,a){var n=a(\"b622\"),r=n(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[r]=!1,\"/./\"[e](t)}catch(n){}}return!1}},bae8:function(e,t,a){\"use strict\";a.d(t,\"b\",(function(){return r})),a.d(t,\"a\",(function(){return i}));var n=a(\"ee97\");function r(e){return Object(n[\"a\"])({url:\"/api/ieg/v1/login/login_in\",method:\"post\",data:e})}function i(){return Object(n[\"a\"])({url:\"/api/ieg/v1/system/info\",method:\"get\"})}},d2c9:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return s}));a(\"d3b7\"),a(\"25f0\"),a(\"96cf\");var n=a(\"c964\"),r=a(\"bae8\"),i=6e5;function o(){var e=localStorage.getItem(\"hg_token_timestamp\");if(!e)return!1;var t=(new Date).getTime(),a=parseInt(e),n=t-a;return n<i}function s(){return c.apply(this,arguments)}function c(){return c=Object(n[\"a\"])(regeneratorRuntime.mark((function e(){var t,a,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=localStorage.getItem(\"hg_token\"),!t){e.next=7;break}if(a=o(),!a){e.next=6;break}return console.log(\"本地token有效，无需重新登录\"),e.abrupt(\"return\",t);case 6:console.log(\"本地token已过期，需要重新登录\");case 7:return e.prev=7,e.next=10,Object(r[\"b\"])({name:\"admin\",password:\"123456\"});case 10:if(n=e.sent,!(n&&0===n.code&&n.data&&n.data.token)){e.next=18;break}return console.log(\"隐式登录成功，获取到新token\"),localStorage.setItem(\"hg_token\",n.data.token),localStorage.setItem(\"hg_token_timestamp\",(new Date).getTime().toString()),e.abrupt(\"return\",n.data.token);case 18:return console.error(\"隐式登录失败:\",n),e.abrupt(\"return\",\"\");case 20:e.next=26;break;case 22:return e.prev=22,e.t0=e[\"catch\"](7),console.error(\"隐式登录出错:\",e.t0),e.abrupt(\"return\",\"\");case 26:case\"end\":return e.stop()}}),e,null,[[7,22]])}))),c.apply(this,arguments)}},ee97:function(e,t,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var n=a(\"bc3a\"),r=a.n(n),i=a(\"4360\"),o=a(\"a18c\"),s=a(\"a47e\"),c=a(\"f7b5\"),u=a(\"f907\"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",n=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),l=n.NODE_ENV,p=n.VUE_APP_IS_MOCK,d=n.VUE_APP_BASE_API,g=\"true\"===p?\"\":d;\"production\"===l&&(g=\"\");var h={baseURL:g,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===l&&(h.timeout=a),t){case\"upload\":h.headers[\"Content-Type\"]=\"multipart/form-data\",h[\"processData\"]=!1,h[\"contentType\"]=!1;break;case\"download\":h[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var f=r.a.create(h);return f.interceptors.request.use((function(e){var t=i[\"a\"].getters.token;if(\"\"!==t&&e.url.startsWith(\"/api/ieg/\")){var a=localStorage.getItem(\"hg_token\");a&&(e.headers[\"authtoken\"]=a)}return e}),(function(e){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:e,print:!0}),Promise.reject(\"response-err:\"+e)})),f.interceptors.response.use((function(e){var a=void 0===e.headers[\"code\"]?200:Number(e.headers[\"code\"]),n=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){o[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"}))}))},r=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",n=arguments.length>2?arguments[2]:void 0,r=\"\";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(r=\"error\"),e.data.code>=2e3&&e.data.code<3e3&&(r=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(t),type:r}),Promise.reject(\"response-err-status:\".concat(n||u[\"a\"][a][t],\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.\".concat(a,\".\").concat(t))))};switch(e.data.code){case u[\"a\"].exception.system:t(\"system\");break;case u[\"a\"].exception.server:t(\"server\");break;case u[\"a\"].exception.session:n();break;case u[\"a\"].exception.access:n();break;case u[\"a\"].exception.certification:t(\"certification\");break;case u[\"a\"].exception.auth:t(\"auth\"),o[\"a\"].replace({path:\"/401\"});break;case u[\"a\"].exception.token:t(\"token\");break;case u[\"a\"].exception.param:t(\"param\");break;case u[\"a\"].exception.idempotency:t(\"idempotency\");break;case u[\"a\"].exception.ip:t(\"ip\"),i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"});break;case u[\"a\"].exception.upload:t(\"upload\");break;case u[\"a\"].attack.xss:t(\"xss\",\"attack\");break;default:t(\"code\",\"exception\",-1);break}};switch(t){case\"upload\":if(0===a)return e.data.data;r();break;case\"download\":if(0===a)return{data:e.data,fileName:decodeURI(e.headers[\"file-name\"])};r();break;default:if(0===e.data.code)return e.data;r();break}}),(function(e){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){o[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(i[\"a\"].dispatch(\"user/reset\"),o[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===t?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(s[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:\".concat(e,\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.service.timeout\"))))})),f(e)};t[\"a\"]=l}}]);", "extractedComments": []}