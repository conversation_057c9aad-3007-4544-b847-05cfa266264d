{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\SoftwareUpgrade.vue", "mtime": 1750059025270}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}