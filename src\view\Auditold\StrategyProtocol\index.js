import React, { useState, useEffect, createRef } from "react";
import { message, Modal, Button, Form, Row, Col, Icon, Select, Input } from "antd";
import SbrTable from "@/components/SbrTable";
import { tacticsPages } from "./services";
const tableRef = createRef();
const List = (props) => {
  const { getFieldDecorator, resetFields, validateFields } = props.form;

  // 列表数据
  const [tableList, setTableList] = useState([]);
  // 查询条件
  const [queryValue, setQueryValue] = useState({});
  const [loading, setLoading] = useState(false);
  useEffect(
    () => {
      getSourceData(true);
    },
    [queryValue]
  );
  // 查询列表
  const getSourceData = async (isSearch=false) => {
    try {
      setLoading(true);
      const res = await tacticsPages(isSearch?{
        pageIndex: 1,
        pageSize: 10,
        ...queryValue,
      }:{
        ...tableRef?.current?.getValue(),
        ...queryValue,
      });
      if (res.retcode == 0) {
        setTableList(res.data);
        setLoading(false);
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  }
  // 列表数据
  const columns = [
    {
      title: "协议名称",
      key: "protocolName",
      dataIndex: "protocolName",
      ellipsis: true,
      render: (text, record) => {
        return <><Icon type="star" theme="filled" style={{display: record.isIndcontrolType ==1 ? '': 'none', color:'#e6a23c'}}/>{record.protocolName}</>
      },
    },
    {
      title: "描述",
      key: "protocolDesc",
      dataIndex: "protocolDesc",
      ellipsis: true,
    },
    {
      title: "TCP端口",
      key: "tcpPort",
      dataIndex: "tcpPort",
      // render: (text, record) => {
      //   return '(' +text.tcpSrcPort +') + (' + text.tcpTarPort + ')'
      // },
    },
    {
      title: "UDP端口",
      key: "udpPort",
      dataIndex: "udpPort",
      // render: (text, record) => {
      //   return '(' +text.udpSrcPort +') + (' + text.udpTarPort + ')'
      // },
    },
    {
      title: "工控协议",
      key: "isIndcontrolType",
      dataIndex: "isIndcontrolType",
      width: 90,
      render: (text, record) => {
        return record.isIndcontrolType == 1 ? "是" : "否";
      },
    }
  ]

  // 条件查询
  const handleSearch = (e) => {
    e.preventDefault();
    validateFields((err, values) => {
        if (!err) {
          setQueryValue(values);
        }
      });
  };

  // 条件清除
  const handleReset = () => {
    resetFields();
  }

  return (
    <div>
      <Form
        className="searchBg"
        onSubmit={handleSearch}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <Row>
          <Col span={8}>
            <Form.Item label="工控协议">
              {getFieldDecorator("isIndcontrolType", {})(
                <Select>
                  <Option value={1}>工控协议</Option>
                  <Option value={0}>非工控协议</Option>                
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="协议名称/描述">
              {getFieldDecorator("nameOrDesc", {})(
                <Input maxLength={50} placeholder="请输入协议名称/描述" />
              )}
            </Form.Item>
          </Col>
          <Col span={8} className="searchBtn">
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button style={{ marginLeft: 15 }} onClick={handleReset}>
              清除
            </Button>
          </Col>
        </Row>
      </Form>
      <div className={`tableBg`}>
        <SbrTable
          columns={columns}
          scroll={false}
          tableList={tableList}
          getSourceData={getSourceData}
          style={{ wordWrap: "break-word", wordBreak: "break-all" }}
          //   size="middle"
          ref={tableRef}
          loading={loading}
        />
      </div>     
    </div>
  );
};

export default Form.create()(List);
