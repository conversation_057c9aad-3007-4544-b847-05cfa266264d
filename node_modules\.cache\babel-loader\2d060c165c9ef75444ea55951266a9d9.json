{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\view\\auditold\\StrategyCollection\\index.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\auditold\\StrategyCollection\\index.js", "mtime": 1715135144000}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}