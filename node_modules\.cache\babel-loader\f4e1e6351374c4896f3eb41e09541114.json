{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\DeviceList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\DeviceList\\index.vue", "mtime": 1750322914782}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}