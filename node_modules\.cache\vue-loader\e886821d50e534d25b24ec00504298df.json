{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue", "mtime": 1750387212981}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}