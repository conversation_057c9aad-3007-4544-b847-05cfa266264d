{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue", "mtime": 1750387721441}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}