{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue", "mtime": 1750386817622}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}