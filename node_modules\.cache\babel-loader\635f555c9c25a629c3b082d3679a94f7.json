{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\components\\UserManageModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\DeviceList\\components\\UserManageModal.vue", "mtime": 1750124063744}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}