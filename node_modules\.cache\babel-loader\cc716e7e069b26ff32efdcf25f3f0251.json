{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategySentine\\components\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\StrategySentine\\components\\DeviceComponent.vue", "mtime": 1750059151201}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}