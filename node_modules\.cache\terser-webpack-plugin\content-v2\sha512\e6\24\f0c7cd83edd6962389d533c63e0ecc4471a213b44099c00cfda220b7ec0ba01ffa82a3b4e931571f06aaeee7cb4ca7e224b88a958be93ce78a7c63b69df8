{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-85cf8d6e\"],{\"078a\":function(t,e,a){\"use strict\";var i=a(\"2b0e\"),r=(a(\"99af\"),a(\"caad\"),a(\"ac1f\"),a(\"2532\"),a(\"5319\"),{bind:function(t,e,a){var i=[t.querySelector(\".el-dialog__header\"),t.querySelector(\".el-dialog\")],r=i[0],s=i[1];r.style.cssText+=\";cursor:move;\",s.style.cssText+=\";top:0px;\";var n=function(){return window.document.currentStyle?function(t,e){return t.currentStyle[e]}:function(t,e){return getComputedStyle(t,!1)[e]}}();r.onmousedown=function(t){var e=[t.clientX-r.offsetLeft,t.clientY-r.offsetTop,s.offsetWidth,s.offsetHeight,document.body.clientWidth,document.body.clientHeight],i=e[0],o=e[1],l=e[2],c=e[3],u=e[4],d=e[5],p=[s.offsetLeft,u-s.offsetLeft-l,s.offsetTop,d-s.offsetTop-c],m=p[0],f=p[1],k=p[2],b=p[3],h=[n(s,\"left\"),n(s,\"top\")],g=h[0],v=h[1];g.includes(\"%\")?(g=+document.body.clientWidth*(+g.replace(/%/g,\"\")/100),v=+document.body.clientHeight*(+v.replace(/%/g,\"\")/100)):(g=+g.replace(/px/g,\"\"),v=+v.replace(/px/g,\"\")),document.onmousemove=function(t){var e=t.clientX-i,r=t.clientY-o;-e>m?e=-m:e>f&&(e=f),-r>k?r=-k:r>b&&(r=b),s.style.cssText+=\";left:\".concat(e+g,\"px;top:\").concat(r+v,\"px;\"),a.child.$emit(\"dragDialog\")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),s=function(t){t.directive(\"el-dialog-drag\",r)};window.Vue&&(window[\"el-dialog-drag\"]=r,i[\"default\"].use(s)),r.elDialogDrag=s;e[\"a\"]=r},2532:function(t,e,a){\"use strict\";var i=a(\"23e7\"),r=a(\"5a34\"),s=a(\"1d80\"),n=a(\"ab13\");i({target:\"String\",proto:!0,forced:!n(\"includes\")},{includes:function(t){return!!~String(s(this)).indexOf(r(t),arguments.length>1?arguments[1]:void 0)}})},\"2f43\":function(t,e,a){\"use strict\";var i=a(\"d906\"),r=a.n(i);r.a},\"5a34\":function(t,e,a){var i=a(\"44e7\");t.exports=function(t){if(i(t))throw TypeError(\"The method doesn't accept regular expressions\");return t}},ab13:function(t,e,a){var i=a(\"b622\"),r=i(\"match\");t.exports=function(t){var e=/./;try{\"/./\"[t](e)}catch(a){try{return e[r]=!1,\"/./\"[t](e)}catch(i){}}return!1}},c54a:function(t,e,a){\"use strict\";a.d(e,\"l\",(function(){return i})),a.d(e,\"m\",(function(){return r})),a.d(e,\"b\",(function(){return s})),a.d(e,\"c\",(function(){return n})),a.d(e,\"a\",(function(){return o})),a.d(e,\"j\",(function(){return l})),a.d(e,\"q\",(function(){return c})),a.d(e,\"d\",(function(){return u})),a.d(e,\"f\",(function(){return d})),a.d(e,\"g\",(function(){return p})),a.d(e,\"e\",(function(){return m})),a.d(e,\"n\",(function(){return f})),a.d(e,\"k\",(function(){return k})),a.d(e,\"p\",(function(){return b})),a.d(e,\"h\",(function(){return h})),a.d(e,\"i\",(function(){return g})),a.d(e,\"o\",(function(){return v}));a(\"ac1f\"),a(\"466d\"),a(\"1276\");function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=\"\";switch(e){case 0:a=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break;case 1:a=/^(?![_.\\-])(?!.*?[_.\\-]$)[a-zA-Z0-9_.\\-\\u4e00-\\u9fa5]+$/;break;case 2:a=/^(?![_./\\-])(?!.*?[_./\\-]$)[a-zA-Z0-9_./\\-\\u4e00-\\u9fa5]+$/;break;case 3:a=/^(?![_./\\-\\s])(?!.*?[_./\\-\\s]$)[a-zA-Z0-9_./\\-\\s\\u4e00-\\u9fa5]+$/;break;default:a=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break}return a.test(t)}function r(t){var e=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\\[\\].<>/?\\-%]).{0,}$/;return e.test(t)}function s(t){var e=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return e.test(t)}function n(t){var e=/^([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+\\.[a-zA-Z]{2,3}$/;return e.test(t)}function o(t){var e=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return e.test(t)}function l(t){for(var e=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,a=t.split(\",\"),i=0;i<a.length;i++)if(!e.test(a[i]))return!1;return!0}function c(t){var e=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return e.test(t)}function u(t){var e=/^(\\d{2,5}-)?\\d{6,9}(-\\d{2,4})?$/;return e.test(t)}function d(t){var e=/^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;return e.test(t)}function p(t){var e=/:/.test(t)&&t.match(/:/g).length<8&&/::/.test(t)?1===t.match(/::/g).length&&/^::$|^(::)?([\\da-f]{1,4}(:|::))*[\\da-f]{1,4}(:|::)?$/i.test(t):/^([\\da-f]{1,4}:){7}[\\da-f]{1,4}$/i.test(t);return e}function m(t){return d(t)||p(t)}function f(t){var e=/^([0-9]|[1-9][0-9]{0,4})$/;return e.test(t)}function k(t){for(var e=/^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(\\:([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,a=t.split(\",\"),i=0;i<a.length;i++)if(!e.test(a[i]))return!1;return!0}function b(t){var e=/^[^ ]+$/;return e.test(t)}function h(t){var e=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\\.[A-Fa-f0-9]{4}){2}$/;return e.test(t)}function g(t){var e=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return e.test(t)}function v(t){var e=/[^\\u4E00-\\u9FA5]/;return e.test(t)}},caad:function(t,e,a){\"use strict\";var i=a(\"23e7\"),r=a(\"4d64\").includes,s=a(\"44d2\"),n=a(\"ae40\"),o=n(\"indexOf\",{ACCESSORS:!0,1:0});i({target:\"Array\",proto:!0,forced:!o},{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),s(\"includes\")},d81d:function(t,e,a){\"use strict\";var i=a(\"23e7\"),r=a(\"b727\").map,s=a(\"1dde\"),n=a(\"ae40\"),o=s(\"map\"),l=n(\"map\");i({target:\"Array\",proto:!0,forced:!o||!l},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},d906:function(t,e,a){},f488:function(t,e,a){\"use strict\";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"router-wrap-table\"},[a(\"header\",{staticClass:\"table-header\"},[a(\"section\",{staticClass:\"table-header-main\"},[a(\"section\",{staticClass:\"table-header-search\"},[a(\"section\",{staticClass:\"table-header-search-input\"},[a(\"el-input\",{attrs:{\"prefix-icon\":\"soc-icon-search\",clearable:\"\",placeholder:t.$t(\"tip.placeholder.query\",[t.$t(\"report.task.label.name\")])},on:{change:t.clickQueryReportTaskTable},model:{value:t.data.fuzzyField,callback:function(e){t.$set(t.data,\"fuzzyField\",e)},expression:\"data.fuzzyField\"}})],1),a(\"section\",{staticClass:\"table-header-search-button\"},[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:t.clickQueryReportTaskTable}},[t._v(\" \"+t._s(t.$t(\"button.query\"))+\" \")])],1)]),a(\"section\",{staticClass:\"table-header-button\"},[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"upload\",expression:\"'upload'\"}],on:{click:t.clickBatchEnableReportTask}},[t._v(\" \"+t._s(t.$t(\"button.on\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"upload\",expression:\"'upload'\"}],on:{click:t.clickBatchDisableReportTask}},[t._v(\" \"+t._s(t.$t(\"button.off\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"add\",expression:\"'add'\"}],on:{click:t.clickAddReportTask}},[t._v(\" \"+t._s(t.$t(\"button.add\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"delete\",expression:\"'delete'\"}],on:{click:t.clickBatchDeleteReportTask}},[t._v(\" \"+t._s(t.$t(\"button.batch.delete\"))+\" \")])],1)])]),a(\"main\",{staticClass:\"table-body\"},[a(\"header\",{staticClass:\"table-body-header\"},[a(\"h2\",{staticClass:\"table-body-title\"},[t._v(\" \"+t._s(t.$t(\"report.task.name\"))+\" \")])]),a(\"main\",{staticClass:\"table-body-main\"},[a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.data.loading,expression:\"data.loading\"}],ref:\"reportTaskTable\",attrs:{data:t.data.table,\"element-loading-background\":\"rgba(0, 0, 0, 0.3)\",size:\"mini\",\"highlight-current-row\":\"\",\"tooltip-effect\":\"light\",height:\"100%\"},on:{\"current-change\":t.reportTaskTableRowChange,\"selection-change\":t.reportTaskTableSelectsChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"50\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:t.$t(\"report.task.label.name\"),prop:\"taskName\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:t.$t(\"report.task.label.type\"),prop:\"taskType\",\"show-overflow-tooltip\":\"\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(\" \"+t._s(e.row.taskTypeText+e.row.taskTimeText)+\" \")]}}])}),a(\"el-table-column\",{attrs:{label:t.$t(\"time.option.executeTime\"),prop:\"taskStartTime\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:t.$t(\"report.task.label.status\"),\"show-overflow-tooltip\":\"\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-switch\",{on:{change:function(a){return t.changeTaskStatus(e.row)}},model:{value:e.row.taskStatus,callback:function(a){t.$set(e.row,\"taskStatus\",a)},expression:\"scope.row.taskStatus\"}})]}}])}),a(\"el-table-column\",{attrs:{fixed:\"right\",width:\"210\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],staticClass:\"el-button--blue\",on:{click:function(a){return t.clickDetailReportTask(e.row)}}},[t._v(\" \"+t._s(t.$t(\"button.detail\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"update\",expression:\"'update'\"}],staticClass:\"el-button--blue\",on:{click:function(a){return t.clickUpdateReportTask(e.row)}}},[t._v(\" \"+t._s(t.$t(\"button.update\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"delete\",expression:\"'delete'\"}],staticClass:\"el-button--red\",on:{click:function(a){return t.clickDeleteReportTask(e.row)}}},[t._v(\" \"+t._s(t.$t(\"button.delete\"))+\" \")])]}}])})],1)],1)]),a(\"footer\",{staticClass:\"table-footer\"},[t.pagination.visible?a(\"el-pagination\",{attrs:{small:\"\",background:\"\",align:\"right\",\"current-page\":t.pagination.pageNum,\"page-sizes\":[10,20,50,100],\"page-size\":t.pagination.pageSize,layout:\"total, sizes, prev, pager, next, jumper\",total:t.pagination.total},on:{\"size-change\":t.reportTaskTableSizeChange,\"current-change\":t.reportTaskTableCurrentChange}}):t._e()],1),a(\"task-dialog\",{attrs:{visible:t.dialog.visible.add,title:t.dialog.title.add},on:{\"update:visible\":function(e){return t.$set(t.dialog.visible,\"add\",e)},\"on-submit\":t.clickSubmitAddTask}}),a(\"task-dialog\",{attrs:{visible:t.dialog.visible.update,title:t.dialog.title.update,recipient:t.dialog.form.update.taskRecipient},on:{\"update:visible\":function(e){return t.$set(t.dialog.visible,\"update\",e)},\"on-submit\":t.clickSubmitUpdateReportTask}}),a(\"task-dialog\",{attrs:{visible:t.dialog.visible.detail,title:t.dialog.title.detail,\"form-data\":t.dialog.form.detail},on:{\"update:visible\":function(e){return t.$set(t.dialog.visible,\"detail\",e)}}})],1)},r=[],s=(a(\"d81d\"),a(\"d3b7\"),a(\"ac1f\"),a(\"25f0\"),a(\"1276\"),a(\"96cf\"),a(\"c964\")),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"custom-dialog\",{ref:\"dialogTemp\",attrs:{visible:t.visible,title:t.title,width:t.width},on:{\"on-close\":t.clickCancelDialog,\"on-submit\":t.clickSubmitForm}},[0===Object.keys(t.formData).length?[a(\"el-form\",{ref:\"formTemp\",attrs:{model:t.form.model,rules:t.form.rule,\"label-width\":\"25%\"}},[\"\"===t.recipient?a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.name\"),prop:\"taskName\"}},[a(\"el-input\",{staticClass:\"width-small\",attrs:{maxlength:\"256\"},model:{value:t.form.model.taskName,callback:function(e){t.$set(t.form.model,\"taskName\",\"string\"===typeof e?e.trim():e)},expression:\"form.model.taskName\"}})],1):t._e(),\"\"===t.recipient?a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.instance\"),prop:\"taskInstance\"}},[a(\"el-select\",{staticClass:\"width-small\",attrs:{clearable:\"\",filterable:\"\"},model:{value:t.form.model.taskInstance,callback:function(e){t.$set(t.form.model,\"taskInstance\",e)},expression:\"form.model.taskInstance\"}},t._l(t.option.taskInstance,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e(),\"\"===t.recipient?a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.type\"),prop:\"taskType\"}},[a(\"el-select\",{staticClass:\"width-small\",attrs:{clearable:\"\"},on:{change:function(e){t.form.model.taskTimeValue=\"\"}},model:{value:t.form.model.taskType,callback:function(e){t.$set(t.form.model,\"taskType\",e)},expression:\"form.model.taskType\"}},t._l(t.option.taskType,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e(),\"\"===t.recipient&&\"\"!==t.form.model.taskType?a(\"el-form-item\",{attrs:{label:t.$t(\"time.option.startDate\"),prop:\"taskStartDate\"}},[a(\"el-date-picker\",{staticClass:\"width-small\",attrs:{clearable:\"\",type:\"date\",\"value-format\":\"yyyy-MM-dd\",format:\"yyyy-MM-dd\",placeholder:t.$t(\"time.option.startDate\"),\"picker-options\":t.disablePickerOption.taskStartDate},on:{change:t.changeTaskStartDate},model:{value:t.form.model.taskStartDate,callback:function(e){t.$set(t.form.model,\"taskStartDate\",e)},expression:\"form.model.taskStartDate\"}})],1):t._e(),\"\"===t.recipient&&\"\"!==t.form.model.taskType?a(\"el-form-item\",{attrs:{label:t.$t(\"time.option.startTime\"),prop:\"taskStartTime\"}},[a(\"el-time-picker\",{staticClass:\"width-small\",attrs:{type:\"time\",clearable:\"\",\"value-format\":\"HH:mm:ss\",format:\"HH:mm:ss\",placeholder:t.$t(\"time.option.startTime\")},model:{value:t.form.model.taskStartTime,callback:function(e){t.$set(t.form.model,\"taskStartTime\",e)},expression:\"form.model.taskStartTime\"}})],1):t._e(),\"\"===t.recipient&&\"month\"===t.form.model.taskType?a(\"el-form-item\",{attrs:{label:t.$t(\"time.cycle.month\"),prop:\"taskTimeValue\"}},[a(\"el-select\",{staticClass:\"width-small\",attrs:{clearable:\"\"},model:{value:t.form.model.taskTimeValue,callback:function(e){t.$set(t.form.model,\"taskTimeValue\",e)},expression:\"form.model.taskTimeValue\"}},t._l(31,(function(e){return a(\"el-option\",{key:e,attrs:{label:e+t.$t(\"time.unit.day\"),value:e}})})),1)],1):t._e(),\"\"===t.recipient&&\"week\"===t.form.model.taskType?a(\"el-form-item\",{attrs:{label:t.$t(\"time.cycle.week\"),prop:\"taskTimeValue\"}},[a(\"el-select\",{staticClass:\"width-small\",attrs:{clearable:\"\"},model:{value:t.form.model.taskTimeValue,callback:function(e){t.$set(t.form.model,\"taskTimeValue\",e)},expression:\"form.model.taskTimeValue\"}},t._l(t.option.week,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e(),\"\"===t.recipient?a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.sendType\"),prop:\"taskSendType\"}},[a(\"el-select\",{staticClass:\"width-small\",attrs:{clearable:\"\"},model:{value:t.form.model.taskSendType,callback:function(e){t.$set(t.form.model,\"taskSendType\",e)},expression:\"form.model.taskSendType\"}},t._l(t.option.sendType,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e(),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.recipient\"),prop:\"taskRecipient\"}},[a(\"el-input\",{staticClass:\"width-small\",attrs:{maxlength:\"64\"},model:{value:t.form.model.taskRecipient,callback:function(e){t.$set(t.form.model,\"taskRecipient\",e)},expression:\"form.model.taskRecipient\"}})],1),\"\"===t.recipient?a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.description\"),prop:\"taskDescription\"}},[a(\"el-input\",{staticClass:\"width-small\",attrs:{type:\"textarea\",maxlength:\"1024\"},model:{value:t.form.model.taskDescription,callback:function(e){t.$set(t.form.model,\"taskDescription\",e)},expression:\"form.model.taskDescription\"}})],1):t._e()],1)]:[a(\"el-form\",{ref:\"formTemp\",attrs:{\"label-width\":\"25%\"}},[a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.name\")}},[t._v(\" \"+t._s(t.formData.taskName)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.instance\")}},[t._v(\" \"+t._s(t.formData.taskInstance)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.status\")}},[t._v(\" \"+t._s(t.formData.taskStatus)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"time.option.startTime\")}},[t._v(\" \"+t._s(t.formData.taskStartDate+\" \"+t.formData.taskStartTime)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.type\")}},[t._v(\" \"+t._s(t.formData.taskType)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"time.option.executeTime\")}},[t._v(\" \"+t._s(t.formData.taskStartTime)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.sendType\")}},[t._v(\" \"+t._s(t.formData.taskSendType)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.recipient\")}},[t._v(\" \"+t._s(t.formData.taskRecipient)+\" \")]),a(\"el-form-item\",{attrs:{label:t.$t(\"report.task.label.description\")}},[t._v(\" \"+t._s(t.formData.taskDescription)+\" \")])],1),a(\"template\",{slot:\"action\"},[a(\"fragment\")],1)]],2)},o=[],l=a(\"d465\"),c=a(\"f7b5\"),u=a(\"c54a\"),d=a(\"4020\");function p(t){return Object(d[\"a\"])({url:\"/reporttask/catalogue\",method:\"post\",data:t||{}})}function m(t){return Object(d[\"a\"])({url:\"/reporttask/catalogue/\".concat(t),method:\"delete\"})}function f(t){return Object(d[\"a\"])({url:\"/reporttask/catalogue\",method:\"put\",data:t||{}})}function k(t,e){return Object(d[\"a\"])({url:\"/reporttask/catalogue/status\",method:\"put\",data:{taskEnableStatus:e,taskIds:t}})}function b(t){return Object(d[\"a\"])({url:\"/reporttask/catalogue\",method:\"get\",params:t||{}})}function h(t){return Object(d[\"a\"])({url:\"/reporttask/catalogue/\".concat(t),method:\"get\"})}function g(){return Object(d[\"a\"])({url:\"/reporttask/catalogue/instances\",method:\"get\"})}var v={components:{CustomDialog:l[\"a\"]},props:{visible:{required:!0,type:Boolean},title:{type:String,default:\"\"},width:{type:String,default:\"35%\"},recipient:{type:String,default:\"\"},formData:{type:Object,default:function(){return{}}}},data:function(){var t=this,e=function(e,a,i){\"\"===a?i(new Error(t.$t(\"validate.empty\"))):Object(u[\"c\"])(a)?i():i(new Error(t.$t(\"validate.comm.email\")))};return{dialogVisible:this.visible,disablePickerOption:{taskStartDate:{disabledDate:function(t){return t.getTime()<=Date.now()-864e5}}},option:{taskInstance:[],taskType:[{value:\"day\",label:this.$t(\"time.cycle.day\")},{value:\"week\",label:this.$t(\"time.cycle.week\")},{value:\"month\",label:this.$t(\"time.cycle.month\")}],week:[{label:this.$t(\"time.week.mon\"),value:1},{label:this.$t(\"time.week.tue\"),value:2},{label:this.$t(\"time.week.wed\"),value:3},{label:this.$t(\"time.week.thu\"),value:4},{label:this.$t(\"time.week.fri\"),value:5},{label:this.$t(\"time.week.sat\"),value:6},{label:this.$t(\"time.week.sun\"),value:0}],sendType:[{label:\"PDF\",value:\"pdf\"},{label:\"Word\",value:\"word\"},{label:\"Excel\",value:\"excel\"}]},form:{model:{taskName:\"\",taskInstance:\"\",taskType:\"\",taskStartDate:\"\",taskStartTime:\"\",taskTimeValue:\"\",updateDate:\"\",taskSendType:\"\",taskRecipient:\"\",taskDescription:\"\"},rule:{taskName:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],taskInstance:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"change\"}],taskStartDate:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],taskStartTime:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],taskTimeValue:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"change\"}],taskType:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"change\"}],taskSendType:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"change\"}],taskRecipient:[{required:!0,validator:e,trigger:\"blur\"}]}}}},watch:{visible:function(t){this.dialogVisible=t},dialogVisible:function(t){this.$emit(\"update:visible\",t)},recipient:function(t){this.form.model.taskRecipient=t}},mounted:function(){this.getTaskInstance()},methods:{clickCancelDialog:function(){this.$refs.formTemp.resetFields(),this.$refs.dialogTemp.end(),this.dialogVisible=!1},clickSubmitForm:function(){var t=this;this.$refs.formTemp.validate((function(e){e?t.$confirm(t.$t(\"tip.confirm.submit\"),t.$t(\"tip.confirm.tip\"),{closeOnClickModal:!1}).then((function(){t.$emit(\"on-submit\",Object.assign({},t.form.model)),t.clickCancelDialog()})):Object(c[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){return!1}))})),this.$refs.dialogTemp.end()},changeTaskStartDate:function(t){this.form.model.taskStartDate=t},getTaskInstance:function(){var t=this;g().then((function(e){t.option.taskInstance=e}))}}},T=v,y=(a(\"2f43\"),a(\"2877\")),$=Object(y[\"a\"])(T,n,o,!1,null,\"1f027814\",null),w=$.exports,S=a(\"13c3\"),_={name:\"ReportTask\",components:{TaskDialog:w},data:function(){return{data:{loading:!1,debounce:null,table:[],selected:[],fuzzyField:\"\"},pagination:{visible:!0,pageSize:this.$store.getters.pageSize,pageNum:1,total:0,currentRow:{}},dialog:{visible:{add:!1,update:!1,detail:!1},title:{add:this.$t(\"dialog.title.add\",[this.$t(\"report.task.name\")]),update:this.$t(\"dialog.title.update\",[this.$t(\"report.task.name\")]),detail:this.$t(\"dialog.title.detail\",[this.$t(\"report.task.name\")])},form:{update:{taskId:\"\",taskRecipient:\"\"},detail:{taskName:\"\",taskInstance:\"\",taskType:\"\",taskStartDate:\"\",taskStartTime:\"\",taskTimeValue:\"\",updateDate:\"\",taskSendType:\"\",taskRecipient:\"\",taskStatus:\"\",taskDescription:\"\"}}}}},mounted:function(){this.initDebounceQuery(),this.getReportTaskTableData()},methods:{clickBatchEnableReportTask:function(){if(this.data.selected.length>0){var t=this.data.selected.map((function(t){return t.taskId}));this.updateReportTaskStatus(t,1)}else Object(c[\"a\"])({i18nCode:\"tip.enable.prompt\",type:\"warning\",print:!0})},clickBatchDisableReportTask:function(){if(this.data.selected.length>0){var t=this.data.selected.map((function(t){return t.taskId}));this.updateReportTaskStatus(t,0)}else Object(c[\"a\"])({i18nCode:\"tip.disable.prompt\",type:\"warning\",print:!0})},clickAddReportTask:function(){this.dialog.visible.add=!0},clickBatchDeleteReportTask:function(){if(this.data.selected.length>0){var t=this.data.selected.map((function(t){return t.taskId})).toString();this.deleteReportTask(t)}else Object(c[\"a\"])({i18nCode:\"tip.delete.prompt\",type:\"warning\",print:!0})},clickDetailReportTask:function(t){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.getReportTaskDetail(t.taskId);case 2:e.dialog.visible.detail=!0;case 3:case\"end\":return a.stop()}}),a)})))()},clickUpdateReportTask:function(t){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.getReportTaskDetail(t.taskId);case 2:e.dialog.form.update.taskId=t.taskId,e.dialog.form.update.taskRecipient=e.dialog.form.detail.taskRecipient,e.dialog.visible.update=!0;case 5:case\"end\":return a.stop()}}),a)})))()},clickDeleteReportTask:function(t){this.deleteReportTask(t.taskId)},clickSubmitAddTask:function(t){this.addReportTask({taskName:t.taskName,taskInstance:t.taskInstance,taskType:t.taskType,taskStartDate:t.taskStartDate,taskStartTime:t.taskStartTime,taskTimeValue:t.taskTimeValue,taskSendType:t.taskSendType,taskRecipient:t.taskRecipient,taskDescription:t.taskDescription})},clickSubmitUpdateReportTask:function(t){this.updateReportTask({taskId:this.dialog.form.update.taskId,taskRecipient:t.taskRecipient})},clickQueryReportTaskTable:function(){this.data.debounce()},changeTaskStatus:function(t){this.updateReportTaskStatus([t.taskId],t.taskStatus?1:0)},reportTaskTableRowChange:function(t){this.pagination.currentRow=t},reportTaskTableSelectsChange:function(t){this.data.selected=t},reportTaskTableSizeChange:function(t){this.pagination.pageSize=t,this.getReportTaskTableData()},reportTaskTableCurrentChange:function(t){this.pagination.pageNum=t,this.getReportTaskTableData()},initDebounceQuery:function(){var t=this;this.data.debounce=Object(S[\"a\"])((function(){t.pagination.pageNum=1,t.getReportTaskTableData()}),500)},addReportTask:function(t){var e=this;p(t).then((function(t){1===t?Object(c[\"a\"])({i18nCode:\"tip.add.success\",type:\"success\"},(function(){e.getReportTaskTableData()})):2===t?Object(c[\"a\"])({i18nCode:\"tip.add.repeat\",type:\"error\"}):Object(c[\"a\"])({i18nCode:\"tip.add.error\",type:\"error\"})}))},deleteReportTask:function(t){var e=this;this.$confirm(this.$t(\"report.task.tip.delete\"),this.$t(\"tip.confirm.tip\"),{closeOnClickModal:!1}).then((function(){m(t).then((function(a){a?Object(c[\"a\"])({i18nCode:\"tip.delete.success\",type:\"success\"},(function(){var a=[e.pagination.pageNum,t.split(\",\")],i=a[0],r=a[1];r.length===e.data.table.length&&(e.pagination.pageNum=1===i?1:i-1),e.getReportTaskTableData()})):Object(c[\"a\"])({i18nCode:\"tip.delete.error\",type:\"error\"})}))}))},updateReportTask:function(t){var e=this;f(t).then((function(t){t?Object(c[\"a\"])({i18nCode:\"tip.update.success\",type:\"success\"},(function(){e.getReportTaskTableData()})):Object(c[\"a\"])({i18nCode:\"tip.update.error\",type:\"error\"})}))},updateReportTaskStatus:function(t,e){var a=this;k(t,e).then((function(t){t?(1===e&&Object(c[\"a\"])({i18nCode:\"tip.enable.success\",type:\"success\"},(function(){a.getReportTaskTableData()})),0===e&&Object(c[\"a\"])({i18nCode:\"tip.disable.success\",type:\"success\"},(function(){a.getReportTaskTableData()}))):(1===e&&Object(c[\"a\"])({i18nCode:\"tip.enable.error\",type:\"success\"}),0===e&&Object(c[\"a\"])({i18nCode:\"tip.disable.error\",type:\"success\"}))}))},getReportTaskTableData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{fuzzyField:this.data.fuzzyField,pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.pagination.visible=!1,this.data.loading=!0,b(e).then((function(e){t.data.table=e.rows,t.pagination.total=e.total,t.pagination.visible=!0,t.data.loading=!1}))},getReportTaskDetail:function(t){var e=this;return Object(s[\"a\"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,h(t).then((function(t){e.dialog.form.detail.taskName=t.taskName,e.dialog.form.detail.taskInstance=t.taskInstanceName,e.dialog.form.detail.taskType=t.taskTypeText+t.taskTimeText,e.dialog.form.detail.taskStartDate=t.taskStartDate,e.dialog.form.detail.taskStartTime=t.taskStartTime,e.dialog.form.detail.taskStartValue=t.taskStartValue,e.dialog.form.detail.updateDate=t.updateDate,e.dialog.form.detail.taskSendType=t.taskSendType,e.dialog.form.detail.taskStatus=t.taskStatus?e.$t(\"button.on\"):e.$t(\"button.off\"),e.dialog.form.detail.taskRecipient=t.taskRecipient,e.dialog.form.detail.taskDescription=t.taskDescription}));case 2:case\"end\":return a.stop()}}),a)})))()}}},D=_,R=Object(y[\"a\"])(D,i,r,!1,null,null,null);e[\"default\"]=R.exports}}]);", "extractedComments": []}