{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\StrategyRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\StrategyRecord.vue", "mtime": 1750125537773}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}