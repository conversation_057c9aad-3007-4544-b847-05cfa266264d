{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\VirusSentineLibrary.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\VirusSentineLibrary.vue", "mtime": 1749799444521}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}