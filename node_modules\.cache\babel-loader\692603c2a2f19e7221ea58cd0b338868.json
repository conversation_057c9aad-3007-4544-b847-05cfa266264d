{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyFilter\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyFilter\\index.vue", "mtime": 1750323702933}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}