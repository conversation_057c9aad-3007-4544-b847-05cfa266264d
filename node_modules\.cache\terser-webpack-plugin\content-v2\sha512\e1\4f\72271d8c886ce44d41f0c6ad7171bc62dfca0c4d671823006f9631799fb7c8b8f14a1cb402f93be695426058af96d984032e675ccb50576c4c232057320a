{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-22e75f47\",\"chunk-3e856590\",\"chunk-20f1c03d\"],{\"0122\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return n}));r(\"a4d3\"),r(\"e01a\"),r(\"d28b\"),r(\"d3b7\"),r(\"3ca3\"),r(\"ddb0\");function n(t){return n=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},n(t)}},\"04f6\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return f}));var n=32,i=7;function o(t){var e=0;while(t>=n)e|=1&t,t>>=1;return t+e}function a(t,e,r,n){var i=e+1;if(i===r)return 1;if(n(t[i++],t[e])<0){while(i<r&&n(t[i],t[i-1])<0)i++;s(t,e,i)}else while(i<r&&n(t[i],t[i-1])>=0)i++;return i-e}function s(t,e,r){r--;while(e<r){var n=t[e];t[e++]=t[r],t[r--]=n}}function u(t,e,r,n,i){for(n===e&&n++;n<r;n++){var o,a=t[n],s=e,u=n;while(s<u)o=s+u>>>1,i(a,t[o])<0?u=o:s=o+1;var c=n-s;switch(c){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(c>0)t[s+c]=t[s+c-1],c--}t[s]=a}}function c(t,e,r,n,i,o){var a=0,s=0,u=1;if(o(t,e[r+i])>0){s=n-i;while(u<s&&o(t,e[r+i+u])>0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}else{s=i+1;while(u<s&&o(t,e[r+i-u])<=0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s);var c=a;a=i-u,u=i-c}a++;while(a<u){var h=a+(u-a>>>1);o(t,e[r+h])>0?a=h+1:u=h}return u}function h(t,e,r,n,i,o){var a=0,s=0,u=1;if(o(t,e[r+i])<0){s=i+1;while(u<s&&o(t,e[r+i-u])<0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s);var c=a;a=i-u,u=i-c}else{s=n-i;while(u<s&&o(t,e[r+i+u])>=0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}a++;while(a<u){var h=a+(u-a>>>1);o(t,e[r+h])<0?u=h:a=h+1}return u}function l(t,e){var r,n,o=i,a=0,s=[];function u(t,e){r[a]=t,n[a]=e,a+=1}function l(){while(a>1){var t=a-2;if(t>=1&&n[t-1]<=n[t]+n[t+1]||t>=2&&n[t-2]<=n[t]+n[t-1])n[t-1]<n[t+1]&&t--;else if(n[t]>n[t+1])break;d(t)}}function f(){while(a>1){var t=a-2;t>0&&n[t-1]<n[t+1]&&t--,d(t)}}function d(i){var o=r[i],s=n[i],u=r[i+1],l=n[i+1];n[i]=s+l,i===a-3&&(r[i+1]=r[i+2],n[i+1]=n[i+2]),a--;var f=h(t[u],t,o,s,0,e);o+=f,s-=f,0!==s&&(l=c(t[o+s-1],t,u,l,l-1,e),0!==l&&(s<=l?p(o,s,u,l):v(o,s,u,l)))}function p(r,n,a,u){var l=0;for(l=0;l<n;l++)s[l]=t[r+l];var f=0,d=a,p=r;if(t[p++]=t[d++],0!==--u)if(1!==n){var v,y,g,b=o;while(1){v=0,y=0,g=!1;do{if(e(t[d],s[f])<0){if(t[p++]=t[d++],y++,v=0,0===--u){g=!0;break}}else if(t[p++]=s[f++],v++,y=0,1===--n){g=!0;break}}while((v|y)<b);if(g)break;do{if(v=h(t[d],s,f,n,0,e),0!==v){for(l=0;l<v;l++)t[p+l]=s[f+l];if(p+=v,f+=v,n-=v,n<=1){g=!0;break}}if(t[p++]=t[d++],0===--u){g=!0;break}if(y=c(s[f],t,d,u,0,e),0!==y){for(l=0;l<y;l++)t[p+l]=t[d+l];if(p+=y,d+=y,u-=y,0===u){g=!0;break}}if(t[p++]=s[f++],1===--n){g=!0;break}b--}while(v>=i||y>=i);if(g)break;b<0&&(b=0),b+=2}if(o=b,o<1&&(o=1),1===n){for(l=0;l<u;l++)t[p+l]=t[d+l];t[p+u]=s[f]}else{if(0===n)throw new Error;for(l=0;l<n;l++)t[p+l]=s[f+l]}}else{for(l=0;l<u;l++)t[p+l]=t[d+l];t[p+u]=s[f]}else for(l=0;l<n;l++)t[p+l]=s[f+l]}function v(r,n,a,u){var l=0;for(l=0;l<u;l++)s[l]=t[a+l];var f=r+n-1,d=u-1,p=a+u-1,v=0,y=0;if(t[p--]=t[f--],0!==--n)if(1!==u){var g=o;while(1){var b=0,_=0,m=!1;do{if(e(s[d],t[f])<0){if(t[p--]=t[f--],b++,_=0,0===--n){m=!0;break}}else if(t[p--]=s[d--],_++,b=0,1===--u){m=!0;break}}while((b|_)<g);if(m)break;do{if(b=n-h(s[d],t,r,n,n-1,e),0!==b){for(p-=b,f-=b,n-=b,y=p+1,v=f+1,l=b-1;l>=0;l--)t[y+l]=t[v+l];if(0===n){m=!0;break}}if(t[p--]=s[d--],1===--u){m=!0;break}if(_=u-c(t[f],s,0,u,u-1,e),0!==_){for(p-=_,d-=_,u-=_,y=p+1,v=d+1,l=0;l<_;l++)t[y+l]=s[v+l];if(u<=1){m=!0;break}}if(t[p--]=t[f--],0===--n){m=!0;break}g--}while(b>=i||_>=i);if(m)break;g<0&&(g=0),g+=2}if(o=g,o<1&&(o=1),1===u){for(p-=n,f-=n,y=p+1,v=f+1,l=n-1;l>=0;l--)t[y+l]=t[v+l];t[p]=s[d]}else{if(0===u)throw new Error;for(v=p-(u-1),l=0;l<u;l++)t[v+l]=s[l]}}else{for(p-=n,f-=n,y=p+1,v=f+1,l=n-1;l>=0;l--)t[y+l]=t[v+l];t[p]=s[d]}else for(v=p-(u-1),l=0;l<u;l++)t[v+l]=s[l]}return r=[],n=[],{mergeRuns:l,forceMergeRuns:f,pushRun:u}}function f(t,e,r,i){r||(r=0),i||(i=t.length);var s=i-r;if(!(s<2)){var c=0;if(s<n)return c=a(t,r,i,e),void u(t,r,i,r+c,e);var h=l(t,e),f=o(s);do{if(c=a(t,r,i,e),c<f){var d=s;d>f&&(d=f),u(t,r,r+d,r+c,e),c=d}h.pushRun(r,c),h.mergeRuns(),s-=c,r+=c}while(0!==s);h.forceMergeRuns()}}},\"04fc\":function(t,e,r){},\"0655\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return a}));var n=r(\"8728\"),i=1e-8;function o(t,e){return Math.abs(t-e)<i}function a(t,e,r){var i=0,a=t[0];if(!a)return!1;for(var s=1;s<t.length;s++){var u=t[s];i+=Object(n[\"a\"])(a[0],a[1],u[0],u[1],e,r),a=u}var c=t[0];return o(a[0],c[0])&&o(a[1],c[1])||(i+=Object(n[\"a\"])(a[0],a[1],c[0],c[1],e,r)),0!==i}},\"0698\":function(t,e,r){\"use strict\";var n=r(\"2cf4c\"),i=r(\"6d8b\"),o=r(\"21a1\"),a=r(\"6fd3\"),s=r(\"3437\"),u=r(\"5210\"),c=r(\"9850\"),h=r(\"4bc4\"),l=r(\"726e\");function f(t,e,r){var n=l[\"d\"].createCanvas(),i=e.getWidth(),o=e.getHeight(),a=n.style;return a&&(a.position=\"absolute\",a.left=\"0\",a.top=\"0\",a.width=i+\"px\",a.height=o+\"px\",n.setAttribute(\"data-zr-dom-id\",t)),n.width=i*r,n.height=o*r,n}var d=function(t){function e(e,r,o){var a,s=t.call(this)||this;s.motionBlur=!1,s.lastFrameAlpha=.7,s.dpr=1,s.virtual=!1,s.config={},s.incremental=!1,s.zlevel=0,s.maxRepaintRectCount=5,s.__dirty=!0,s.__firstTimePaint=!0,s.__used=!1,s.__drawIndex=0,s.__startIndex=0,s.__endIndex=0,s.__prevStartIndex=null,s.__prevEndIndex=null,o=o||n[\"e\"],\"string\"===typeof e?a=f(e,r,o):i[\"A\"](e)&&(a=e,e=a.id),s.id=e,s.dom=a;var u=a.style;return u&&(i[\"j\"](a),a.onselectstart=function(){return!1},u.padding=\"0\",u.margin=\"0\",u.borderWidth=\"0\"),s.painter=r,s.dpr=o,s}return Object(o[\"a\"])(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext(\"2d\"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=f(\"back-\"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext(\"2d\"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,r,n){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var i,o=[],a=this.maxRepaintRectCount,s=!1,u=new c[\"a\"](0,0,0,0);function l(t){if(t.isFinite()&&!t.isZero())if(0===o.length){var e=new c[\"a\"](0,0,0,0);e.copy(t),o.push(e)}else{for(var r=!1,n=1/0,i=0,h=0;h<o.length;++h){var l=o[h];if(l.intersect(t)){var f=new c[\"a\"](0,0,0,0);f.copy(l),f.union(t),o[h]=f,r=!0;break}if(s){u.copy(t),u.union(l);var d=t.width*t.height,p=l.width*l.height,v=u.width*u.height,y=v-d-p;y<n&&(n=y,i=h)}}if(s&&(o[i].union(t),r=!0),!r){e=new c[\"a\"](0,0,0,0);e.copy(t),o.push(e)}s||(s=o.length>=a)}}for(var f=this.__startIndex;f<this.__endIndex;++f){var d=t[f];if(d){var p=d.shouldBePainted(r,n,!0,!0),v=d.__isRendered&&(d.__dirty&h[\"a\"]||!p)?d.getPrevPaintRect():null;v&&l(v);var y=p&&(d.__dirty&h[\"a\"]||!d.__isRendered)?d.getPaintRect():null;y&&l(y)}}for(f=this.__prevStartIndex;f<this.__prevEndIndex;++f){d=e[f],p=d&&d.shouldBePainted(r,n,!0,!0);if(d&&(!p||!d.__zr)&&d.__isRendered){v=d.getPrevPaintRect();v&&l(v)}}do{i=!1;for(f=0;f<o.length;)if(o[f].isZero())o.splice(f,1);else{for(var g=f+1;g<o.length;)o[f].intersect(o[g])?(i=!0,o[f].union(o[g]),o.splice(g,1)):g++;f++}}while(i);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var r=this.dpr,n=this.dom,i=n.style,o=this.domBack;i&&(i.width=t+\"px\",i.height=e+\"px\"),n.width=t*r,n.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},e.prototype.clear=function(t,e,r){var n=this.dom,o=this.ctx,a=n.width,c=n.height;e=e||this.clearColor;var h=this.motionBlur&&!t,l=this.lastFrameAlpha,f=this.dpr,d=this;h&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation=\"copy\",this.ctxBack.drawImage(n,0,0,a/f,c/f));var p=this.domBack;function v(t,r,n,a){if(o.clearRect(t,r,n,a),e&&\"transparent\"!==e){var c=void 0;if(i[\"x\"](e)){var v=e.global||e.__width===n&&e.__height===a;c=v&&e.__canvasGradient||Object(s[\"a\"])(o,e,{x:0,y:0,width:n,height:a}),e.__canvasGradient=c,e.__width=n,e.__height=a}else i[\"y\"](e)&&(e.scaleX=e.scaleX||f,e.scaleY=e.scaleY||f,c=Object(u[\"c\"])(o,e,{dirty:function(){d.setUnpainted(),d.painter.refresh()}}));o.save(),o.fillStyle=c||e,o.fillRect(t,r,n,a),o.restore()}h&&(o.save(),o.globalAlpha=l,o.drawImage(p,t,r,n,a),o.restore())}!r||h?v(0,0,a,c):r.length&&i[\"k\"](r,(function(t){v(t.x*f,t.y*f,t.width*f,t.height*f)}))},e}(a[\"a\"]),p=d,v=r(\"98b7\"),y=r(\"22d1\"),g=1e5,b=314159,_=.01,m=.001;function x(t){return!!t&&(!!t.__builtin__||\"function\"===typeof t.resize&&\"function\"===typeof t.refresh)}function w(t,e){var r=document.createElement(\"div\");return r.style.cssText=[\"position:relative\",\"width:\"+t+\"px\",\"height:\"+e+\"px\",\"padding:0\",\"margin:0\",\"border-width:0\"].join(\";\")+\";\",r}var O=function(){function t(t,e,r,o){this.type=\"canvas\",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type=\"canvas\";var a=!t.nodeName||\"CANVAS\"===t.nodeName.toUpperCase();this._opts=r=i[\"m\"]({},r||{}),this.dpr=r.devicePixelRatio||n[\"e\"],this._singleCanvas=a,this.root=t;var u=t.style;u&&(i[\"j\"](t),t.innerHTML=\"\"),this.storage=e;var c=this._zlevelList;this._prevDisplayList=[];var h=this._layers;if(a){var l=t,f=l.width,d=l.height;null!=r.width&&(f=r.width),null!=r.height&&(d=r.height),this.dpr=r.devicePixelRatio||1,l.width=f*this.dpr,l.height=d*this.dpr,this._width=f,this._height=d;var v=new p(l,this,this.dpr);v.__builtin__=!0,v.initContext(),h[b]=v,v.zlevel=b,c.push(b),this._domRoot=t}else{this._width=Object(s[\"b\"])(t,0,r),this._height=Object(s[\"b\"])(t,1,r);var y=this._domRoot=w(this._width,this._height);t.appendChild(y)}}return t.prototype.getType=function(){return\"canvas\"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var i=0;i<n.length;i++){var o=n[i],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===i?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var n,i={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r||(r=this._hoverlayer=this.getLayer(g)),n||(n=r.ctx,n.save()),Object(u[\"a\"])(n,a,i,o===e-1))}n&&n.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(g)},t.prototype.paintOne=function(t,e){Object(u[\"b\"])(t,e)},t.prototype._paintList=function(t,e,r,n){if(this._redrawId===n){r=r||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e,r),o=i.finished,a=i.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;Object(v[\"a\"])((function(){s._paintList(t,e,r,n)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(b).ctx,e=this._domRoot.width,r=this._domRoot.height;t.clearRect(0,0,e,r),this.eachBuiltinLayer((function(n){n.virtual&&t.drawImage(n.dom,0,0,e,r)}))},t.prototype._doPaintList=function(t,e,r){for(var n=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],c=this._layers[u];c.__builtin__&&c!==this._hoverlayer&&(c.__dirty||r)&&o.push(c)}for(var h=!0,l=!1,f=function(i){var s,u=o[i],c=u.ctx,f=a&&u.createRepaintRects(t,e,d._width,d._height),p=r?u.__startIndex:u.__drawIndex,v=!r&&u.incremental&&Date.now,y=v&&Date.now(),g=u.zlevel===d._zlevelList[0]?d._backgroundColor:null;if(u.__startIndex===u.__endIndex)u.clear(!1,g,f);else if(p===u.__startIndex){var b=t[p];b.incremental&&b.notClear&&!r||u.clear(!1,g,f)}-1===p&&(console.error(\"For some unknown reason. drawIndex is -1\"),p=u.__startIndex);var _=function(e){var r={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(s=p;s<u.__endIndex;s++){var i=t[s];if(i.__inHover&&(l=!0),n._doPaintEl(i,u,a,e,r,s===u.__endIndex-1),v){var o=Date.now()-y;if(o>15)break}}r.prevElClipPaths&&c.restore()};if(f)if(0===f.length)s=u.__endIndex;else for(var m=d.dpr,x=0;x<f.length;++x){var w=f[x];c.save(),c.beginPath(),c.rect(w.x*m,w.y*m,w.width*m,w.height*m),c.clip(),_(w),c.restore()}else c.save(),_(),c.restore();u.__drawIndex=s,u.__drawIndex<u.__endIndex&&(h=!1)},d=this,p=0;p<o.length;p++)f(p);return y[\"a\"].wxa&&i[\"k\"](this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:l}},t.prototype._doPaintEl=function(t,e,r,n,i,o){var a=e.ctx;if(r){var s=t.getPaintRect();(!n||s&&s.intersect(n))&&(Object(u[\"a\"])(a,t,i,o),t.setPrevPaintRect(s))}else Object(u[\"a\"])(a,t,i,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=b);var r=this._layers[t];return r||(r=new p(\"zr_\"+t,this,this.dpr),r.zlevel=t,r.__builtin__=!0,this._layerConfig[t]?i[\"I\"](r,this._layerConfig[t],!0):this._layerConfig[t-_]&&i[\"I\"](r,this._layerConfig[t-_],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},t.prototype.insertLayer=function(t,e){var r=this._layers,n=this._zlevelList,i=n.length,o=this._domRoot,a=null,s=-1;if(!r[t]&&x(e)){if(i>0&&t>n[0]){for(s=0;s<i-1;s++)if(n[s]<t&&n[s+1]>t)break;a=r[n[s]]}if(n.splice(s+1,0,t),r[t]=e,!e.virtual)if(a){var u=a.dom;u.nextSibling?o.insertBefore(e.dom,u.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,n=0;n<r.length;n++){var i=r[n];t.call(e,this._layers[i],i)}},t.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,n=0;n<r.length;n++){var i=r[n],o=this._layers[i];o.__builtin__&&t.call(e,o,i)}},t.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,n=0;n<r.length;n++){var i=r[n],o=this._layers[i];o.__builtin__||t.call(e,o,i)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){s&&(s.__endIndex!==t&&(s.__dirty=!0),s.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var r=1;r<t.length;r++){var n=t[r];if(n.zlevel!==t[r-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var o,a,s=null,u=0;for(a=0;a<t.length;a++){n=t[a];var c=n.zlevel,l=void 0;o!==c&&(o=c,u=0),n.incremental?(l=this.getLayer(c+m,this._needsManuallyCompositing),l.incremental=!0,u=1):l=this.getLayer(c+(u>0?_:0),this._needsManuallyCompositing),l.__builtin__||i[\"G\"](\"ZLevel \"+c+\" has been used by unkown layer \"+l.id),l!==s&&(l.__used=!0,l.__startIndex!==a&&(l.__dirty=!0),l.__startIndex=a,l.incremental?l.__drawIndex=-1:l.__drawIndex=a,e(a),s=l),n.__dirty&h[\"a\"]&&!n.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=a))}e(a),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,i[\"k\"](this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?i[\"I\"](r[t],e,!0):r[t]=e;for(var n=0;n<this._zlevelList.length;n++){var o=this._zlevelList[n];if(o===t||o===t+_){var a=this._layers[o];i[\"I\"](a,r[t],!0)}}}},t.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],r.splice(i[\"r\"](r,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display=\"none\";var n=this._opts,i=this.root;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=Object(s[\"b\"])(i,0,n),e=Object(s[\"b\"])(i,1,n),r.style.display=\"\",this._width!==t||e!==this._height){for(var o in r.style.width=t+\"px\",r.style.height=e+\"px\",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(b).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML=\"\",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[b].dom;var e=new p(\"image\",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height;this.eachLayer((function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,c=a.length;s<c;s++){var h=a[s];Object(u[\"a\"])(r,h,o,s===c-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();e[\"a\"]=O},\"06ad\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return _}));var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1,n=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=n/4):e=n*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/n))},elasticOut:function(t){var e,r=.1,n=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=n/4):e=n*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/n)+1)},elasticInOut:function(t){var e,r=.1,n=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=n/4):e=n*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/n)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}},i=n,o=r(\"6d8b\"),a=r(\"b362\"),s=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||o[\"L\"],this.ondestroy=t.ondestroy||o[\"L\"],this.onrestart=t.onrestart||o[\"L\"],t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,n=t-this._startTime-this._pausedTime,i=n/r;i<0&&(i=0),i=Math.min(i,1);var o=this.easingFunc,a=o?o(i):i;if(this.onframe(a),1===i){if(!this.loop)return!0;var s=n%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=Object(o[\"w\"])(t)?t:i[t]||Object(a[\"a\"])(t)},t}(),u=s,c=r(\"41ef\"),h=r(\"7a29\"),l=Array.prototype.slice;function f(t,e,r){return(e-t)*r+t}function d(t,e,r,n){for(var i=e.length,o=0;o<i;o++)t[o]=f(e[o],r[o],n);return t}function p(t,e,r,n){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=f(e[a][s],r[a][s],n)}return t}function v(t,e,r,n){for(var i=e.length,o=0;o<i;o++)t[o]=e[o]+r[o]*n;return t}function y(t,e,r,n){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*n}return t}function g(t,e){for(var r=t.length,n=e.length,i=r>n?e:t,o=Math.min(r,n),a=i[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,n);s++)i.push({offset:a.offset,color:a.color.slice()})}function b(t,e,r){var n=t,i=e;if(n.push&&i.push){var o=n.length,a=i.length;if(o!==a){var s=o>a;if(s)n.length=a;else for(var u=o;u<a;u++)n.push(1===r?i[u]:l.call(i[u]))}var c=n[0]&&n[0].length;for(u=0;u<n.length;u++)if(1===r)isNaN(n[u])&&(n[u]=i[u]);else for(var h=0;h<c;h++)isNaN(n[u][h])&&(n[u][h]=i[u][h])}}function _(t){if(Object(o[\"u\"])(t)){var e=t.length;if(Object(o[\"u\"])(t[0])){for(var r=[],n=0;n<e;n++)r.push(l.call(t[n]));return r}return l.call(t)}return t}function m(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],\"rgba(\"+t.join(\",\")+\")\"}function x(t){return Object(o[\"u\"])(t&&t[0])?2:1}var w=0,O=1,k=2,T=3,S=4,j=5,C=6;function A(t){return t===S||t===j}function P(t){return t===O||t===k}var M=[0,0,0,0],L=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var n=this.keyframes,s=n.length,u=!1,l=C,f=e;if(Object(o[\"u\"])(e)){var d=x(e);l=d,(1===d&&!Object(o[\"z\"])(e[0])||2===d&&!Object(o[\"z\"])(e[0][0]))&&(u=!0)}else if(Object(o[\"z\"])(e)&&!Object(o[\"l\"])(e))l=w;else if(Object(o[\"C\"])(e))if(isNaN(+e)){var p=c[\"parse\"](e);p&&(f=p,l=T)}else l=w;else if(Object(o[\"x\"])(e)){var v=Object(o[\"m\"])({},f);v.colorStops=Object(o[\"H\"])(e.colorStops,(function(t){return{offset:t.offset,color:c[\"parse\"](t.color)}})),Object(h[\"m\"])(e)?l=S:Object(h[\"o\"])(e)&&(l=j),f=v}0===s?this.valType=l:l===this.valType&&l!==C||(u=!0),this.discrete=this.discrete||u;var y={time:t,value:f,rawValue:e,percent:0};return r&&(y.easing=r,y.easingFunc=Object(o[\"w\"])(r)?r:i[r]||Object(a[\"a\"])(r)),n.push(y),y},t.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort((function(t,e){return t.time-e.time}));for(var n=this.valType,i=r.length,o=r[i-1],a=this.discrete,s=P(n),u=A(n),c=0;c<i;c++){var h=r[c],l=h.value,f=o.value;h.percent=h.time/t,a||(s&&c!==i-1?b(l,f,n):u&&g(l.colorStops,f.colorStops))}if(!a&&n!==j&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;var d=r[0].value;for(c=0;c<i;c++)n===w?r[c].additiveValue=r[c].value-d:n===T?r[c].additiveValue=v([],r[c].value,d,-1):P(n)&&(r[c].additiveValue=n===O?v([],r[c].value,d,-1):y([],r[c].value,d,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,n,i,a=null!=this._additiveTrack,s=a?\"additiveValue\":\"value\",u=this.valType,c=this.keyframes,h=c.length,l=this.propName,v=u===T,y=this._lastFr,g=Math.min;if(1===h)n=i=c[0];else{if(e<0)r=0;else if(e<this._lastFrP){var b=g(y+1,h-1);for(r=b;r>=0;r--)if(c[r].percent<=e)break;r=g(r,h-2)}else{for(r=y;r<h;r++)if(c[r].percent>e)break;r=g(r-1,h-2)}i=c[r+1],n=c[r]}if(n&&i){this._lastFr=r,this._lastFrP=e;var _=i.percent-n.percent,x=0===_?1:g((e-n.percent)/_,1);i.easingFunc&&(x=i.easingFunc(x));var w=a?this._additiveValue:v?M:t[l];if(!P(u)&&!v||w||(w=this._additiveValue=[]),this.discrete)t[l]=x<1?n.rawValue:i.rawValue;else if(P(u))u===O?d(w,n[s],i[s],x):p(w,n[s],i[s],x);else if(A(u)){var k=n[s],j=i[s],C=u===S;t[l]={type:C?\"linear\":\"radial\",x:f(k.x,j.x,x),y:f(k.y,j.y,x),colorStops:Object(o[\"H\"])(k.colorStops,(function(t,e){var r=j.colorStops[e];return{offset:f(t.offset,r.offset,x),color:m(d([],t.color,r.color,x))}})),global:j.global},C?(t[l].x2=f(k.x2,j.x2,x),t[l].y2=f(k.y2,j.y2,x)):t[l].r=f(k.r,j.r,x)}else if(v)d(w,n[s],i[s],x),a||(t[l]=m(w));else{var L=f(n[s],i[s],x);a?this._additiveValue=L:t[l]=L}a&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,n=this._additiveValue;e===w?t[r]=t[r]+n:e===T?(c[\"parse\"](t[r],M),v(M,M,n,1),t[r]=m(M)):e===O?v(t[r],t[r],n,1):e===k&&y(t[r],t[r],n,1)},t}(),I=function(){function t(t,e,r,n){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n?Object(o[\"G\"])(\"Can' use additive animation on looped animation.\"):(this._additiveAnimators=n,this._allowDiscrete=r)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,Object(o[\"F\"])(e),r)},t.prototype.whenWithKeys=function(t,e,r,n){for(var i=this._tracks,o=0;o<r.length;o++){var a=r[o],s=i[a];if(!s){s=i[a]=new L(a);var u=void 0,c=this._getAdditiveTrack(a);if(c){var h=c.keyframes,l=h[h.length-1];u=l&&l.value,c.valType===T&&u&&(u=m(u))}else u=this._target[a];if(null==u)continue;t>0&&s.addKeyframe(0,_(u),n),this._trackKeys.push(a)}s.addKeyframe(t,_(e[a]),n)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var n=0;n<r.length;n++){var i=r[n].getTrack(t);i&&(e=i)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,r=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var o=this._trackKeys[i],a=this._tracks[o],s=this._getAdditiveTrack(o),c=a.keyframes,h=c.length;if(a.prepare(n,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var l=c[h-1];l&&(e._target[a.propName]=l.rawValue),a.setFinished()}else r.push(a)}if(r.length||this._force){var f=new u({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var n=e._additiveAnimators;if(n){for(var i=!1,o=0;o<n.length;o++)if(n[o]._clip){i=!0;break}i||(e._additiveAnimators=null)}for(o=0;o<r.length;o++)r[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return Object(o[\"H\"])(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,n=this._trackKeys,i=0;i<t.length;i++){var o=r[t[i]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(i=0;i<n.length;i++)if(!r[n[i]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var i=e[n],o=this._tracks[i];if(o&&!o.isFinished()){var a=o.keyframes,s=a[r?0:a.length-1];s&&(t[i]=_(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||Object(o[\"F\"])(t);for(var r=0;r<e.length;r++){var n=e[r],i=this._tracks[n];if(i){var a=i.keyframes;if(a.length>1){var s=a.pop();i.addKeyframe(s.time,t[n]),i.prepare(this._maxTime,i.getAdditiveTrack())}}}},t}();e[\"b\"]=I},\"0b25\":function(t,e,r){var n=r(\"a691\"),i=r(\"50c4\");t.exports=function(t){if(void 0===t)return 0;var e=n(t),r=i(e);if(e!==r)throw RangeError(\"Wrong length or index\");return r}},\"0da8\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"19eb\"),o=r(\"9850\"),a=r(\"6d8b\"),s=Object(a[\"i\"])({x:0,y:0},i[\"b\"]),u={style:Object(a[\"i\"])({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},i[\"a\"].style)};function c(t){return!!(t&&\"string\"!==typeof t&&t.width&&t.height)}var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(n[\"a\"])(e,t),e.prototype.createStyle=function(t){return Object(a[\"g\"])(s,t)},e.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var n=c(e.image)?e.image:this.__image;if(!n)return 0;var i=\"width\"===t?\"height\":\"width\",o=e[i];return null==o?n[t]:n[t]/n[i]*o},e.prototype.getWidth=function(){return this._getSize(\"width\")},e.prototype.getHeight=function(){return this._getSize(\"height\")},e.prototype.getAnimationStyleProps=function(){return u},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new o[\"a\"](t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(i[\"c\"]);h.prototype.type=\"image\",e[\"a\"]=h},\"0e50\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return q})),r.d(e,\"c\",(function(){return $})),r.d(e,\"a\",(function(){return J})),r.d(e,\"d\",(function(){return tt}));var n=r(\"4a3f\"),i=r(\"cbe5\"),o=r(\"6d8b\"),a=r(\"401b\"),s=r(\"342d\"),u=r(\"8582\"),c=r(\"e263\"),h=r(\"9850\"),l=r(\"dce8\"),f=r(\"87b1\"),d=r(\"c7a2\"),p=r(\"4aa2\"),v=r(\"20c8\"),y=v[\"a\"].CMD;function g(t,e){return Math.abs(t-e)<1e-5}function b(t){var e,r,n,i,o,a=t.data,s=t.len(),u=[],c=0,h=0,l=0,f=0;function d(t,r){e&&e.length>2&&u.push(e),e=[t,r]}function p(t,r,n,i){g(t,n)&&g(r,i)||e.push(t,r,n,i,n,i)}function v(t,r,n,i,o,a){var s=Math.abs(r-t),u=4*Math.tan(s/4)/3,c=r<t?-1:1,h=Math.cos(t),l=Math.sin(t),f=Math.cos(r),d=Math.sin(r),p=h*o+n,v=l*a+i,y=f*o+n,g=d*a+i,b=o*u*c,_=a*u*c;e.push(p-b*l,v+_*h,y+b*d,g-_*f,y,g)}for(var b=0;b<s;){var _=a[b++],m=1===b;switch(m&&(c=a[b],h=a[b+1],l=c,f=h,_!==y.L&&_!==y.C&&_!==y.Q||(e=[l,f])),_){case y.M:c=l=a[b++],h=f=a[b++],d(l,f);break;case y.L:r=a[b++],n=a[b++],p(c,h,r,n),c=r,h=n;break;case y.C:e.push(a[b++],a[b++],a[b++],a[b++],c=a[b++],h=a[b++]);break;case y.Q:r=a[b++],n=a[b++],i=a[b++],o=a[b++],e.push(c+2/3*(r-c),h+2/3*(n-h),i+2/3*(r-i),o+2/3*(n-o),i,o),c=i,h=o;break;case y.A:var x=a[b++],w=a[b++],O=a[b++],k=a[b++],T=a[b++],S=a[b++]+T;b+=1;var j=!a[b++];r=Math.cos(T)*O+x,n=Math.sin(T)*k+w,m?(l=r,f=n,d(l,f)):p(c,h,r,n),c=Math.cos(S)*O+x,h=Math.sin(S)*k+w;for(var C=(j?-1:1)*Math.PI/2,A=T;j?A>S:A<S;A+=C){var P=j?Math.max(A+C,S):Math.min(A+C,S);v(A,P,x,w,O,k)}break;case y.R:l=c=a[b++],f=h=a[b++],r=l+a[b++],n=f+a[b++],d(r,f),p(r,f,r,n),p(r,n,l,n),p(l,n,l,f),p(l,f,r,f);break;case y.Z:e&&p(c,h,l,f),c=l,h=f;break}}return e&&e.length>2&&u.push(e),u}function _(t,e,r,i,o,a,s,u,c,h){if(g(t,r)&&g(e,i)&&g(o,s)&&g(a,u))c.push(s,u);else{var l=2/h,f=l*l,d=s-t,p=u-e,v=Math.sqrt(d*d+p*p);d/=v,p/=v;var y=r-t,b=i-e,m=o-s,x=a-u,w=y*y+b*b,O=m*m+x*x;if(w<f&&O<f)c.push(s,u);else{var k=d*y+p*b,T=-d*m-p*x,S=w-k*k,j=O-T*T;if(S<f&&k>=0&&j<f&&T>=0)c.push(s,u);else{var C=[],A=[];Object(n[\"g\"])(t,r,o,s,.5,C),Object(n[\"g\"])(e,i,a,u,.5,A),_(C[0],A[0],C[1],A[1],C[2],A[2],C[3],A[3],c,h),_(C[4],A[4],C[5],A[5],C[6],A[6],C[7],A[7],c,h)}}}}function m(t,e){var r=b(t),n=[];e=e||1;for(var i=0;i<r.length;i++){var o=r[i],a=[],s=o[0],u=o[1];a.push(s,u);for(var c=2;c<o.length;){var h=o[c++],l=o[c++],f=o[c++],d=o[c++],p=o[c++],v=o[c++];_(s,u,h,l,f,d,p,v,a,e),s=p,u=v}n.push(a)}return n}function x(t,e,r){var n=t[e],i=t[1-e],o=Math.abs(n/i),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var u=[],c=0;c<a;c++)u.push(s);var h=a*s,l=r-h;if(l>0)for(c=0;c<l;c++)u[c%a]+=1;return u}function w(t,e,r){for(var n=t.r0,i=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),u=s*i,c=i-n,h=u>Math.abs(c),l=x([u,c],h?0:1,e),f=(h?s:c)/l.length,d=0;d<l.length;d++)for(var p=(h?c:s)/l[d],v=0;v<l[d];v++){var y={};h?(y.startAngle=o+f*d,y.endAngle=o+f*(d+1),y.r0=n+p*v,y.r=n+p*(v+1)):(y.startAngle=o+p*v,y.endAngle=o+p*(v+1),y.r0=n+f*d,y.r=n+f*(d+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,r.push(y)}}function O(t,e,r){for(var n=t.width,i=t.height,o=n>i,a=x([n,i],o?0:1,e),s=o?\"width\":\"height\",u=o?\"height\":\"width\",c=o?\"x\":\"y\",h=o?\"y\":\"x\",l=t[s]/a.length,f=0;f<a.length;f++)for(var d=t[u]/a[f],p=0;p<a[f];p++){var v={};v[c]=f*l,v[h]=p*d,v[s]=l,v[u]=d,v.x+=t.x,v.y+=t.y,r.push(v)}}function k(t,e,r,n){return t*n-r*e}function T(t,e,r,n,i,o,a,s){var u=r-t,c=n-e,h=a-i,f=s-o,d=k(h,f,u,c);if(Math.abs(d)<1e-6)return null;var p=t-i,v=e-o,y=k(p,v,h,f)/d;return y<0||y>1?null:new l[\"a\"](y*u+t,y*c+e)}function S(t,e,r){var n=new l[\"a\"];l[\"a\"].sub(n,r,e),n.normalize();var i=new l[\"a\"];l[\"a\"].sub(i,t,e);var o=i.dot(n);return o}function j(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function C(t,e,r){for(var n=t.length,i=[],o=0;o<n;o++){var a=t[o],s=t[(o+1)%n],u=T(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);u&&i.push({projPt:S(u,e,r),pt:u,idx:o})}if(i.length<2)return[{points:t},{points:t}];i.sort((function(t,e){return t.projPt-e.projPt}));var c=i[0],h=i[i.length-1];if(h.idx<c.idx){var l=c;c=h,h=l}var f=[c.pt.x,c.pt.y],d=[h.pt.x,h.pt.y],p=[f],v=[d];for(o=c.idx+1;o<=h.idx;o++)j(p,t[o].slice());j(p,d),j(p,f);for(o=h.idx+1;o<=c.idx+n;o++)j(v,t[o%n].slice());return j(v,f),j(v,d),[{points:p},{points:v}]}function A(t){var e=t.points,r=[],n=[];Object(c[\"d\"])(e,r,n);var i=new h[\"a\"](r[0],r[1],n[0]-r[0],n[1]-r[1]),o=i.width,a=i.height,s=i.x,u=i.y,f=new l[\"a\"],d=new l[\"a\"];return o>a?(f.x=d.x=s+o/2,f.y=u,d.y=u+a):(f.y=d.y=u+a/2,f.x=s,d.x=s+o),C(e,f,d)}function P(t,e,r,n){if(1===r)n.push(e);else{var i=Math.floor(r/2),o=t(e);P(t,o[0],i,n),P(t,o[1],r-i,n)}return n}function M(t,e){for(var r=[],n=0;n<e;n++)r.push(Object(s[\"a\"])(t));return r}function L(t,e){e.setStyle(t.style),e.z=t.z,e.z2=t.z2,e.zlevel=t.zlevel}function I(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}function D(t,e){var r,n=[],i=t.shape;switch(t.type){case\"rect\":O(i,e,n),r=d[\"a\"];break;case\"sector\":w(i,e,n),r=p[\"a\"];break;case\"circle\":w({r0:0,r:i.r,startAngle:0,endAngle:2*Math.PI,cx:i.cx,cy:i.cy},e,n),r=p[\"a\"];break;default:var a=t.getComputedTransform(),s=a?Math.sqrt(Math.max(a[0]*a[0]+a[1]*a[1],a[2]*a[2]+a[3]*a[3])):1,u=Object(o[\"H\"])(m(t.getUpdatedPathProxy(),s),(function(t){return I(t)})),h=u.length;if(0===h)P(A,{points:u[0]},e,n);else if(h===e)for(var l=0;l<h;l++)n.push({points:u[l]});else{var v=0,y=Object(o[\"H\"])(u,(function(t){var e=[],r=[];Object(c[\"d\"])(t,e,r);var n=(r[1]-e[1])*(r[0]-e[0]);return v+=n,{poly:t,area:n}}));y.sort((function(t,e){return e.area-t.area}));var g=e;for(l=0;l<h;l++){var b=y[l];if(g<=0)break;var _=l===h-1?g:Math.ceil(b.area/v*e);_<0||(P(A,{points:b.poly},_,n),g-=_)}}r=f[\"a\"];break}if(!r)return M(t,e);var x=[];for(l=0;l<n.length;l++){var k=new r;k.setShape(n[l]),L(t,k),x.push(k)}return x}function F(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var o=[],a=[],s=r<i?t:e,u=Math.min(r,i),c=Math.abs(i-r)/6,h=(u-2)/6,l=Math.ceil(c/h)+1,f=[s[0],s[1]],d=c,p=2;p<u;){var v=s[p-2],y=s[p-1],g=s[p++],b=s[p++],_=s[p++],m=s[p++],x=s[p++],w=s[p++];if(d<=0)f.push(g,b,_,m,x,w);else{for(var O=Math.min(d,l-1)+1,k=1;k<=O;k++){var T=k/O;Object(n[\"g\"])(v,g,_,x,T,o),Object(n[\"g\"])(y,b,m,w,T,a),v=o[3],y=a[3],f.push(o[1],a[1],o[2],a[2],v,y),g=o[5],b=a[5],_=o[6],m=a[6]}d-=O-1}}return s===t?[f,e]:[t,f]}function z(t,e){for(var r=t.length,n=t[r-2],i=t[r-1],o=[],a=0;a<e.length;)o[a++]=n,o[a++]=i;return o}function R(t,e){for(var r,n,i,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var u=t[s],c=e[s],h=void 0,l=void 0;u?c?(r=F(u,c),h=r[0],l=r[1],n=h,i=l):(l=z(i||u,u),h=u):(h=z(n||c,c),l=c),o.push(h),a.push(l)}return[o,a]}function B(t){for(var e=0,r=0,n=0,i=t.length,o=0,a=i-2;o<i;a=o,o+=2){var s=t[a],u=t[a+1],c=t[o],h=t[o+1],l=s*h-c*u;e+=l,r+=(s+c)*l,n+=(u+h)*l}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,n/e/3,e]}function E(t,e,r,n){for(var i=(t.length-2)/6,o=1/0,a=0,s=t.length,u=s-2,c=0;c<i;c++){for(var h=6*c,l=0,f=0;f<s;f+=2){var d=0===f?h:(h+f-2)%u+2,p=t[d]-r[0],v=t[d+1]-r[1],y=e[f]-n[0],g=e[f+1]-n[1],b=y-p,_=g-v;l+=b*b+_*_}l<o&&(o=l,a=c)}return a}function N(t){for(var e=[],r=t.length,n=0;n<r;n+=2)e[n]=t[r-n-2],e[n+1]=t[r-n-1];return e}function H(t,e,r,n){for(var i,o=[],a=0;a<t.length;a++){var s=t[a],u=e[a],c=B(s),h=B(u);null==i&&(i=c[2]<0!==h[2]<0);var l=[],f=[],d=0,p=1/0,v=[],y=s.length;i&&(s=N(s));for(var g=6*E(s,u,c,h),b=y-2,_=0;_<b;_+=2){var m=(g+_)%b+2;l[_+2]=s[m]-c[0],l[_+3]=s[m+1]-c[1]}if(l[0]=s[g]-c[0],l[1]=s[g+1]-c[1],r>0)for(var x=n/r,w=-n/2;w<=n/2;w+=x){var O=Math.sin(w),k=Math.cos(w),T=0;for(_=0;_<s.length;_+=2){var S=l[_],j=l[_+1],C=u[_]-h[0],A=u[_+1]-h[1],P=C*k-A*O,M=C*O+A*k;v[_]=P,v[_+1]=M;var L=P-S,I=M-j;T+=L*L+I*I}if(T<p){p=T,d=w;for(var D=0;D<v.length;D++)f[D]=v[D]}}else for(var F=0;F<y;F+=2)f[F]=u[F]-h[0],f[F+1]=u[F+1]-h[1];o.push({from:l,to:f,fromCp:c,toCp:h,rotation:-d})}return o}function q(t){return t.__isCombineMorphing}var W=\"__mOriginal_\";function Y(t,e,r){var n=W+e,i=t[n]||t[e];t[n]||(t[n]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):i.apply(this,e),a&&a.apply(this,e),t}}function X(t,e){var r=W+e;t[r]&&(t[e]=t[r],t[r]=null)}function V(t,e){for(var r=0;r<t.length;r++)for(var n=t[r],i=0;i<n.length;){var o=n[i],a=n[i+1];n[i++]=e[0]*o+e[2]*a+e[4],n[i++]=e[1]*o+e[3]*a+e[5]}}function U(t,e){var r=t.getUpdatedPathProxy(),n=e.getUpdatedPathProxy(),i=R(b(r),b(n)),o=i[0],s=i[1],u=t.getComputedTransform(),c=e.getComputedTransform();function h(){this.transform=null}u&&V(o,u),c&&V(s,c),Y(e,\"updateTransform\",{replace:h}),e.transform=null;var l=H(o,s,10,Math.PI),f=[];Y(e,\"buildPath\",{replace:function(t){for(var r=e.__morphT,n=1-r,i=[],o=0;o<l.length;o++){var s=l[o],u=s.from,c=s.to,h=s.rotation*r,d=s.fromCp,p=s.toCp,v=Math.sin(h),y=Math.cos(h);Object(a[\"j\"])(i,d,p,r);for(var g=0;g<u.length;g+=2){var b=u[g],_=u[g+1],m=c[g],x=c[g+1],w=b*n+m*r,O=_*n+x*r;f[g]=w*y-O*v+i[0],f[g+1]=w*v+O*y+i[1]}var k=f[0],T=f[1];t.moveTo(k,T);for(g=2;g<u.length;){m=f[g++],x=f[g++];var S=f[g++],j=f[g++],C=f[g++],A=f[g++];k===m&&T===x&&S===C&&j===A?t.lineTo(C,A):t.bezierCurveTo(m,x,S,j,C,A),k=C,T=A}}}})}function $(t,e,r){if(!t||!e)return e;var n=r.done,i=r.during;function a(){X(e,\"buildPath\"),X(e,\"updateTransform\"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return U(t,e),e.__morphT=0,e.animateTo({__morphT:1},Object(o[\"i\"])({during:function(t){e.dirtyShape(),i&&i(t)},done:function(){a(),n&&n()}},r)),e}function Z(t,e,r,n,i,o){var a=16;t=i===r?0:Math.round(32767*(t-r)/(i-r)),e=o===n?0:Math.round(32767*(e-n)/(o-n));for(var s,u=0,c=(1<<a)/2;c>0;c/=2){var h=0,l=0;(t&c)>0&&(h=1),(e&c)>0&&(l=1),u+=c*c*(3*h^l),0===l&&(1===h&&(t=c-1-t,e=c-1-e),s=t,t=e,e=s)}return u}function G(t){var e=1/0,r=1/0,n=-1/0,i=-1/0,a=Object(o[\"H\"])(t,(function(t){var o=t.getBoundingRect(),a=t.getComputedTransform(),s=o.x+o.width/2+(a?a[4]:0),u=o.y+o.height/2+(a?a[5]:0);return e=Math.min(s,e),r=Math.min(u,r),n=Math.max(s,n),i=Math.max(u,i),[s,u]})),s=Object(o[\"H\"])(a,(function(o,a){return{cp:o,z:Z(o[0],o[1],e,r,n,i),path:t[a]}}));return s.sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function Q(t){return D(t.path,t.count)}function K(){return{fromIndividuals:[],toIndividuals:[],count:0}}function J(t,e,r){var n=[];function a(t){for(var e=0;e<t.length;e++){var r=t[e];q(r)?a(r.childrenRef()):r instanceof i[\"b\"]&&n.push(r)}}a(t);var s=n.length;if(!s)return K();var c=r.dividePath||Q,h=c({path:e,count:s});if(h.length!==s)return console.error(\"Invalid morphing: unmatched splitted path\"),K();n=G(n),h=G(h);for(var l=r.done,f=r.during,d=r.individualDelay,p=new u[\"c\"],v=0;v<s;v++){var y=n[v],g=h[v];g.parent=e,g.copyTransform(p),d||U(y,g)}function b(t){for(var e=0;e<h.length;e++)h[e].addSelfToZr(t)}function _(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,X(e,\"addSelfToZr\"),X(e,\"removeSelfFromZr\")}e.__isCombineMorphing=!0,e.childrenRef=function(){return h},Y(e,\"addSelfToZr\",{after:function(t){b(t)}}),Y(e,\"removeSelfFromZr\",{after:function(t){for(var e=0;e<h.length;e++)h[e].removeSelfFromZr(t)}});var m=h.length;if(d){var x=m,w=function(){x--,0===x&&(_(),l&&l())};for(v=0;v<m;v++){var O=d?Object(o[\"i\"])({delay:(r.delay||0)+d(v,m,n[v],h[v]),done:w},r):r;$(n[v],h[v],O)}}else e.__morphT=0,e.animateTo({__morphT:1},Object(o[\"i\"])({during:function(t){for(var r=0;r<m;r++){var n=h[r];n.__morphT=e.__morphT,n.dirtyShape()}f&&f(t)},done:function(){_();for(var e=0;e<t.length;e++)X(t[e],\"updateTransform\");l&&l()}},r));return e.__zr&&b(e.__zr),{fromIndividuals:n,toIndividuals:h,count:m}}function tt(t,e,r){var n=e.length,a=[],u=r.dividePath||Q;function c(t){for(var e=0;e<t.length;e++){var r=t[e];q(r)?c(r.childrenRef()):r instanceof i[\"b\"]&&a.push(r)}}if(q(t)){c(t.childrenRef());var h=a.length;if(h<n)for(var l=0,f=h;f<n;f++)a.push(Object(s[\"a\"])(a[l++%h]));a.length=n}else{a=u({path:t,count:n});var d=t.getComputedTransform();for(f=0;f<a.length;f++)a[f].setLocalTransform(d);if(a.length!==n)return console.error(\"Invalid morphing: unmatched splitted path\"),K()}a=G(a),e=G(e);var p=r.individualDelay;for(f=0;f<n;f++){var v=p?Object(o[\"i\"])({delay:(r.delay||0)+p(f,n,a[f],e[f])},r):r;$(a[f],e[f],v)}return{fromIndividuals:a,toIndividuals:e,count:e.length}}},\"145e\":function(t,e,r){\"use strict\";var n=r(\"7b0b\"),i=r(\"23cb\"),o=r(\"50c4\"),a=Math.min;t.exports=[].copyWithin||function(t,e){var r=n(this),s=o(r.length),u=i(t,s),c=i(e,s),h=arguments.length>2?arguments[2]:void 0,l=a((void 0===h?s:i(h,s))-c,s-u),f=1;c<u&&u<c+l&&(f=-1,c+=l-1,u+=l-1);while(l-- >0)c in r?r[u]=r[c]:delete r[u],u+=f,c+=f;return r}},1687:function(t,e,r){\"use strict\";function n(){return[1,0,0,1,0,0]}function i(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function a(t,e,r){var n=e[0]*r[0]+e[2]*r[1],i=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],u=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=n,t[1]=i,t[2]=o,t[3]=a,t[4]=s,t[5]=u,t}function s(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function u(t,e,r,n){void 0===n&&(n=[0,0]);var i=e[0],o=e[2],a=e[4],s=e[1],u=e[3],c=e[5],h=Math.sin(r),l=Math.cos(r);return t[0]=i*l+s*h,t[1]=-i*h+s*l,t[2]=o*l+u*h,t[3]=-o*h+l*u,t[4]=l*(a-n[0])+h*(c-n[1])+n[0],t[5]=l*(c-n[1])-h*(a-n[0])+n[1],t}function c(t,e,r){var n=r[0],i=r[1];return t[0]=e[0]*n,t[1]=e[1]*i,t[2]=e[2]*n,t[3]=e[3]*i,t[4]=e[4]*n,t[5]=e[5]*i,t}function h(t,e){var r=e[0],n=e[2],i=e[4],o=e[1],a=e[3],s=e[5],u=r*a-o*n;return u?(u=1/u,t[0]=a*u,t[1]=-o*u,t[2]=-n*u,t[3]=r*u,t[4]=(n*s-a*i)*u,t[5]=(o*i-r*s)*u,t):null}function l(t){var e=n();return o(e,t),e}r.d(e,\"c\",(function(){return n})),r.d(e,\"d\",(function(){return i})),r.d(e,\"b\",(function(){return o})),r.d(e,\"f\",(function(){return a})),r.d(e,\"i\",(function(){return s})),r.d(e,\"g\",(function(){return u})),r.d(e,\"h\",(function(){return c})),r.d(e,\"e\",(function(){return h})),r.d(e,\"a\",(function(){return l}))},\"170b\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"50c4\"),o=r(\"23cb\"),a=r(\"4840\"),s=n.aTypedArray,u=n.exportTypedArrayMethod;u(\"subarray\",(function(t,e){var r=s(this),n=r.length,u=o(t,n);return new(a(r,r.constructor))(r.buffer,r.byteOffset+u*r.BYTES_PER_ELEMENT,i((void 0===e?n:o(e,n))-u))}))},\"182d\":function(t,e,r){var n=r(\"f8cd\");t.exports=function(t,e){var r=n(t);if(r%e)throw RangeError(\"Wrong offset\");return r}},\"19eb\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return c})),r.d(e,\"a\",(function(){return h}));var n=r(\"21a1\"),i=r(\"d5b7\"),o=r(\"9850\"),a=r(\"6d8b\"),s=r(\"4bc4\"),u=\"__zr_style_\"+Math.round(10*Math.random()),c={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:\"#000\",opacity:1,blend:\"source-over\"},h={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};c[u]=!0;var l=[\"z\",\"z2\",\"invisible\"],f=[\"invisible\"],d=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype._init=function(e){for(var r=Object(a[\"F\"])(e),n=0;n<r.length;n++){var i=r[n];\"style\"===i?this.useStyle(e[i]):t.prototype.attrKV.call(this,i,e[i])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,r,n){var i=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&y(this,t,e)||i&&!i[0]&&!i[3])return!1;if(r&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(n&&this.parent){var a=this.parent;while(a){if(a.ignore)return!1;a=a.parent}}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(r[0],r[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,r=this.getBoundingRect(),n=this.style,i=n.shadowBlur||0,a=n.shadowOffsetX||0,s=n.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new o[\"a\"](0,0,0,0)),e?o[\"a\"].applyTransform(t,r,e):t.copy(r),(i||a||s)&&(t.width+=2*i+Math.abs(a),t.height+=2*i+Math.abs(s),t.x=Math.min(t.x,t.x+a-i),t.y=Math.min(t.y,t.y+s-i));var u=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-u),t.y=Math.floor(t.y-u),t.width=Math.ceil(t.width+1+2*u),t.height=Math.ceil(t.height+1+2*u))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new o[\"a\"](0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate(\"style\",t)},e.prototype.updateDuringAnimation=function(t){\"style\"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,r){\"style\"!==e?t.prototype.attrKV.call(this,e,r):this.style?this.setStyle(r):this.useStyle(r)},e.prototype.setStyle=function(t,e){return\"string\"===typeof t?this.style[t]=e:Object(a[\"m\"])(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=s[\"c\"],this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(this.__dirty&s[\"c\"])},e.prototype.styleUpdated=function(){this.__dirty&=~s[\"c\"]},e.prototype.createStyle=function(t){return Object(a[\"g\"])(c,t)},e.prototype.useStyle=function(t){t[u]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[u]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var r=this._normalState;e.style&&!r.style&&(r.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,r,l)},e.prototype._applyStateObj=function(e,r,n,i,o,s){t.prototype._applyStateObj.call(this,e,r,n,i,o,s);var u,c=!(r&&i);if(r&&r.style?o?i?u=r.style:(u=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(u,r.style)):(u=this._mergeStyle(this.createStyle(),i?this.style:n.style),this._mergeStyle(u,r.style)):c&&(u=n.style),u)if(o){var h=this.style;if(this.style=this.createStyle(c?{}:h),c)for(var d=Object(a[\"F\"])(h),p=0;p<d.length;p++){var v=d[p];v in u&&(u[v]=u[v],this.style[v]=h[v])}var y=Object(a[\"F\"])(u);for(p=0;p<y.length;p++){v=y[p];this.style[v]=this.style[v]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);var g=this.__inHover?f:l;for(p=0;p<g.length;p++){v=g[p];r&&null!=r[v]?this[v]=r[v]:c&&null!=n[v]&&(this[v]=n[v])}},e.prototype._mergeStates=function(e){for(var r,n=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.style&&(r=r||{},this._mergeStyle(r,o.style))}return r&&(n.style=r),n},e.prototype._mergeStyle=function(t,e){return Object(a[\"m\"])(t,e),t},e.prototype.getAnimationStyleProps=function(){return h},e.initDefaultProps=function(){var t=e.prototype;t.type=\"displayable\",t.invisible=!1,t.z=0,t.z2=0,t.zlevel=0,t.culling=!1,t.cursor=\"pointer\",t.rectHover=!1,t.incremental=!1,t._rect=null,t.dirtyRectTolerance=0,t.__dirty=s[\"a\"]|s[\"c\"]}(),e}(i[\"a\"]),p=new o[\"a\"](0,0,0,0),v=new o[\"a\"](0,0,0,0);function y(t,e,r){return p.copy(t.getBoundingRect()),t.transform&&p.applyTransform(t.transform),v.width=e,v.height=r,!p.intersect(v)}e[\"c\"]=d},\"20c8\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return T}));var n=r(\"401b\"),i=r(\"9850\"),o=r(\"2cf4c\"),a=r(\"e263\"),s=r(\"4a3f\"),u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},c=[],h=[],l=[],f=[],d=[],p=[],v=Math.min,y=Math.max,g=Math.cos,b=Math.sin,_=Math.abs,m=Math.PI,x=2*m,w=\"undefined\"!==typeof Float32Array,O=[];function k(t){var e=Math.round(t/m*1e8)/1e8;return e%2*m}function T(t,e){var r=k(t[0]);r<0&&(r+=x);var n=r-t[0],i=t[1];i+=n,!e&&i-r>=x?i=r+x:e&&r-i>=x?i=r-x:!e&&r>i?i=r+(x-k(r-i)):e&&r<i&&(i=r-(x-k(i-r))),t[0]=r,t[1]=i}var S=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,r){r=r||0,r>0&&(this._ux=_(r/o[\"e\"]/t)||0,this._uy=_(r/o[\"e\"]/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(u.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var r=_(t-this._xi),n=_(e-this._yi),i=r>this._ux||n>this._uy;if(this.addData(u.L,t,e),this._ctx&&i&&this._ctx.lineTo(t,e),i)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=r*r+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,r,n,i,o){return this._drawPendingPt(),this.addData(u.C,t,e,r,n,i,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,n,i,o),this._xi=i,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,r,n){return this._drawPendingPt(),this.addData(u.Q,t,e,r,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,n),this._xi=r,this._yi=n,this},t.prototype.arc=function(t,e,r,n,i,o){this._drawPendingPt(),O[0]=n,O[1]=i,T(O,o),n=O[0],i=O[1];var a=i-n;return this.addData(u.A,t,e,r,r,n,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,n,i,o),this._xi=g(i)*r+t,this._yi=b(i)*r+e,this},t.prototype.arcTo=function(t,e,r,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,n,i),this},t.prototype.rect=function(t,e,r,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,n),this.addData(u.R,t,e,r,n),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(u.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!w||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,n=this._len,i=0;i<e;i++)r+=t[i].len();w&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+r));for(i=0;i<e;i++)for(var o=t[i].data,a=0;a<o.length;a++)this.data[n++]=o[a];this._len=n},t.prototype.addData=function(t,e,r,n,i,o,a,s,u){if(this._saveData){var c=this.data;this._len+arguments.length>c.length&&(this._expandData(),c=this.data);for(var h=0;h<arguments.length;h++)c[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,w&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){l[0]=l[1]=d[0]=d[1]=Number.MAX_VALUE,f[0]=f[1]=p[0]=p[1]=-Number.MAX_VALUE;var t,e=this.data,r=0,o=0,s=0,c=0;for(t=0;t<this._len;){var h=e[t++],v=1===t;switch(v&&(r=e[t],o=e[t+1],s=r,c=o),h){case u.M:r=s=e[t++],o=c=e[t++],d[0]=s,d[1]=c,p[0]=s,p[1]=c;break;case u.L:Object(a[\"c\"])(r,o,e[t],e[t+1],d,p),r=e[t++],o=e[t++];break;case u.C:Object(a[\"b\"])(r,o,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],d,p),r=e[t++],o=e[t++];break;case u.Q:Object(a[\"e\"])(r,o,e[t++],e[t++],e[t],e[t+1],d,p),r=e[t++],o=e[t++];break;case u.A:var y=e[t++],_=e[t++],m=e[t++],x=e[t++],w=e[t++],O=e[t++]+w;t+=1;var k=!e[t++];v&&(s=g(w)*m+y,c=b(w)*x+_),Object(a[\"a\"])(y,_,m,x,w,O,k,d,p),r=g(O)*m+y,o=b(O)*x+_;break;case u.R:s=r=e[t++],c=o=e[t++];var T=e[t++],S=e[t++];Object(a[\"c\"])(s,c,s+T,c+S,d,p);break;case u.Z:r=s,o=c;break}n[\"l\"](l,l,d),n[\"k\"](f,f,p)}return 0===t&&(l[0]=l[1]=f[0]=f[1]=0),new i[\"a\"](l[0],l[1],f[0]-l[0],f[1]-l[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,n=this._uy,i=0,o=0,a=0,c=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,f=0,d=0;d<e;){var p=t[d++],m=1===d;m&&(i=t[d],o=t[d+1],a=i,c=o);var w=-1;switch(p){case u.M:i=a=t[d++],o=c=t[d++];break;case u.L:var O=t[d++],k=t[d++],T=O-i,S=k-o;(_(T)>r||_(S)>n||d===e-1)&&(w=Math.sqrt(T*T+S*S),i=O,o=k);break;case u.C:var j=t[d++],C=t[d++],A=(O=t[d++],k=t[d++],t[d++]),P=t[d++];w=Object(s[\"d\"])(i,o,j,C,O,k,A,P,10),i=A,o=P;break;case u.Q:j=t[d++],C=t[d++],O=t[d++],k=t[d++];w=Object(s[\"k\"])(i,o,j,C,O,k,10),i=O,o=k;break;case u.A:var M=t[d++],L=t[d++],I=t[d++],D=t[d++],F=t[d++],z=t[d++],R=z+F;d+=1,m&&(a=g(F)*I+M,c=b(F)*D+L),w=y(I,D)*v(x,Math.abs(z)),i=g(R)*I+M,o=b(R)*D+L;break;case u.R:a=i=t[d++],c=o=t[d++];var B=t[d++],E=t[d++];w=2*B+2*E;break;case u.Z:T=a-i,S=c-o;w=Math.sqrt(T*T+S*S),i=a,o=c;break}w>=0&&(h[f++]=w,l+=w)}return this._pathLen=l,l},t.prototype.rebuildPath=function(t,e){var r,n,i,o,a,l,f,d,p,m,x,w=this.data,O=this._ux,k=this._uy,T=this._len,S=e<1,j=0,C=0,A=0;if(!S||(this._pathSegLen||this._calculateLength(),f=this._pathSegLen,d=this._pathLen,p=e*d,p))t:for(var P=0;P<T;){var M=w[P++],L=1===P;switch(L&&(i=w[P],o=w[P+1],r=i,n=o),M!==u.L&&A>0&&(t.lineTo(m,x),A=0),M){case u.M:r=i=w[P++],n=o=w[P++],t.moveTo(i,o);break;case u.L:a=w[P++],l=w[P++];var I=_(a-i),D=_(l-o);if(I>O||D>k){if(S){var F=f[C++];if(j+F>p){var z=(p-j)/F;t.lineTo(i*(1-z)+a*z,o*(1-z)+l*z);break t}j+=F}t.lineTo(a,l),i=a,o=l,A=0}else{var R=I*I+D*D;R>A&&(m=a,x=l,A=R)}break;case u.C:var B=w[P++],E=w[P++],N=w[P++],H=w[P++],q=w[P++],W=w[P++];if(S){F=f[C++];if(j+F>p){z=(p-j)/F;Object(s[\"g\"])(i,B,N,q,z,c),Object(s[\"g\"])(o,E,H,W,z,h),t.bezierCurveTo(c[1],h[1],c[2],h[2],c[3],h[3]);break t}j+=F}t.bezierCurveTo(B,E,N,H,q,W),i=q,o=W;break;case u.Q:B=w[P++],E=w[P++],N=w[P++],H=w[P++];if(S){F=f[C++];if(j+F>p){z=(p-j)/F;Object(s[\"n\"])(i,B,N,z,c),Object(s[\"n\"])(o,E,H,z,h),t.quadraticCurveTo(c[1],h[1],c[2],h[2]);break t}j+=F}t.quadraticCurveTo(B,E,N,H),i=N,o=H;break;case u.A:var Y=w[P++],X=w[P++],V=w[P++],U=w[P++],$=w[P++],Z=w[P++],G=w[P++],Q=!w[P++],K=V>U?V:U,J=_(V-U)>.001,tt=$+Z,et=!1;if(S){F=f[C++];j+F>p&&(tt=$+Z*(p-j)/F,et=!0),j+=F}if(J&&t.ellipse?t.ellipse(Y,X,V,U,G,$,tt,Q):t.arc(Y,X,K,$,tt,Q),et)break t;L&&(r=g($)*V+Y,n=b($)*U+X),i=g(tt)*V+Y,o=b(tt)*U+X;break;case u.R:r=i=w[P],n=o=w[P+1],a=w[P++],l=w[P++];var rt=w[P++],nt=w[P++];if(S){F=f[C++];if(j+F>p){var it=p-j;t.moveTo(a,l),t.lineTo(a+v(it,rt),l),it-=rt,it>0&&t.lineTo(a+rt,l+v(it,nt)),it-=nt,it>0&&t.lineTo(a+y(rt-it,0),l+nt),it-=rt,it>0&&t.lineTo(a,l+y(nt-it,0));break t}j+=F}t.rect(a,l,rt,nt);break;case u.Z:if(S){F=f[C++];if(j+F>p){z=(p-j)/F;t.lineTo(i*(1-z)+r*z,o*(1-z)+n*z);break t}j+=F}t.closePath(),i=r,o=n}}},t.prototype.clone=function(){var e=new t,r=this.data;return e.data=r.slice?r.slice():Array.prototype.slice.call(r),e._len=this._len,e},t.CMD=u,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();e[\"a\"]=S},\"219c\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=n.aTypedArray,o=n.exportTypedArrayMethod,a=[].sort;o(\"sort\",(function(t){return a.call(i(this),t)}))},\"21a1\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return i}));\n/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\nvar n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},n(t,e)};function i(t,e){if(\"function\"!==typeof e&&null!==e)throw new TypeError(\"Class extends value \"+String(e)+\" is not a constructor or null\");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}Object.create;Object.create},\"21f4\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return n})),r.d(e,\"a\",(function(){return i}));r(\"d3b7\"),r(\"ac1f\"),r(\"25f0\"),r(\"5319\");function n(t){return\"undefined\"===typeof t||null===t||\"\"===t}function i(t,e){var r=t.per_page||t.size,n=t.total-r*(t.page-1),i=Math.floor((e-n)/r)+1;i<0&&(i=0);var o=t.page-i;return o<1&&(o=1),o}},\"22d1\":function(t,e,r){\"use strict\";var n=function(){function t(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return t}(),i=function(){function t(){this.browser=new n,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=\"undefined\"!==typeof window}return t}(),o=new i;function a(t,e){var r=e.browser,n=t.match(/Firefox\\/([\\d.]+)/),i=t.match(/MSIE\\s([\\d.]+)/)||t.match(/Trident\\/.+?rv:(([\\d.]+))/),o=t.match(/Edge?\\/([\\d.]+)/),a=/micromessenger/i.test(t);n&&(r.firefox=!0,r.version=n[1]),i&&(r.ie=!0,r.version=i[1]),o&&(r.edge=!0,r.version=o[1],r.newEdge=+o[1].split(\".\")[0]>18),a&&(r.weChat=!0),e.svgSupported=\"undefined\"!==typeof SVGRect,e.touchEventsSupported=\"ontouchstart\"in window&&!r.ie&&!r.edge,e.pointerEventsSupported=\"onpointerdown\"in window&&(r.edge||r.ie&&+r.version>=11),e.domSupported=\"undefined\"!==typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&\"transition\"in s||r.edge||\"WebKitCSSMatrix\"in window&&\"m11\"in new WebKitCSSMatrix||\"MozPerspective\"in s)&&!(\"OTransition\"in s),e.transformSupported=e.transform3dSupported||r.ie&&+r.version>=9}\"object\"===typeof wx&&\"function\"===typeof wx.getSystemInfoSync?(o.wxa=!0,o.touchEventsSupported=!0):\"undefined\"===typeof document&&\"undefined\"!==typeof self?o.worker=!0:!o.hasGlobalWindow||\"Deno\"in window?(o.node=!0,o.svgSupported=!0):a(navigator.userAgent,o),e[\"a\"]=o},2308:function(t,e,r){\"use strict\";var n=r(\"04fc\"),i=r.n(n);i.a},\"25a1\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"d58f\").right,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"reduceRight\",(function(t){return i(o(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},2954:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"4840\"),o=r(\"d039\"),a=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod,c=[].slice,h=o((function(){new Int8Array(1).slice()}));u(\"slice\",(function(t,e){var r=c.call(a(this),t,e),n=i(this,this.constructor),o=0,u=r.length,h=new(s(n))(u);while(u>o)h[o]=r[o++];return h}),h)},\"2cf4c\":function(t,e,r){\"use strict\";r.d(e,\"e\",(function(){return o})),r.d(e,\"b\",(function(){return a})),r.d(e,\"a\",(function(){return s})),r.d(e,\"d\",(function(){return u})),r.d(e,\"c\",(function(){return c}));var n=r(\"22d1\"),i=1;n[\"a\"].hasGlobalWindow&&(i=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var o=i,a=.4,s=\"#333\",u=\"#ccc\",c=\"#eee\"},\"2dc5\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"6d8b\"),o=r(\"d5b7\"),a=r(\"9850\"),s=function(t){function e(e){var r=t.call(this)||this;return r.isGroup=!0,r._children=[],r.attr(e),r}return Object(n[\"a\"])(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,n=r.indexOf(e);n>=0&&(r.splice(n,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var r=i[\"r\"](this._children,t);return r>=0&&this.replaceAt(e,r),this},e.prototype.replaceAt=function(t,e){var r=this._children,n=r[e];if(t&&t!==this&&t.parent!==this&&t!==n){r[e]=t,n.parent=null;var i=this.__zr;i&&n.removeSelfFromZr(i),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,r=this._children,n=i[\"r\"](r,t);return n<0||(r.splice(n,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var n=t[r];e&&n.removeSelfFromZr(e),n.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var r=this._children,n=0;n<r.length;n++){var i=r[n];t.call(e,i,n)}return this},e.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var n=this._children[r],i=t.call(e,n);n.isGroup&&!i&&n.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var r=0;r<this._children.length;r++){var n=this._children[r];n.addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var r=0;r<this._children.length;r++){var n=this._children[r];n.removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new a[\"a\"](0,0,0,0),r=t||this._children,n=[],i=null,o=0;o<r.length;o++){var s=r[o];if(!s.ignore&&!s.invisible){var u=s.getBoundingRect(),c=s.getLocalTransform(n);c?(a[\"a\"].applyTransform(e,u,c),i=i||e.clone(),i.union(e)):(i=i||u.clone(),i.union(u))}}return i||e},e}(o[\"a\"]);s.prototype.type=\"group\",e[\"a\"]=s},3041:function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return q})),r.d(e,\"b\",(function(){return W}));var n,i=r(\"2dc5\"),o=r(\"0da8\"),a=r(\"d9fc\"),s=r(\"c7a2\"),u=r(\"ae69\"),c=r(\"cb11\"),h=r(\"87b1\"),l=r(\"d498\"),f=r(\"1687\"),d=r(\"342d\"),p=r(\"6d8b\"),v=r(\"48a9\"),y=r(\"dded\"),g=r(\"dd4f\"),b=r(\"4a80\"),_={fill:\"fill\",stroke:\"stroke\",\"stroke-width\":\"lineWidth\",opacity:\"opacity\",\"fill-opacity\":\"fillOpacity\",\"stroke-opacity\":\"strokeOpacity\",\"stroke-dasharray\":\"lineDash\",\"stroke-dashoffset\":\"lineDashOffset\",\"stroke-linecap\":\"lineCap\",\"stroke-linejoin\":\"lineJoin\",\"stroke-miterlimit\":\"miterLimit\",\"font-family\":\"fontFamily\",\"font-size\":\"fontSize\",\"font-style\":\"fontStyle\",\"font-weight\":\"fontWeight\",\"text-anchor\":\"textAlign\",visibility:\"visibility\",display:\"display\"},m=Object(p[\"F\"])(_),x={\"alignment-baseline\":\"textBaseline\",\"stop-color\":\"stopColor\"},w=Object(p[\"F\"])(x),O=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var r=Object(b[\"a\"])(t);this._defsUsePending=[];var n=new i[\"a\"];this._root=n;var o=[],a=r.getAttribute(\"viewBox\")||\"\",u=parseFloat(r.getAttribute(\"width\")||e.width),c=parseFloat(r.getAttribute(\"height\")||e.height);isNaN(u)&&(u=null),isNaN(c)&&(c=null),A(r,n,null,!0,!1);var h,l,f=r.firstChild;while(f)this._parseNode(f,n,o,null,!1,!1),f=f.nextSibling;if(I(this._defs,this._defsUsePending),this._defsUsePending=[],a){var d=F(a);d.length>=4&&(h={x:parseFloat(d[0]||0),y:parseFloat(d[1]||0),width:parseFloat(d[2]),height:parseFloat(d[3])})}if(h&&null!=u&&null!=c&&(l=q(h,{x:0,y:0,width:u,height:c}),!e.ignoreViewBox)){var p=n;n=new i[\"a\"],n.add(p),p.scaleX=p.scaleY=l.scale,p.x=l.x,p.y=l.y}return e.ignoreRootClip||null==u||null==c||n.setClipPath(new s[\"a\"]({shape:{x:0,y:0,width:u,height:c}})),{root:n,width:u,height:c,viewBoxRect:h,viewBoxTransform:l,named:o}},t.prototype._parseNode=function(t,e,r,i,o,a){var s,u=t.nodeName.toLowerCase(),c=i;if(\"defs\"===u&&(o=!0),\"text\"===u&&(a=!0),\"defs\"===u||\"switch\"===u)s=e;else{if(!o){var h=n[u];if(h&&Object(p[\"q\"])(n,u)){s=h.call(this,t,e);var l=t.getAttribute(\"name\");if(l){var f={name:l,namedFrom:null,svgNodeTagLower:u,el:s};r.push(f),\"g\"===u&&(c=f)}else i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:u,el:s});e.add(s)}}var d=k[u];if(d&&Object(p[\"q\"])(k,u)){var v=d.call(this,t),y=t.getAttribute(\"id\");y&&(this._defs[y]=v)}}if(s&&s.isGroup){var g=t.firstChild;while(g)1===g.nodeType?this._parseNode(g,s,r,c,o,a):3===g.nodeType&&a&&this._parseText(g,s),g=g.nextSibling}},t.prototype._parseText=function(t,e){var r=new g[\"a\"]({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});j(e,r),A(t,r,this._defsUsePending,!1,!1),P(r,e);var n=r.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,r.scaleX*=i/9,r.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+\"px\",n.fontFamily||\"sans-serif\"].join(\" \");n.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},t.internalField=function(){n={g:function(t,e){var r=new i[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new s[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute(\"x\")||\"0\"),y:parseFloat(t.getAttribute(\"y\")||\"0\"),width:parseFloat(t.getAttribute(\"width\")||\"0\"),height:parseFloat(t.getAttribute(\"height\")||\"0\")}),r.silent=!0,r},circle:function(t,e){var r=new a[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute(\"cx\")||\"0\"),cy:parseFloat(t.getAttribute(\"cy\")||\"0\"),r:parseFloat(t.getAttribute(\"r\")||\"0\")}),r.silent=!0,r},line:function(t,e){var r=new c[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute(\"x1\")||\"0\"),y1:parseFloat(t.getAttribute(\"y1\")||\"0\"),x2:parseFloat(t.getAttribute(\"x2\")||\"0\"),y2:parseFloat(t.getAttribute(\"y2\")||\"0\")}),r.silent=!0,r},ellipse:function(t,e){var r=new u[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute(\"cx\")||\"0\"),cy:parseFloat(t.getAttribute(\"cy\")||\"0\"),rx:parseFloat(t.getAttribute(\"rx\")||\"0\"),ry:parseFloat(t.getAttribute(\"ry\")||\"0\")}),r.silent=!0,r},polygon:function(t,e){var r,n=t.getAttribute(\"points\");n&&(r=C(n));var i=new h[\"a\"]({shape:{points:r||[]},silent:!0});return j(e,i),A(t,i,this._defsUsePending,!1,!1),i},polyline:function(t,e){var r,n=t.getAttribute(\"points\");n&&(r=C(n));var i=new l[\"a\"]({shape:{points:r||[]},silent:!0});return j(e,i),A(t,i,this._defsUsePending,!1,!1),i},image:function(t,e){var r=new o[\"a\"];return j(e,r),A(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute(\"xlink:href\")||t.getAttribute(\"href\"),x:+t.getAttribute(\"x\"),y:+t.getAttribute(\"y\"),width:+t.getAttribute(\"width\"),height:+t.getAttribute(\"height\")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute(\"x\")||\"0\",n=t.getAttribute(\"y\")||\"0\",o=t.getAttribute(\"dx\")||\"0\",a=t.getAttribute(\"dy\")||\"0\";this._textX=parseFloat(r)+parseFloat(o),this._textY=parseFloat(n)+parseFloat(a);var s=new i[\"a\"];return j(e,s),A(t,s,this._defsUsePending,!1,!0),s},tspan:function(t,e){var r=t.getAttribute(\"x\"),n=t.getAttribute(\"y\");null!=r&&(this._textX=parseFloat(r)),null!=n&&(this._textY=parseFloat(n));var o=t.getAttribute(\"dx\")||\"0\",a=t.getAttribute(\"dy\")||\"0\",s=new i[\"a\"];return j(e,s),A(t,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(o),this._textY+=parseFloat(a),s},path:function(t,e){var r=t.getAttribute(\"d\")||\"\",n=Object(d[\"b\"])(r);return j(e,n),A(t,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),t}(),k={lineargradient:function(t){var e=parseInt(t.getAttribute(\"x1\")||\"0\",10),r=parseInt(t.getAttribute(\"y1\")||\"0\",10),n=parseInt(t.getAttribute(\"x2\")||\"10\",10),i=parseInt(t.getAttribute(\"y2\")||\"0\",10),o=new v[\"a\"](e,r,n,i);return T(t,o),S(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute(\"cx\")||\"0\",10),r=parseInt(t.getAttribute(\"cy\")||\"0\",10),n=parseInt(t.getAttribute(\"r\")||\"0\",10),i=new y[\"a\"](e,r,n);return T(t,i),S(t,i),i}};function T(t,e){var r=t.getAttribute(\"gradientUnits\");\"userSpaceOnUse\"===r&&(e.global=!0)}function S(t,e){var r=t.firstChild;while(r){if(1===r.nodeType&&\"stop\"===r.nodeName.toLocaleLowerCase()){var n=r.getAttribute(\"offset\"),i=void 0;i=n&&n.indexOf(\"%\")>0?parseInt(n,10)/100:n?parseFloat(n):0;var o={};N(r,o,o);var a=o.stopColor||r.getAttribute(\"stop-color\")||\"#000000\";e.colorStops.push({offset:i,color:a})}r=r.nextSibling}}function j(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),Object(p[\"i\"])(e.__inheritedStyle,t.__inheritedStyle))}function C(t){for(var e=F(t),r=[],n=0;n<e.length;n+=2){var i=parseFloat(e[n]),o=parseFloat(e[n+1]);r.push([i,o])}return r}function A(t,e,r,n,i){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(B(t,e),N(t,a,s),n||H(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=L(o,\"fill\",a.fill,r)),null!=a.stroke&&(o.style.stroke=L(o,\"stroke\",a.stroke,r)),Object(p[\"k\"])([\"lineWidth\",\"opacity\",\"fillOpacity\",\"strokeOpacity\",\"miterLimit\",\"fontSize\"],(function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))})),Object(p[\"k\"])([\"lineDashOffset\",\"lineCap\",\"lineJoin\",\"fontWeight\",\"fontFamily\",\"fontStyle\",\"textAlign\"],(function(t){null!=a[t]&&(o.style[t]=a[t])})),i&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=Object(p[\"H\"])(F(a.lineDash),(function(t){return parseFloat(t)}))),\"hidden\"!==a.visibility&&\"collapse\"!==a.visibility||(o.invisible=!0),\"none\"===a.display&&(o.ignore=!0)}function P(t,e){var r=e.__selfStyle;if(r){var n=r.textBaseline,i=n;n&&\"auto\"!==n?\"baseline\"===n?i=\"alphabetic\":\"before-edge\"===n||\"text-before-edge\"===n?i=\"top\":\"after-edge\"===n||\"text-after-edge\"===n?i=\"bottom\":\"central\"!==n&&\"mathematical\"!==n||(i=\"middle\"):i=\"alphabetic\",t.style.textBaseline=i}var o=e.__inheritedStyle;if(o){var a=o.textAlign,s=a;a&&(\"middle\"===a&&(s=\"center\"),t.style.textAlign=s)}}var M=/^url\\(\\s*#(.*?)\\)/;function L(t,e,r,n){var i=r&&r.match(M);if(!i)return\"none\"===r&&(r=null),r;var o=Object(p[\"T\"])(i[1]);n.push([t,e,o])}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n[0].style[n[1]]=t[n[2]]}}var D=/-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;function F(t){return t.match(D)||[]}var z=/(translate|scale|rotate|skewX|skewY|matrix)\\(([\\-\\s0-9\\.eE,]*)\\)/g,R=Math.PI/180;function B(t,e){var r=t.getAttribute(\"transform\");if(r){r=r.replace(/,/g,\" \");var n=[],i=null;r.replace(z,(function(t,e,r){return n.push(e,r),\"\"}));for(var o=n.length-1;o>0;o-=2){var a=n[o],s=n[o-1],u=F(a);switch(i=i||f[\"c\"](),s){case\"translate\":f[\"i\"](i,i,[parseFloat(u[0]),parseFloat(u[1]||\"0\")]);break;case\"scale\":f[\"h\"](i,i,[parseFloat(u[0]),parseFloat(u[1]||u[0])]);break;case\"rotate\":f[\"g\"](i,i,-parseFloat(u[0])*R,[parseFloat(u[1]||\"0\"),parseFloat(u[2]||\"0\")]);break;case\"skewX\":var c=Math.tan(parseFloat(u[0])*R);f[\"f\"](i,[1,0,c,1,0,0],i);break;case\"skewY\":var h=Math.tan(parseFloat(u[0])*R);f[\"f\"](i,[1,h,0,1,0,0],i);break;case\"matrix\":i[0]=parseFloat(u[0]),i[1]=parseFloat(u[1]),i[2]=parseFloat(u[2]),i[3]=parseFloat(u[3]),i[4]=parseFloat(u[4]),i[5]=parseFloat(u[5]);break}}e.setLocalTransform(i)}}var E=/([^\\s:;]+)\\s*:\\s*([^:;]+)/g;function N(t,e,r){var n=t.getAttribute(\"style\");if(n){var i;E.lastIndex=0;while(null!=(i=E.exec(n))){var o=i[1],a=Object(p[\"q\"])(_,o)?_[o]:null;a&&(e[a]=i[2]);var s=Object(p[\"q\"])(x,o)?x[o]:null;s&&(r[s]=i[2])}}}function H(t,e,r){for(var n=0;n<m.length;n++){var i=m[n],o=t.getAttribute(i);null!=o&&(e[_[i]]=o)}for(n=0;n<w.length;n++){i=w[n],o=t.getAttribute(i);null!=o&&(r[x[i]]=o)}}function q(t,e){var r=e.width/t.width,n=e.height/t.height,i=Math.min(r,n);return{scale:i,x:-(t.x+t.width/2)*i+(e.x+e.width/2),y:-(t.y+t.height/2)*i+(e.y+e.height/2)}}function W(t,e){var r=new O;return r.parse(t,e)}},3280:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"e58c\"),o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"lastIndexOf\",(function(t){return i.apply(o(this),arguments)}))},\"342d\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return j})),r.d(e,\"c\",(function(){return C})),r.d(e,\"d\",(function(){return A})),r.d(e,\"a\",(function(){return P}));var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"20c8\"),a=r(\"401b\"),s=o[\"a\"].CMD,u=[[],[],[]],c=Math.sqrt,h=Math.atan2;function l(t,e){if(e){var r,n,i,o,l,f,d=t.data,p=t.len(),v=s.M,y=s.C,g=s.L,b=s.R,_=s.A,m=s.Q;for(i=0,o=0;i<p;){switch(r=d[i++],o=i,n=0,r){case v:n=1;break;case g:n=1;break;case y:n=3;break;case m:n=2;break;case _:var x=e[4],w=e[5],O=c(e[0]*e[0]+e[1]*e[1]),k=c(e[2]*e[2]+e[3]*e[3]),T=h(-e[1]/k,e[0]/O);d[i]*=O,d[i++]+=x,d[i]*=k,d[i++]+=w,d[i++]*=O,d[i++]*=k,d[i++]+=T,d[i++]+=T,i+=2,o=i;break;case b:f[0]=d[i++],f[1]=d[i++],Object(a[\"b\"])(f,f,e),d[o++]=f[0],d[o++]=f[1],f[0]+=d[i++],f[1]+=d[i++],Object(a[\"b\"])(f,f,e),d[o++]=f[0],d[o++]=f[1]}for(l=0;l<n;l++){var S=u[l];S[0]=d[i++],S[1]=d[i++],Object(a[\"b\"])(S,S,e),d[o++]=S[0],d[o++]=S[1]}}t.increaseVersion()}}var f=r(\"6d8b\"),d=Math.sqrt,p=Math.sin,v=Math.cos,y=Math.PI;function g(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function b(t,e){return(t[0]*e[0]+t[1]*e[1])/(g(t)*g(e))}function _(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(b(t,e))}function m(t,e,r,n,i,o,a,s,u,c,h){var l=u*(y/180),f=v(l)*(t-r)/2+p(l)*(e-n)/2,g=-1*p(l)*(t-r)/2+v(l)*(e-n)/2,m=f*f/(a*a)+g*g/(s*s);m>1&&(a*=d(m),s*=d(m));var x=(i===o?-1:1)*d((a*a*(s*s)-a*a*(g*g)-s*s*(f*f))/(a*a*(g*g)+s*s*(f*f)))||0,w=x*a*g/s,O=x*-s*f/a,k=(t+r)/2+v(l)*w-p(l)*O,T=(e+n)/2+p(l)*w+v(l)*O,S=_([1,0],[(f-w)/a,(g-O)/s]),j=[(f-w)/a,(g-O)/s],C=[(-1*f-w)/a,(-1*g-O)/s],A=_(j,C);if(b(j,C)<=-1&&(A=y),b(j,C)>=1&&(A=0),A<0){var P=Math.round(A/y*1e6)/1e6;A=2*y+P%2*y}h.addData(c,k,T,a,s,S,A,l,o)}var x=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,w=/-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;function O(t){var e=new o[\"a\"];if(!t)return e;var r,n=0,i=0,a=n,s=i,u=o[\"a\"].CMD,c=t.match(x);if(!c)return e;for(var h=0;h<c.length;h++){for(var l=c[h],f=l.charAt(0),d=void 0,p=l.match(w)||[],v=p.length,y=0;y<v;y++)p[y]=parseFloat(p[y]);var g=0;while(g<v){var b=void 0,_=void 0,O=void 0,k=void 0,T=void 0,S=void 0,j=void 0,C=n,A=i,P=void 0,M=void 0;switch(f){case\"l\":n+=p[g++],i+=p[g++],d=u.L,e.addData(d,n,i);break;case\"L\":n=p[g++],i=p[g++],d=u.L,e.addData(d,n,i);break;case\"m\":n+=p[g++],i+=p[g++],d=u.M,e.addData(d,n,i),a=n,s=i,f=\"l\";break;case\"M\":n=p[g++],i=p[g++],d=u.M,e.addData(d,n,i),a=n,s=i,f=\"L\";break;case\"h\":n+=p[g++],d=u.L,e.addData(d,n,i);break;case\"H\":n=p[g++],d=u.L,e.addData(d,n,i);break;case\"v\":i+=p[g++],d=u.L,e.addData(d,n,i);break;case\"V\":i=p[g++],d=u.L,e.addData(d,n,i);break;case\"C\":d=u.C,e.addData(d,p[g++],p[g++],p[g++],p[g++],p[g++],p[g++]),n=p[g-2],i=p[g-1];break;case\"c\":d=u.C,e.addData(d,p[g++]+n,p[g++]+i,p[g++]+n,p[g++]+i,p[g++]+n,p[g++]+i),n+=p[g-2],i+=p[g-1];break;case\"S\":b=n,_=i,P=e.len(),M=e.data,r===u.C&&(b+=n-M[P-4],_+=i-M[P-3]),d=u.C,C=p[g++],A=p[g++],n=p[g++],i=p[g++],e.addData(d,b,_,C,A,n,i);break;case\"s\":b=n,_=i,P=e.len(),M=e.data,r===u.C&&(b+=n-M[P-4],_+=i-M[P-3]),d=u.C,C=n+p[g++],A=i+p[g++],n+=p[g++],i+=p[g++],e.addData(d,b,_,C,A,n,i);break;case\"Q\":C=p[g++],A=p[g++],n=p[g++],i=p[g++],d=u.Q,e.addData(d,C,A,n,i);break;case\"q\":C=p[g++]+n,A=p[g++]+i,n+=p[g++],i+=p[g++],d=u.Q,e.addData(d,C,A,n,i);break;case\"T\":b=n,_=i,P=e.len(),M=e.data,r===u.Q&&(b+=n-M[P-4],_+=i-M[P-3]),n=p[g++],i=p[g++],d=u.Q,e.addData(d,b,_,n,i);break;case\"t\":b=n,_=i,P=e.len(),M=e.data,r===u.Q&&(b+=n-M[P-4],_+=i-M[P-3]),n+=p[g++],i+=p[g++],d=u.Q,e.addData(d,b,_,n,i);break;case\"A\":O=p[g++],k=p[g++],T=p[g++],S=p[g++],j=p[g++],C=n,A=i,n=p[g++],i=p[g++],d=u.A,m(C,A,n,i,S,j,O,k,T,d,e);break;case\"a\":O=p[g++],k=p[g++],T=p[g++],S=p[g++],j=p[g++],C=n,A=i,n+=p[g++],i+=p[g++],d=u.A,m(C,A,n,i,S,j,O,k,T,d,e);break}}\"z\"!==f&&\"Z\"!==f||(d=u.Z,e.addData(d),n=a,i=s),r=d}return e.toStatic(),e}var k=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(n[\"a\"])(e,t),e.prototype.applyTransform=function(t){},e}(i[\"b\"]);function T(t){return null!=t.setData}function S(t,e){var r=O(t),n=Object(f[\"m\"])({},e);return n.buildPath=function(t){if(T(t)){t.setData(r.data);var e=t.getContext();e&&t.rebuildPath(e,1)}else{e=t;r.rebuildPath(e,1)}},n.applyTransform=function(t){l(r,t),this.dirtyShape()},n}function j(t,e){return new k(S(t,e))}function C(t,e){var r=S(t,e),i=function(t){function e(e){var n=t.call(this,e)||this;return n.applyTransform=r.applyTransform,n.buildPath=r.buildPath,n}return Object(n[\"a\"])(e,t),e}(k);return i}function A(t,e){for(var r=[],n=t.length,o=0;o<n;o++){var a=t[o];r.push(a.getUpdatedPathProxy(!0))}var s=new i[\"b\"](e);return s.createPathProxy(),s.buildPath=function(t){if(T(t)){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e,1)}},s}function P(t,e){e=e||{};var r=new i[\"b\"];return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?l(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}},3437:function(t,e,r){\"use strict\";function n(t){return isFinite(t)}function i(t,e,r){var i=null==e.x?0:e.x,o=null==e.x2?1:e.x2,a=null==e.y?0:e.y,s=null==e.y2?0:e.y2;e.global||(i=i*r.width+r.x,o=o*r.width+r.x,a=a*r.height+r.y,s=s*r.height+r.y),i=n(i)?i:0,o=n(o)?o:1,a=n(a)?a:0,s=n(s)?s:0;var u=t.createLinearGradient(i,a,o,s);return u}function o(t,e,r){var i=r.width,o=r.height,a=Math.min(i,o),s=null==e.x?.5:e.x,u=null==e.y?.5:e.y,c=null==e.r?.5:e.r;e.global||(s=s*i+r.x,u=u*o+r.y,c*=a),s=n(s)?s:.5,u=n(u)?u:.5,c=c>=0&&n(c)?c:.5;var h=t.createRadialGradient(s,u,0,s,u,c);return h}function a(t,e,r){for(var n=\"radial\"===e.type?o(t,e,r):i(t,e,r),a=e.colorStops,s=0;s<a.length;s++)n.addColorStop(a[s].offset,a[s].color);return n}function s(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!0;return!1}function u(t){return parseInt(t,10)}function c(t,e,r){var n=[\"width\",\"height\"][e],i=[\"clientWidth\",\"clientHeight\"][e],o=[\"paddingLeft\",\"paddingTop\"][e],a=[\"paddingRight\",\"paddingBottom\"][e];if(null!=r[n]&&\"auto\"!==r[n])return parseFloat(r[n]);var s=document.defaultView.getComputedStyle(t);return(t[i]||u(s[n])||u(t.style[n]))-(u(s[o])||0)-(u(s[a])||0)|0}r.d(e,\"a\",(function(){return a})),r.d(e,\"c\",(function(){return s})),r.d(e,\"b\",(function(){return c}))},\"392f\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"19eb\"),o=r(\"9850\"),a=[],s=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return Object(n[\"a\"])(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new o[\"a\"](1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],n=r.getBoundingRect().clone();r.needLocalTransform()&&n.applyTransform(r.getLocalTransform(a)),t.union(n)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),n=this.getBoundingRect();if(n.contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++){var o=this._displayables[i];if(o.contain(t,e))return!0}return!1},e}(i[\"c\"]);e[\"a\"]=s},\"3a7b\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").findIndex,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"findIndex\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},\"3c5d\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"50c4\"),o=r(\"182d\"),a=r(\"7b0b\"),s=r(\"d039\"),u=n.aTypedArray,c=n.exportTypedArrayMethod,h=s((function(){new Int8Array(1).set({})}));c(\"set\",(function(t){u(this);var e=o(arguments.length>1?arguments[1]:void 0,1),r=this.length,n=a(t),s=i(n.length),c=0;if(s+e>r)throw RangeError(\"Wrong length\");while(c<s)this[e+c]=n[c++]}),h)},\"3fcc\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").map,o=r(\"4840\"),a=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod;u(\"map\",(function(t){return i(a(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(s(o(t,t.constructor)))(e)}))}))},\"401b\":function(t,e,r){\"use strict\";function n(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function i(t,e){return t[0]=e[0],t[1]=e[1],t}function o(t){return[t[0],t[1]]}function a(t,e,r){return t[0]=e,t[1]=r,t}function s(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function u(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t}function c(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function h(t){return Math.sqrt(l(t))}r.d(e,\"e\",(function(){return n})),r.d(e,\"d\",(function(){return i})),r.d(e,\"c\",(function(){return o})),r.d(e,\"p\",(function(){return a})),r.d(e,\"a\",(function(){return s})),r.d(e,\"o\",(function(){return u})),r.d(e,\"q\",(function(){return c})),r.d(e,\"i\",(function(){return h})),r.d(e,\"n\",(function(){return f})),r.d(e,\"m\",(function(){return d})),r.d(e,\"h\",(function(){return p})),r.d(e,\"f\",(function(){return v})),r.d(e,\"g\",(function(){return g})),r.d(e,\"j\",(function(){return b})),r.d(e,\"b\",(function(){return _})),r.d(e,\"l\",(function(){return m})),r.d(e,\"k\",(function(){return x}));function l(t){return t[0]*t[0]+t[1]*t[1]}function f(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function d(t,e){var r=h(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function p(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var v=p;function y(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var g=y;function b(t,e,r,n){return t[0]=e[0]+n*(r[0]-e[0]),t[1]=e[1]+n*(r[1]-e[1]),t}function _(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i+r[4],t[1]=r[1]*n+r[3]*i+r[5],t}function m(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function x(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}},\"41ef\":function(t,e,r){\"use strict\";r.r(e),r.d(e,\"parse\",(function(){return b})),r.d(e,\"lift\",(function(){return x})),r.d(e,\"toHex\",(function(){return w})),r.d(e,\"fastLerp\",(function(){return O})),r.d(e,\"fastMapToColor\",(function(){return k})),r.d(e,\"lerp\",(function(){return T})),r.d(e,\"mapToColor\",(function(){return S})),r.d(e,\"modifyHSL\",(function(){return j})),r.d(e,\"modifyAlpha\",(function(){return C})),r.d(e,\"stringify\",(function(){return A})),r.d(e,\"lum\",(function(){return P})),r.d(e,\"random\",(function(){return M})),r.d(e,\"liftColor\",(function(){return I}));var n=r(\"d51b\"),i=r(\"6d8b\"),o={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return t=Math.round(t),t<0?0:t>255?255:t}function s(t){return t=Math.round(t),t<0?0:t>360?360:t}function u(t){return t<0?0:t>1?1:t}function c(t){var e=t;return e.length&&\"%\"===e.charAt(e.length-1)?a(parseFloat(e)/100*255):a(parseInt(e,10))}function h(t){var e=t;return e.length&&\"%\"===e.charAt(e.length-1)?u(parseFloat(e)/100):u(parseFloat(e))}function l(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function f(t,e,r){return t+(e-t)*r}function d(t,e,r,n,i){return t[0]=e,t[1]=r,t[2]=n,t[3]=i,t}function p(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var v=new n[\"a\"](20),y=null;function g(t,e){y&&p(y,e),y=v.put(t,y||e.slice())}function b(t,e){if(t){e=e||[];var r=v.get(t);if(r)return p(e,r);t+=\"\";var n=t.replace(/ /g,\"\").toLowerCase();if(n in o)return p(e,o[n]),g(t,e),e;var i=n.length;if(\"#\"!==n.charAt(0)){var a=n.indexOf(\"(\"),s=n.indexOf(\")\");if(-1!==a&&s+1===i){var u=n.substr(0,a),l=n.substr(a+1,s-(a+1)).split(\",\"),f=1;switch(u){case\"rgba\":if(4!==l.length)return 3===l.length?d(e,+l[0],+l[1],+l[2],1):d(e,0,0,0,1);f=h(l.pop());case\"rgb\":return l.length>=3?(d(e,c(l[0]),c(l[1]),c(l[2]),3===l.length?f:h(l[3])),g(t,e),e):void d(e,0,0,0,1);case\"hsla\":return 4!==l.length?void d(e,0,0,0,1):(l[3]=h(l[3]),_(l,e),g(t,e),e);case\"hsl\":return 3!==l.length?void d(e,0,0,0,1):(_(l,e),g(t,e),e);default:return}}d(e,0,0,0,1)}else{if(4===i||5===i){var y=parseInt(n.slice(1,4),16);return y>=0&&y<=4095?(d(e,(3840&y)>>4|(3840&y)>>8,240&y|(240&y)>>4,15&y|(15&y)<<4,5===i?parseInt(n.slice(4),16)/15:1),g(t,e),e):void d(e,0,0,0,1)}if(7===i||9===i){y=parseInt(n.slice(1,7),16);return y>=0&&y<=16777215?(d(e,(16711680&y)>>16,(65280&y)>>8,255&y,9===i?parseInt(n.slice(7),16)/255:1),g(t,e),e):void d(e,0,0,0,1)}}}}function _(t,e){var r=(parseFloat(t[0])%360+360)%360/360,n=h(t[1]),i=h(t[2]),o=i<=.5?i*(n+1):i+n-i*n,s=2*i-o;return e=e||[],d(e,a(255*l(s,o,r+1/3)),a(255*l(s,o,r)),a(255*l(s,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function m(t){if(t){var e,r,n=t[0]/255,i=t[1]/255,o=t[2]/255,a=Math.min(n,i,o),s=Math.max(n,i,o),u=s-a,c=(s+a)/2;if(0===u)e=0,r=0;else{r=c<.5?u/(s+a):u/(2-s-a);var h=((s-n)/6+u/2)/u,l=((s-i)/6+u/2)/u,f=((s-o)/6+u/2)/u;n===s?e=f-l:i===s?e=1/3+h-f:o===s&&(e=2/3+l-h),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,r,c];return null!=t[3]&&d.push(t[3]),d}}function x(t,e){var r=b(t);if(r){for(var n=0;n<3;n++)r[n]=e<0?r[n]*(1-e)|0:(255-r[n])*e+r[n]|0,r[n]>255?r[n]=255:r[n]<0&&(r[n]=0);return A(r,4===r.length?\"rgba\":\"rgb\")}}function w(t){var e=b(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function O(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var n=t*(e.length-1),i=Math.floor(n),o=Math.ceil(n),s=e[i],c=e[o],h=n-i;return r[0]=a(f(s[0],c[0],h)),r[1]=a(f(s[1],c[1],h)),r[2]=a(f(s[2],c[2],h)),r[3]=u(f(s[3],c[3],h)),r}}var k=O;function T(t,e,r){if(e&&e.length&&t>=0&&t<=1){var n=t*(e.length-1),i=Math.floor(n),o=Math.ceil(n),s=b(e[i]),c=b(e[o]),h=n-i,l=A([a(f(s[0],c[0],h)),a(f(s[1],c[1],h)),a(f(s[2],c[2],h)),u(f(s[3],c[3],h))],\"rgba\");return r?{color:l,leftIndex:i,rightIndex:o,value:n}:l}}var S=T;function j(t,e,r,n){var i=b(t);if(t)return i=m(i),null!=e&&(i[0]=s(e)),null!=r&&(i[1]=h(r)),null!=n&&(i[2]=h(n)),A(_(i),\"rgba\")}function C(t,e){var r=b(t);if(r&&null!=e)return r[3]=u(e),A(r,\"rgba\")}function A(t,e){if(t&&t.length){var r=t[0]+\",\"+t[1]+\",\"+t[2];return\"rgba\"!==e&&\"hsva\"!==e&&\"hsla\"!==e||(r+=\",\"+t[3]),e+\"(\"+r+\")\"}}function P(t,e){var r=b(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}function M(){return A([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],\"rgb\")}var L=new n[\"a\"](100);function I(t){if(Object(i[\"C\"])(t)){var e=L.get(t);return e||(e=x(t,-.1),L.put(t,e)),e}if(Object(i[\"x\"])(t)){var r=Object(i[\"m\"])({},t);return r.colorStops=Object(i[\"H\"])(t.colorStops,(function(t){return{offset:t.offset,color:x(t.color,-.1)}})),r}return t}},\"42e5\":function(t,e,r){\"use strict\";var n=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();e[\"a\"]=n},4573:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var r=e.cx,n=e.cy,i=2*Math.PI;t.moveTo(r+e.r,n),t.arc(r,n,e.r,0,i,!1),t.moveTo(r+e.r0,n),t.arc(r,n,e.r0,0,i,!0)},e}(i[\"b\"]);a.prototype.type=\"ring\",e[\"a\"]=a},4755:function(t,e,r){\"use strict\";var n=Math.round(9*Math.random()),i=\"function\"===typeof Object.defineProperty,o=function(){function t(){this._id=\"__ec_inner_\"+n++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var r=this._guard(t);return i?Object.defineProperty(r,this._id,{value:e,enumerable:!1,configurable:!0}):r[this._id]=e,this},t.prototype[\"delete\"]=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError(\"Value of WeakMap is not a non-null object.\");return t},t}();e[\"a\"]=o},\"48a9\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"42e5\"),o=function(t){function e(e,r,n,i,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==r?0:r,s.x2=null==n?1:n,s.y2=null==i?0:i,s.type=\"linear\",s.global=a||!1,s}return Object(n[\"a\"])(e,t),e}(i[\"a\"]);e[\"a\"]=o},\"4a3f\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return v})),r.d(e,\"b\",(function(){return y})),r.d(e,\"f\",(function(){return g})),r.d(e,\"c\",(function(){return b})),r.d(e,\"g\",(function(){return _})),r.d(e,\"e\",(function(){return m})),r.d(e,\"d\",(function(){return x})),r.d(e,\"h\",(function(){return w})),r.d(e,\"i\",(function(){return O})),r.d(e,\"m\",(function(){return k})),r.d(e,\"j\",(function(){return T})),r.d(e,\"n\",(function(){return S})),r.d(e,\"l\",(function(){return j})),r.d(e,\"k\",(function(){return C}));var n=r(\"401b\"),i=Math.pow,o=Math.sqrt,a=1e-8,s=1e-4,u=o(3),c=1/3,h=Object(n[\"e\"])(),l=Object(n[\"e\"])(),f=Object(n[\"e\"])();function d(t){return t>-a&&t<a}function p(t){return t>a||t<-a}function v(t,e,r,n,i){var o=1-i;return o*o*(o*t+3*i*e)+i*i*(i*n+3*o*r)}function y(t,e,r,n,i){var o=1-i;return 3*(((e-t)*o+2*(r-e)*i)*o+(n-r)*i*i)}function g(t,e,r,n,a,s){var h=n+3*(e-r)-t,l=3*(r-2*e+t),f=3*(e-t),p=t-a,v=l*l-3*h*f,y=l*f-9*h*p,g=f*f-3*l*p,b=0;if(d(v)&&d(y))if(d(l))s[0]=0;else{var _=-f/l;_>=0&&_<=1&&(s[b++]=_)}else{var m=y*y-4*v*g;if(d(m)){var x=y/v,w=(_=-l/h+x,-x/2);_>=0&&_<=1&&(s[b++]=_),w>=0&&w<=1&&(s[b++]=w)}else if(m>0){var O=o(m),k=v*l+1.5*h*(-y+O),T=v*l+1.5*h*(-y-O);k=k<0?-i(-k,c):i(k,c),T=T<0?-i(-T,c):i(T,c);_=(-l-(k+T))/(3*h);_>=0&&_<=1&&(s[b++]=_)}else{var S=(2*v*l-3*h*y)/(2*o(v*v*v)),j=Math.acos(S)/3,C=o(v),A=Math.cos(j),P=(_=(-l-2*C*A)/(3*h),w=(-l+C*(A+u*Math.sin(j)))/(3*h),(-l+C*(A-u*Math.sin(j)))/(3*h));_>=0&&_<=1&&(s[b++]=_),w>=0&&w<=1&&(s[b++]=w),P>=0&&P<=1&&(s[b++]=P)}}return b}function b(t,e,r,n,i){var a=6*r-12*e+6*t,s=9*e+3*n-3*t-9*r,u=3*e-3*t,c=0;if(d(s)){if(p(a)){var h=-u/a;h>=0&&h<=1&&(i[c++]=h)}}else{var l=a*a-4*s*u;if(d(l))i[0]=-a/(2*s);else if(l>0){var f=o(l),v=(h=(-a+f)/(2*s),(-a-f)/(2*s));h>=0&&h<=1&&(i[c++]=h),v>=0&&v<=1&&(i[c++]=v)}}return c}function _(t,e,r,n,i,o){var a=(e-t)*i+t,s=(r-e)*i+e,u=(n-r)*i+r,c=(s-a)*i+a,h=(u-s)*i+s,l=(h-c)*i+c;o[0]=t,o[1]=a,o[2]=c,o[3]=l,o[4]=l,o[5]=h,o[6]=u,o[7]=n}function m(t,e,r,i,a,u,c,d,p,y,g){var b,_,m,x,w,O=.005,k=1/0;h[0]=p,h[1]=y;for(var T=0;T<1;T+=.05)l[0]=v(t,r,a,c,T),l[1]=v(e,i,u,d,T),x=Object(n[\"g\"])(h,l),x<k&&(b=T,k=x);k=1/0;for(var S=0;S<32;S++){if(O<s)break;_=b-O,m=b+O,l[0]=v(t,r,a,c,_),l[1]=v(e,i,u,d,_),x=Object(n[\"g\"])(l,h),_>=0&&x<k?(b=_,k=x):(f[0]=v(t,r,a,c,m),f[1]=v(e,i,u,d,m),w=Object(n[\"g\"])(f,h),m<=1&&w<k?(b=m,k=w):O*=.5)}return g&&(g[0]=v(t,r,a,c,b),g[1]=v(e,i,u,d,b)),o(k)}function x(t,e,r,n,i,o,a,s,u){for(var c=t,h=e,l=0,f=1/u,d=1;d<=u;d++){var p=d*f,y=v(t,r,i,a,p),g=v(e,n,o,s,p),b=y-c,_=g-h;l+=Math.sqrt(b*b+_*_),c=y,h=g}return l}function w(t,e,r,n){var i=1-n;return i*(i*t+2*n*e)+n*n*r}function O(t,e,r,n){return 2*((1-n)*(e-t)+n*(r-e))}function k(t,e,r,n,i){var a=t-2*e+r,s=2*(e-t),u=t-n,c=0;if(d(a)){if(p(s)){var h=-u/s;h>=0&&h<=1&&(i[c++]=h)}}else{var l=s*s-4*a*u;if(d(l)){h=-s/(2*a);h>=0&&h<=1&&(i[c++]=h)}else if(l>0){var f=o(l),v=(h=(-s+f)/(2*a),(-s-f)/(2*a));h>=0&&h<=1&&(i[c++]=h),v>=0&&v<=1&&(i[c++]=v)}}return c}function T(t,e,r){var n=t+r-2*e;return 0===n?.5:(t-e)/n}function S(t,e,r,n,i){var o=(e-t)*n+t,a=(r-e)*n+e,s=(a-o)*n+o;i[0]=t,i[1]=o,i[2]=s,i[3]=s,i[4]=a,i[5]=r}function j(t,e,r,i,a,u,c,d,p){var v,y=.005,g=1/0;h[0]=c,h[1]=d;for(var b=0;b<1;b+=.05){l[0]=w(t,r,a,b),l[1]=w(e,i,u,b);var _=Object(n[\"g\"])(h,l);_<g&&(v=b,g=_)}g=1/0;for(var m=0;m<32;m++){if(y<s)break;var x=v-y,O=v+y;l[0]=w(t,r,a,x),l[1]=w(e,i,u,x);_=Object(n[\"g\"])(l,h);if(x>=0&&_<g)v=x,g=_;else{f[0]=w(t,r,a,O),f[1]=w(e,i,u,O);var k=Object(n[\"g\"])(f,h);O<=1&&k<g?(v=O,g=k):y*=.5}}return p&&(p[0]=w(t,r,a,v),p[1]=w(e,i,u,v)),o(g)}function C(t,e,r,n,i,o,a){for(var s=t,u=e,c=0,h=1/a,l=1;l<=a;l++){var f=l*h,d=w(t,r,i,f),p=w(e,n,o,f),v=d-s,y=p-u;c+=Math.sqrt(v*v+y*y),s=d,u=p}return c}},\"4a80\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return i}));var n=r(\"6d8b\");function i(t){if(Object(n[\"C\"])(t)){var e=new DOMParser;t=e.parseFromString(t,\"text/xml\")}var r=t;9===r.nodeType&&(r=r.firstChild);while(\"svg\"!==r.nodeName.toLowerCase()||1!==r.nodeType)r=r.nextSibling;return r}},\"4aa2\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"6d8b\"),a=Math.PI,s=2*a,u=Math.sin,c=Math.cos,h=Math.acos,l=Math.atan2,f=Math.abs,d=Math.sqrt,p=Math.max,v=Math.min,y=1e-4;function g(t,e,r,n,i,o,a,s){var u=r-t,c=n-e,h=a-i,l=s-o,f=l*u-h*c;if(!(f*f<y))return f=(h*(e-o)-l*(t-i))/f,[t+f*u,e+f*c]}function b(t,e,r,n,i,o,a){var s=t-r,u=e-n,c=(a?o:-o)/d(s*s+u*u),h=c*u,l=-c*s,f=t+h,v=e+l,y=r+h,g=n+l,b=(f+y)/2,_=(v+g)/2,m=y-f,x=g-v,w=m*m+x*x,O=i-o,k=f*g-y*v,T=(x<0?-1:1)*d(p(0,O*O*w-k*k)),S=(k*x-m*T)/w,j=(-k*m-x*T)/w,C=(k*x+m*T)/w,A=(-k*m+x*T)/w,P=S-b,M=j-_,L=C-b,I=A-_;return P*P+M*M>L*L+I*I&&(S=C,j=A),{cx:S,cy:j,x0:-h,y0:-l,x1:S*(i/O-1),y1:j*(i/O-1)}}function _(t){var e;if(Object(o[\"t\"])(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}function m(t,e){var r,n=p(e.r,0),i=p(e.r0||0,0),o=n>0,m=i>0;if(o||m){if(o||(n=i,i=0),i>n){var x=n;n=i,i=x}var w=e.startAngle,O=e.endAngle;if(!isNaN(w)&&!isNaN(O)){var k=e.cx,T=e.cy,S=!!e.clockwise,j=f(O-w),C=j>s&&j%s;if(C>y&&(j=C),n>y)if(j>s-y)t.moveTo(k+n*c(w),T+n*u(w)),t.arc(k,T,n,w,O,!S),i>y&&(t.moveTo(k+i*c(O),T+i*u(O)),t.arc(k,T,i,O,w,S));else{var A=void 0,P=void 0,M=void 0,L=void 0,I=void 0,D=void 0,F=void 0,z=void 0,R=void 0,B=void 0,E=void 0,N=void 0,H=void 0,q=void 0,W=void 0,Y=void 0,X=n*c(w),V=n*u(w),U=i*c(O),$=i*u(O),Z=j>y;if(Z){var G=e.cornerRadius;G&&(r=_(G),A=r[0],P=r[1],M=r[2],L=r[3]);var Q=f(n-i)/2;if(I=v(Q,M),D=v(Q,L),F=v(Q,A),z=v(Q,P),E=R=p(I,D),N=B=p(F,z),(R>y||B>y)&&(H=n*c(O),q=n*u(O),W=i*c(w),Y=i*u(w),j<a)){var K=g(X,V,W,Y,H,q,U,$);if(K){var J=X-K[0],tt=V-K[1],et=H-K[0],rt=q-K[1],nt=1/u(h((J*et+tt*rt)/(d(J*J+tt*tt)*d(et*et+rt*rt)))/2),it=d(K[0]*K[0]+K[1]*K[1]);E=v(R,(n-it)/(nt+1)),N=v(B,(i-it)/(nt-1))}}}if(Z)if(E>y){var ot=v(M,E),at=v(L,E),st=b(W,Y,X,V,n,ot,S),ut=b(H,q,U,$,n,at,S);t.moveTo(k+st.cx+st.x0,T+st.cy+st.y0),E<R&&ot===at?t.arc(k+st.cx,T+st.cy,E,l(st.y0,st.x0),l(ut.y0,ut.x0),!S):(ot>0&&t.arc(k+st.cx,T+st.cy,ot,l(st.y0,st.x0),l(st.y1,st.x1),!S),t.arc(k,T,n,l(st.cy+st.y1,st.cx+st.x1),l(ut.cy+ut.y1,ut.cx+ut.x1),!S),at>0&&t.arc(k+ut.cx,T+ut.cy,at,l(ut.y1,ut.x1),l(ut.y0,ut.x0),!S))}else t.moveTo(k+X,T+V),t.arc(k,T,n,w,O,!S);else t.moveTo(k+X,T+V);if(i>y&&Z)if(N>y){ot=v(A,N),at=v(P,N),st=b(U,$,H,q,i,-at,S),ut=b(X,V,W,Y,i,-ot,S);t.lineTo(k+st.cx+st.x0,T+st.cy+st.y0),N<B&&ot===at?t.arc(k+st.cx,T+st.cy,N,l(st.y0,st.x0),l(ut.y0,ut.x0),!S):(at>0&&t.arc(k+st.cx,T+st.cy,at,l(st.y0,st.x0),l(st.y1,st.x1),!S),t.arc(k,T,i,l(st.cy+st.y1,st.cx+st.x1),l(ut.cy+ut.y1,ut.cx+ut.x1),S),ot>0&&t.arc(k+ut.cx,T+ut.cy,ot,l(ut.y1,ut.x1),l(ut.y0,ut.x0),!S))}else t.lineTo(k+U,T+$),t.arc(k,T,i,O,w,S);else t.lineTo(k+U,T+$)}else t.moveTo(k,T);t.closePath()}}}var x=function(){function t(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0}return t}(),w=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new x},e.prototype.buildPath=function(t,e){m(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(i[\"b\"]);w.prototype.type=\"sector\";e[\"a\"]=w},\"4bc4\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return n})),r.d(e,\"c\",(function(){return i})),r.d(e,\"b\",(function(){return o}));var n=1,i=2,o=4},\"4fac\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return o}));var n=r(\"401b\");function i(t,e,r,i){var o,a,s,u,c=[],h=[],l=[],f=[];if(i){s=[1/0,1/0],u=[-1/0,-1/0];for(var d=0,p=t.length;d<p;d++)Object(n[\"l\"])(s,s,t[d]),Object(n[\"k\"])(u,u,t[d]);Object(n[\"l\"])(s,s,i[0]),Object(n[\"k\"])(u,u,i[1])}for(d=0,p=t.length;d<p;d++){var v=t[d];if(r)o=t[d?d-1:p-1],a=t[(d+1)%p];else{if(0===d||d===p-1){c.push(Object(n[\"c\"])(t[d]));continue}o=t[d-1],a=t[d+1]}Object(n[\"q\"])(h,a,o),Object(n[\"n\"])(h,h,e);var y=Object(n[\"h\"])(v,o),g=Object(n[\"h\"])(v,a),b=y+g;0!==b&&(y/=b,g/=b),Object(n[\"n\"])(l,h,-y),Object(n[\"n\"])(f,h,g);var _=Object(n[\"a\"])([],v,l),m=Object(n[\"a\"])([],v,f);i&&(Object(n[\"k\"])(_,_,s),Object(n[\"l\"])(_,_,u),Object(n[\"k\"])(m,m,s),Object(n[\"l\"])(m,m,u)),c.push(_),c.push(m)}return r&&c.push(c.shift()),c}function o(t,e,r){var n=e.smooth,o=e.points;if(o&&o.length>=2){if(n){var a=i(o,n,r,e.smoothConstraint);t.moveTo(o[0][0],o[0][1]);for(var s=o.length,u=0;u<(r?s:s-1);u++){var c=a[2*u],h=a[2*u+1],l=o[(u+1)%s];t.bezierCurveTo(c[0],c[1],h[0],h[1],l[0],l[1])}}else{t.moveTo(o[0][0],o[0][1]);u=1;for(var f=o.length;u<f;u++)t.lineTo(o[u][0],o[u][1])}r&&t.closePath()}}},5210:function(t,e,r){\"use strict\";r.d(e,\"c\",(function(){return m})),r.d(e,\"b\",(function(){return E})),r.d(e,\"a\",(function(){return N}));var n=r(\"19eb\"),i=r(\"20c8\"),o=r(\"5e76\"),a=r(\"3437\"),s=r(\"cbe5\"),u=r(\"0da8\"),c=r(\"dd4f\"),h=r(\"6d8b\"),l=r(\"8d1d\"),f=r(\"4bc4\"),d=r(\"726e\"),p=new i[\"a\"](!0);function v(t){var e=t.stroke;return!(null==e||\"none\"===e||!(t.lineWidth>0))}function y(t){return\"string\"===typeof t&&\"none\"!==t}function g(t){var e=t.fill;return null!=e&&\"none\"!==e}function b(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var r=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r}else t.fill()}function _(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var r=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r}else t.stroke()}function m(t,e,r){var n=Object(o[\"a\"])(e.image,e.__image,r);if(Object(o[\"c\"])(n)){var i=t.createPattern(n,e.repeat||\"repeat\");if(\"function\"===typeof DOMMatrix&&i&&i.setTransform){var a=new DOMMatrix;a.translateSelf(e.x||0,e.y||0),a.rotateSelf(0,0,(e.rotation||0)*h[\"a\"]),a.scaleSelf(e.scaleX||1,e.scaleY||1),i.setTransform(a)}return i}}function x(t,e,r,n){var i,o=v(r),s=g(r),u=r.strokePercent,c=u<1,h=!e.path;e.silent&&!c||!h||e.createPathProxy();var d=e.path||p,y=e.__dirty;if(!n){var x=r.fill,w=r.stroke,O=s&&!!x.colorStops,k=o&&!!w.colorStops,T=s&&!!x.image,S=o&&!!w.image,j=void 0,C=void 0,A=void 0,P=void 0,M=void 0;(O||k)&&(M=e.getBoundingRect()),O&&(j=y?Object(a[\"a\"])(t,x,M):e.__canvasFillGradient,e.__canvasFillGradient=j),k&&(C=y?Object(a[\"a\"])(t,w,M):e.__canvasStrokeGradient,e.__canvasStrokeGradient=C),T&&(A=y||!e.__canvasFillPattern?m(t,x,e):e.__canvasFillPattern,e.__canvasFillPattern=A),S&&(P=y||!e.__canvasStrokePattern?m(t,w,e):e.__canvasStrokePattern,e.__canvasStrokePattern=A),O?t.fillStyle=j:T&&(A?t.fillStyle=A:s=!1),k?t.strokeStyle=C:S&&(P?t.strokeStyle=P:o=!1)}var L,I,D=e.getGlobalScale();d.setScale(D[0],D[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(i=Object(l[\"a\"])(e),L=i[0],I=i[1]);var F=!0;(h||y&f[\"b\"])&&(d.setDPR(t.dpr),c?d.setContext(null):(d.setContext(t),F=!1),d.reset(),e.buildPath(d,e.shape,n),d.toStatic(),e.pathUpdated()),F&&d.rebuildPath(t,c?u:1),L&&(t.setLineDash(L),t.lineDashOffset=I),n||(r.strokeFirst?(o&&_(t,r),s&&b(t,r)):(s&&b(t,r),o&&_(t,r))),L&&t.setLineDash([])}function w(t,e,r){var n=e.__image=Object(o[\"a\"])(r.image,e.__image,e,e.onload);if(n&&Object(o[\"c\"])(n)){var i=r.x||0,a=r.y||0,s=e.getWidth(),u=e.getHeight(),c=n.width/n.height;if(null==s&&null!=u?s=u*c:null==u&&null!=s?u=s/c:null==s&&null==u&&(s=n.width,u=n.height),r.sWidth&&r.sHeight){var h=r.sx||0,l=r.sy||0;t.drawImage(n,h,l,r.sWidth,r.sHeight,i,a,s,u)}else if(r.sx&&r.sy){h=r.sx,l=r.sy;var f=s-h,d=u-l;t.drawImage(n,h,l,f,d,i,a,s,u)}else t.drawImage(n,i,a,s,u)}}function O(t,e,r){var n,i=r.text;if(null!=i&&(i+=\"\"),i){t.font=r.font||d[\"a\"],t.textAlign=r.textAlign,t.textBaseline=r.textBaseline;var o=void 0,a=void 0;t.setLineDash&&r.lineDash&&(n=Object(l[\"a\"])(e),o=n[0],a=n[1]),o&&(t.setLineDash(o),t.lineDashOffset=a),r.strokeFirst?(v(r)&&t.strokeText(i,r.x,r.y),g(r)&&t.fillText(i,r.x,r.y)):(g(r)&&t.fillText(i,r.x,r.y),v(r)&&t.strokeText(i,r.x,r.y)),o&&t.setLineDash([])}}var k=[\"shadowBlur\",\"shadowOffsetX\",\"shadowOffsetY\"],T=[[\"lineCap\",\"butt\"],[\"lineJoin\",\"miter\"],[\"miterLimit\",10]];function S(t,e,r,i,o){var a=!1;if(!i&&(r=r||{},e===r))return!1;if(i||e.opacity!==r.opacity){R(t,o),a=!0;var s=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(s)?n[\"b\"].opacity:s}(i||e.blend!==r.blend)&&(a||(R(t,o),a=!0),t.globalCompositeOperation=e.blend||n[\"b\"].blend);for(var u=0;u<k.length;u++){var c=k[u];(i||e[c]!==r[c])&&(a||(R(t,o),a=!0),t[c]=t.dpr*(e[c]||0))}return(i||e.shadowColor!==r.shadowColor)&&(a||(R(t,o),a=!0),t.shadowColor=e.shadowColor||n[\"b\"].shadowColor),a}function j(t,e,r,n,i){var o=B(e,i.inHover),a=n?null:r&&B(r,i.inHover)||{};if(o===a)return!1;var s=S(t,o,a,n,i);if((n||o.fill!==a.fill)&&(s||(R(t,i),s=!0),y(o.fill)&&(t.fillStyle=o.fill)),(n||o.stroke!==a.stroke)&&(s||(R(t,i),s=!0),y(o.stroke)&&(t.strokeStyle=o.stroke)),(n||o.opacity!==a.opacity)&&(s||(R(t,i),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var u=o.lineWidth,c=u/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==c&&(s||(R(t,i),s=!0),t.lineWidth=c)}for(var h=0;h<T.length;h++){var l=T[h],f=l[0];(n||o[f]!==a[f])&&(s||(R(t,i),s=!0),t[f]=o[f]||l[1])}return s}function C(t,e,r,n,i){return S(t,B(e,i.inHover),r&&B(r,i.inHover),n,i)}function A(t,e){var r=e.transform,n=t.dpr||1;r?t.setTransform(n*r[0],n*r[1],n*r[2],n*r[3],n*r[4],n*r[5]):t.setTransform(n,0,0,n,0,0)}function P(t,e,r){for(var n=!1,i=0;i<t.length;i++){var o=t[i];n=n||o.isZeroArea(),A(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=n}function M(t,e){return t&&e?t[0]!==e[0]||t[1]!==e[1]||t[2]!==e[2]||t[3]!==e[3]||t[4]!==e[4]||t[5]!==e[5]:!(!t&&!e)}var L=1,I=2,D=3,F=4;function z(t){var e=g(t),r=v(t);return!(t.lineDash||!(+e^+r)||e&&\"string\"!==typeof t.fill||r&&\"string\"!==typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}function R(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill=\"\",e.batchStroke=\"\"}function B(t,e){return e&&t.__hoverStyle||t.style}function E(t,e){N(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function N(t,e,r,n){var i=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~f[\"a\"],void(e.__isRendered=!1);var o=e.__clipPaths,h=r.prevElClipPaths,l=!1,d=!1;if(h&&!Object(a[\"c\"])(o,h)||(h&&h.length&&(R(t,r),t.restore(),d=l=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),o&&o.length&&(R(t,r),t.save(),P(o,t,r),l=!0),r.prevElClipPaths=o),r.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var p=r.prevEl;p||(d=l=!0);var v=e instanceof s[\"b\"]&&e.autoBatch&&z(e.style);l||M(i,p.transform)?(R(t,r),A(t,e)):v||R(t,r);var y=B(e,r.inHover);e instanceof s[\"b\"]?(r.lastDrawType!==L&&(d=!0,r.lastDrawType=L),j(t,e,p,d,r),v&&(r.batchFill||r.batchStroke)||t.beginPath(),x(t,e,y,v),v&&(r.batchFill=y.fill||\"\",r.batchStroke=y.stroke||\"\")):e instanceof c[\"a\"]?(r.lastDrawType!==D&&(d=!0,r.lastDrawType=D),j(t,e,p,d,r),O(t,e,y)):e instanceof u[\"a\"]?(r.lastDrawType!==I&&(d=!0,r.lastDrawType=I),C(t,e,p,d,r),w(t,e,y)):e.getTemporalDisplayables&&(r.lastDrawType!==F&&(d=!0,r.lastDrawType=F),H(t,e,r)),v&&n&&R(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),r.prevEl=e,e.__dirty=0,e.__isRendered=!0}}function H(t,e,r){var n=e.getDisplayables(),i=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=n.length;o<a;o++){var u=n[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),N(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var c=0,h=i.length;c<h;c++){u=i[c];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),N(t,u,s,c===h-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}},\"5cc6\":function(t,e,r){var n=r(\"74e8\");n(\"Uint8\",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},\"5e76\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return a})),r.d(e,\"a\",(function(){return s})),r.d(e,\"c\",(function(){return c}));var n=r(\"d51b\"),i=r(\"726e\"),o=new n[\"a\"](50);function a(t){if(\"string\"===typeof t){var e=o.get(t);return e&&e.image}return t}function s(t,e,r,n,a){if(t){if(\"string\"===typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var s=o.get(t),h={hostEl:r,cb:n,cbPayload:a};return s?(e=s.image,!c(e)&&s.pending.push(h)):(e=i[\"d\"].loadImage(t,u,u),e.__zrImageSrc=t,o.put(t,e.__cachedImgObj={image:e,pending:[h]})),e}return t}return e}function u(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],n=r.cb;n&&n(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function c(t){return t&&t.width&&t.height}},\"5f96\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=n.aTypedArray,o=n.exportTypedArrayMethod,a=[].join;o(\"join\",(function(t){return a.apply(i(this),arguments)}))},\"607d\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return u})),r.d(e,\"c\",(function(){return h})),r.d(e,\"e\",(function(){return l})),r.d(e,\"a\",(function(){return d})),r.d(e,\"f\",(function(){return p})),r.d(e,\"g\",(function(){return v})),r.d(e,\"d\",(function(){return y}));var n=r(\"22d1\"),i=r(\"65ed\"),o=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,a=[],s=n[\"a\"].browser.firefox&&+n[\"a\"].browser.version.split(\".\")[0]<39;function u(t,e,r,n){return r=r||{},n?c(t,e,r):s&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):c(t,e,r),r}function c(t,e,r){if(n[\"a\"].domSupported&&t.getBoundingClientRect){var o=e.clientX,s=e.clientY;if(Object(i[\"b\"])(t)){var u=t.getBoundingClientRect();return r.zrX=o-u.left,void(r.zrY=s-u.top)}if(Object(i[\"c\"])(a,t,o,s))return r.zrX=a[0],void(r.zrY=a[1])}r.zrX=r.zrY=0}function h(t){return t||window.event}function l(t,e,r){if(e=h(e),null!=e.zrX)return e;var n=e.type,i=n&&n.indexOf(\"touch\")>=0;if(i){var a=\"touchend\"!==n?e.targetTouches[0]:e.changedTouches[0];a&&u(t,a,e,r)}else{u(t,e,e,r);var s=f(e);e.zrDelta=s?s/120:-(e.detail||0)/3}var c=e.button;return null==e.which&&void 0!==c&&o.test(e.type)&&(e.which=1&c?1:2&c?3:4&c?2:0),e}function f(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,n=t.deltaY;if(null==r||null==n)return e;var i=0!==n?Math.abs(n):Math.abs(r),o=n>0?-1:n<0?1:r>0?-1:1;return 3*i*o}function d(t,e,r,n){t.addEventListener(e,r,n)}function p(t,e,r,n){t.removeEventListener(e,r,n)}var v=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function y(t){return 2===t.which||3===t.which}},\"60bd\":function(t,e,r){\"use strict\";var n=r(\"da84\"),i=r(\"ebb5\"),o=r(\"e260\"),a=r(\"b622\"),s=a(\"iterator\"),u=n.Uint8Array,c=o.values,h=o.keys,l=o.entries,f=i.aTypedArray,d=i.exportTypedArrayMethod,p=u&&u.prototype[s],v=!!p&&(\"values\"==p.name||void 0==p.name),y=function(){return c.call(f(this))};d(\"entries\",(function(){return l.call(f(this))})),d(\"keys\",(function(){return h.call(f(this))})),d(\"values\",y,!v),d(s,y,!v)},\"621a\":function(t,e,r){\"use strict\";var n=r(\"da84\"),i=r(\"83ab\"),o=r(\"a981\"),a=r(\"9112\"),s=r(\"e2cc\"),u=r(\"d039\"),c=r(\"19aa\"),h=r(\"a691\"),l=r(\"50c4\"),f=r(\"0b25\"),d=r(\"77a7\"),p=r(\"e163\"),v=r(\"d2bb\"),y=r(\"241c\").f,g=r(\"9bf2\").f,b=r(\"81d5\"),_=r(\"d44e\"),m=r(\"69f3\"),x=m.get,w=m.set,O=\"ArrayBuffer\",k=\"DataView\",T=\"prototype\",S=\"Wrong length\",j=\"Wrong index\",C=n[O],A=C,P=n[k],M=P&&P[T],L=Object.prototype,I=n.RangeError,D=d.pack,F=d.unpack,z=function(t){return[255&t]},R=function(t){return[255&t,t>>8&255]},B=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},E=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},N=function(t){return D(t,23,4)},H=function(t){return D(t,52,8)},q=function(t,e){g(t[T],e,{get:function(){return x(this)[e]}})},W=function(t,e,r,n){var i=f(r),o=x(t);if(i+e>o.byteLength)throw I(j);var a=x(o.buffer).bytes,s=i+o.byteOffset,u=a.slice(s,s+e);return n?u:u.reverse()},Y=function(t,e,r,n,i,o){var a=f(r),s=x(t);if(a+e>s.byteLength)throw I(j);for(var u=x(s.buffer).bytes,c=a+s.byteOffset,h=n(+i),l=0;l<e;l++)u[c+l]=h[o?l:e-l-1]};if(o){if(!u((function(){C(1)}))||!u((function(){new C(-1)}))||u((function(){return new C,new C(1.5),new C(NaN),C.name!=O}))){A=function(t){return c(this,A),new C(f(t))};for(var X,V=A[T]=C[T],U=y(C),$=0;U.length>$;)(X=U[$++])in A||a(A,X,C[X]);V.constructor=A}v&&p(M)!==L&&v(M,L);var Z=new P(new A(2)),G=M.setInt8;Z.setInt8(0,2147483648),Z.setInt8(1,2147483649),!Z.getInt8(0)&&Z.getInt8(1)||s(M,{setInt8:function(t,e){G.call(this,t,e<<24>>24)},setUint8:function(t,e){G.call(this,t,e<<24>>24)}},{unsafe:!0})}else A=function(t){c(this,A,O);var e=f(t);w(this,{bytes:b.call(new Array(e),0),byteLength:e}),i||(this.byteLength=e)},P=function(t,e,r){c(this,P,k),c(t,A,k);var n=x(t).byteLength,o=h(e);if(o<0||o>n)throw I(\"Wrong offset\");if(r=void 0===r?n-o:l(r),o+r>n)throw I(S);w(this,{buffer:t,byteLength:r,byteOffset:o}),i||(this.buffer=t,this.byteLength=r,this.byteOffset=o)},i&&(q(A,\"byteLength\"),q(P,\"buffer\"),q(P,\"byteLength\"),q(P,\"byteOffset\")),s(P[T],{getInt8:function(t){return W(this,1,t)[0]<<24>>24},getUint8:function(t){return W(this,1,t)[0]},getInt16:function(t){var e=W(this,2,t,arguments.length>1?arguments[1]:void 0);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=W(this,2,t,arguments.length>1?arguments[1]:void 0);return e[1]<<8|e[0]},getInt32:function(t){return E(W(this,4,t,arguments.length>1?arguments[1]:void 0))},getUint32:function(t){return E(W(this,4,t,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(t){return F(W(this,4,t,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(t){return F(W(this,8,t,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(t,e){Y(this,1,t,z,e)},setUint8:function(t,e){Y(this,1,t,z,e)},setInt16:function(t,e){Y(this,2,t,R,e,arguments.length>2?arguments[2]:void 0)},setUint16:function(t,e){Y(this,2,t,R,e,arguments.length>2?arguments[2]:void 0)},setInt32:function(t,e){Y(this,4,t,B,e,arguments.length>2?arguments[2]:void 0)},setUint32:function(t,e){Y(this,4,t,B,e,arguments.length>2?arguments[2]:void 0)},setFloat32:function(t,e){Y(this,4,t,N,e,arguments.length>2?arguments[2]:void 0)},setFloat64:function(t,e){Y(this,8,t,H,e,arguments.length>2?arguments[2]:void 0)}});_(A,O),_(P,k),t.exports={ArrayBuffer:A,DataView:P}},\"649e\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").some,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"some\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},\"65ed\":function(t,e,r){\"use strict\";r.d(e,\"d\",(function(){return c})),r.d(e,\"c\",(function(){return h})),r.d(e,\"b\",(function(){return d})),r.d(e,\"a\",(function(){return y}));var n=r(\"22d1\"),i=Math.log(2);function o(t,e,r,n,a,s){var u=n+\"-\"+a,c=t.length;if(s.hasOwnProperty(u))return s[u];if(1===e){var h=Math.round(Math.log((1<<c)-1&~a)/i);return t[r][h]}var l=n|1<<r,f=r+1;while(n&1<<f)f++;for(var d=0,p=0,v=0;p<c;p++){var y=1<<p;y&a||(d+=(v%2?-1:1)*t[r][p]*o(t,e-1,f,l,a|y,s),v++)}return s[u]=d,d}function a(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],n={},i=o(r,8,0,0,0,n);if(0!==i){for(var a=[],s=0;s<8;s++)for(var u=0;u<8;u++)null==a[u]&&(a[u]=0),a[u]+=((s+u)%2?-1:1)*o(r,7,0===s?1:0,1<<s,1<<u,n)/i*e[s];return function(t,e,r){var n=e*a[6]+r*a[7]+1;t[0]=(e*a[0]+r*a[1]+a[2])/n,t[1]=(e*a[3]+r*a[4]+a[5])/n}}}var s=\"___zrEVENTSAVED\",u=[];function c(t,e,r,n,i){return h(u,e,n,i,!0)&&h(t,r,u[0],u[1])}function h(t,e,r,i,o){if(e.getBoundingClientRect&&n[\"a\"].domSupported&&!d(e)){var a=e[s]||(e[s]={}),u=l(e,a),c=f(u,a,o);if(c)return c(t,r,i),!0}return!1}function l(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var n=[\"left\",\"right\"],i=[\"top\",\"bottom\"],o=0;o<4;o++){var a=document.createElement(\"div\"),s=a.style,u=o%2,c=(o>>1)%2;s.cssText=[\"position: absolute\",\"visibility: hidden\",\"padding: 0\",\"margin: 0\",\"border-width: 0\",\"user-select: none\",\"width:0\",\"height:0\",n[u]+\":0\",i[c]+\":0\",n[1-u]+\":auto\",i[1-c]+\":auto\",\"\"].join(\"!important;\"),t.appendChild(a),r.push(a)}return r}function f(t,e,r){for(var n=r?\"invTrans\":\"trans\",i=e[n],o=e.srcCoords,s=[],u=[],c=!0,h=0;h<4;h++){var l=t[h].getBoundingClientRect(),f=2*h,d=l.left,p=l.top;s.push(d,p),c=c&&o&&d===o[f]&&p===o[f+1],u.push(t[h].offsetLeft,t[h].offsetTop)}return c&&i?i:(e.srcCoords=s,e[n]=r?a(u,s):a(s,u))}function d(t){return\"CANVAS\"===t.nodeName.toUpperCase()}var p=/([&<>\"'])/g,v={\"&\":\"&amp;\",\"<\":\"&lt;\",\">\":\"&gt;\",'\"':\"&quot;\",\"'\":\"&#39;\"};function y(t){return null==t?\"\":(t+\"\").replace(p,(function(t,e){return v[e]}))}},\"68ab\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return i}));var n=r(\"4a3f\");function i(t,e,r,i,o,a,s,u,c){if(0===s)return!1;var h=s;if(c>e+h&&c>i+h&&c>a+h||c<e-h&&c<i-h&&c<a-h||u>t+h&&u>r+h&&u>o+h||u<t-h&&u<r-h&&u<o-h)return!1;var l=Object(n[\"l\"])(t,e,r,i,o,a,u,c,null);return l<=h/2}},\"697e\":function(t,e,r){\"use strict\";r.r(e),r.d(e,\"init\",(function(){return yt})),r.d(e,\"dispose\",(function(){return gt})),r.d(e,\"disposeAll\",(function(){return bt})),r.d(e,\"getInstance\",(function(){return _t})),r.d(e,\"registerPainter\",(function(){return mt})),r.d(e,\"getElementSSRData\",(function(){return xt})),r.d(e,\"registerSSRDataGetter\",(function(){return wt})),r.d(e,\"version\",(function(){return Ot}));var n=r(\"22d1\"),i=r(\"6d8b\"),o=r(\"21a1\"),a=r(\"401b\"),s=function(){function t(t,e){this.target=t,this.topTarget=e&&e.topTarget}return t}(),u=function(){function t(t){this.handler=t,t.on(\"mousedown\",this._dragStart,this),t.on(\"mousemove\",this._drag,this),t.on(\"mouseup\",this._dragEnd,this)}return t.prototype._dragStart=function(t){var e=t.target;while(e&&!e.draggable)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new s(e,t),\"dragstart\",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var r=t.offsetX,n=t.offsetY,i=r-this._x,o=n-this._y;this._x=r,this._y=n,e.drift(i,o,t),this.handler.dispatchToElement(new s(e,t),\"drag\",t.event);var a=this.handler.findHover(r,n,e).target,u=this._dropTarget;this._dropTarget=a,e!==a&&(u&&a!==u&&this.handler.dispatchToElement(new s(u,t),\"dragleave\",t.event),a&&a!==u&&this.handler.dispatchToElement(new s(a,t),\"dragenter\",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new s(e,t),\"dragend\",t.event),this._dropTarget&&this.handler.dispatchToElement(new s(this._dropTarget,t),\"drop\",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),c=u,h=r(\"6fd3\"),l=r(\"607d\"),f=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,r){var n=t.touches;if(n){for(var i={points:[],touches:[],target:e,event:t},o=0,a=n.length;o<a;o++){var s=n[o],u=l[\"b\"](r,s,{});i.points.push([u.zrX,u.zrY]),i.touches.push(s)}this._track.push(i)}},t.prototype._recognize=function(t){for(var e in v)if(v.hasOwnProperty(e)){var r=v[e](this._track,t);if(r)return r}},t}();function d(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}function p(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var v={pinch:function(t,e){var r=t.length;if(r){var n=(t[r-1]||{}).points,i=(t[r-2]||{}).points||n;if(i&&i.length>1&&n&&n.length>1){var o=d(n)/d(i);!isFinite(o)&&(o=1),e.pinchScale=o;var a=p(n);return e.pinchX=a[0],e.pinchY=a[1],{type:\"pinch\",target:t[0].target,event:e}}}}},y=r(\"9850\"),g=\"silent\";function b(t,e,r){return{type:t,event:r,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:_}}function _(){l[\"g\"](this.event)}var m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return Object(o[\"a\"])(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(h[\"a\"]),x=function(){function t(t,e){this.x=t,this.y=e}return t}(),w=[\"click\",\"dblclick\",\"mousewheel\",\"mouseout\",\"mouseup\",\"mousedown\",\"mousemove\",\"contextmenu\"],O=new y[\"a\"](0,0,0,0),k=function(t){function e(e,r,n,i,o){var a=t.call(this)||this;return a._hovered=new x(0,0),a.storage=e,a.painter=r,a.painterRoot=i,a._pointerSize=o,n=n||new m,a.proxy=null,a.setHandlerProxy(n),a._draggingMgr=new c(a),a}return Object(o[\"a\"])(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(i[\"k\"](w,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,n=j(this,e,r),i=this._hovered,o=i.target;o&&!o.__zr&&(i=this.findHover(i.x,i.y),o=i.target);var a=this._hovered=n?new x(e,r):this.findHover(e,r),s=a.target,u=this.proxy;u.setCursor&&u.setCursor(s?s.cursor:\"default\"),o&&s!==o&&this.dispatchToElement(i,\"mouseout\",t),this.dispatchToElement(a,\"mousemove\",t),s&&s!==o&&this.dispatchToElement(a,\"mouseover\",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;\"only_globalout\"!==e&&this.dispatchToElement(this._hovered,\"mouseout\",t),\"no_globalout\"!==e&&this.trigger(\"globalout\",{type:\"globalout\",event:t})},e.prototype.resize=function(){this._hovered=new x(0,0)},e.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,r){t=t||{};var n=t.target;if(!n||!n.silent){var i=\"on\"+e,o=b(e,t,r);while(n)if(n[i]&&(o.cancelBubble=!!n[i].call(n,o)),n.trigger(e,o),n=n.__hostTarget?n.__hostTarget:n.parent,o.cancelBubble)break;o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){\"function\"===typeof t[i]&&t[i].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,r){var n=this.storage.getDisplayList(),i=new x(t,e);if(S(n,i,t,e,r),this._pointerSize&&!i.target){for(var o=[],a=this._pointerSize,s=a/2,u=new y[\"a\"](t-s,e-s,a,a),c=n.length-1;c>=0;c--){var h=n[c];h===r||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(O.copy(h.getBoundingRect()),h.transform&&O.applyTransform(h.transform),O.intersect(u)&&o.push(h))}if(o.length)for(var l=4,f=Math.PI/12,d=2*Math.PI,p=0;p<s;p+=l)for(var v=0;v<d;v+=f){var g=t+p*Math.cos(v),b=e+p*Math.sin(v);if(S(o,i,g,b,r),i.target)return i}}return i},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new f);var r=this._gestureMgr;\"start\"===e&&r.clear();var n=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if(\"end\"===e&&r.clear(),n){var i=n.type;t.gestureEvent=i;var o=new x;o.target=n.target,this.dispatchToElement(o,i,n.event)}},e}(h[\"a\"]);function T(t,e,r){if(t[t.rectHover?\"rectContain\":\"contain\"](e,r)){var n=t,i=void 0,o=!1;while(n){if(n.ignoreClip&&(o=!0),!o){var a=n.getClipPath();if(a&&!a.contain(e,r))return!1}n.silent&&(i=!0);var s=n.__hostTarget;n=s||n.parent}return!i||g}return!1}function S(t,e,r,n,i){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==i&&!a.ignore&&(s=T(a,r,n))&&(!e.topTarget&&(e.topTarget=a),s!==g)){e.target=a;break}}}function j(t,e,r){var n=t.painter;return e<0||e>n.getWidth()||r<0||r>n.getHeight()}i[\"k\"]([\"click\",\"mousedown\",\"mouseup\",\"mousewheel\",\"dblclick\",\"contextmenu\"],(function(t){k.prototype[t]=function(e){var r,n,i=e.zrX,o=e.zrY,s=j(this,i,o);if(\"mouseup\"===t&&s||(r=this.findHover(i,o),n=r.target),\"mousedown\"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if(\"mouseup\"===t)this._upEl=n;else if(\"click\"===t){if(this._downEl!==this._upEl||!this._downPoint||a[\"f\"](this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(r,t,e)}}));var C=k,A=r(\"04f6\"),P=r(\"4bc4\"),M=!1;function L(){M||(M=!0,console.warn(\"z / z2 / zlevel of displayable is invalid, which may cause unexpected errors\"))}function I(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var D=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=I}return t.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,n=0,i=e.length;n<i;n++)this._updateAndAddDisplayable(e[n],null,t);r.length=this._displayListLen,Object(A[\"a\"])(r,I)},t.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e=e?e.slice():[];var i=n,o=t;while(i)i.parent=o,i.updateTransform(),e.push(i),o=i,i=i.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var u=a[s];t.__dirty&&(u.__dirty|=P[\"a\"]),this._updateAndAddDisplayable(u,e,r)}t.__dirty=0}else{var c=t;e&&e.length?c.__clipPaths=e:c.__clipPaths&&c.__clipPaths.length>0&&(c.__clipPaths=[]),isNaN(c.z)&&(L(),c.z=0),isNaN(c.z2)&&(L(),c.z2=0),isNaN(c.zlevel)&&(L(),c.zlevel=0),this._displayList[this._displayListLen++]=c}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,r);var l=t.getTextGuideLine();l&&this._updateAndAddDisplayable(l,e,r);var f=t.getTextContent();f&&this._updateAndAddDisplayable(f,e,r)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var n=i[\"r\"](this._roots,t);n>=0&&this._roots.splice(n,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),F=D,z=r(\"98b7\"),R=r(\"06ad\");function B(){return(new Date).getTime()}var E=function(t){function e(e){var r=t.call(this)||this;return r._running=!1,r._time=0,r._pausedTime=0,r._pauseStart=0,r._paused=!1,e=e||{},r.stage=e.stage||{},r}return Object(o[\"a\"])(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,r=t.next;e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){var e=B()-this._pausedTime,r=e-this._time,n=this._head;while(n){var i=n.next,o=n.step(e,r);o?(n.ondestroy(),this.removeClip(n),n=i):n=i}this._time=e,t||(this.trigger(\"frame\",r),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;function e(){t._running&&(Object(z[\"a\"])(e),!t._paused&&t.update())}this._running=!0,Object(z[\"a\"])(e)},e.prototype.start=function(){this._running||(this._time=B(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=B(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=B()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){var t=this._head;while(t){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var r=new R[\"b\"](t,e.loop);return this.addAnimator(r),r},e}(h[\"a\"]),N=E,H=300,q=n[\"a\"].domSupported,W=function(){var t=[\"click\",\"dblclick\",\"mousewheel\",\"wheel\",\"mouseout\",\"mouseup\",\"mousedown\",\"mousemove\",\"contextmenu\"],e=[\"touchstart\",\"touchend\",\"touchmove\"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=i[\"H\"](t,(function(t){var e=t.replace(\"mouse\",\"pointer\");return r.hasOwnProperty(e)?e:t}));return{mouse:t,touch:e,pointer:n}}(),Y={mouse:[\"mousemove\",\"mouseup\"],pointer:[\"pointermove\",\"pointerup\"]},X=!1;function V(t){var e=t.pointerType;return\"pen\"===e||\"touch\"===e}function U(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}function $(t){t&&(t.zrByTouch=!0)}function Z(t,e){return Object(l[\"e\"])(t.dom,new Q(t,e),!0)}function G(t,e){var r=e,n=!1;while(r&&9!==r.nodeType&&!(n=r.domBelongToZr||r!==e&&r===t.painterRoot))r=r.parentNode;return n}var Q=function(){function t(t,e){this.stopPropagation=i[\"L\"],this.stopImmediatePropagation=i[\"L\"],this.preventDefault=i[\"L\"],this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return t}(),K={mousedown:function(t){t=Object(l[\"e\"])(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger(\"mousedown\",t)},mousemove:function(t){t=Object(l[\"e\"])(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger(\"mousemove\",t)},mouseup:function(t){t=Object(l[\"e\"])(this.dom,t),this.__togglePointerCapture(!1),this.trigger(\"mouseup\",t)},mouseout:function(t){t=Object(l[\"e\"])(this.dom,t);var e=t.toElement||t.relatedTarget;G(this,e)||(this.__pointerCapturing&&(t.zrEventControl=\"no_globalout\"),this.trigger(\"mouseout\",t))},wheel:function(t){X=!0,t=Object(l[\"e\"])(this.dom,t),this.trigger(\"mousewheel\",t)},mousewheel:function(t){X||(t=Object(l[\"e\"])(this.dom,t),this.trigger(\"mousewheel\",t))},touchstart:function(t){t=Object(l[\"e\"])(this.dom,t),$(t),this.__lastTouchMoment=new Date,this.handler.processGesture(t,\"start\"),K.mousemove.call(this,t),K.mousedown.call(this,t)},touchmove:function(t){t=Object(l[\"e\"])(this.dom,t),$(t),this.handler.processGesture(t,\"change\"),K.mousemove.call(this,t)},touchend:function(t){t=Object(l[\"e\"])(this.dom,t),$(t),this.handler.processGesture(t,\"end\"),K.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<H&&K.click.call(this,t)},pointerdown:function(t){K.mousedown.call(this,t)},pointermove:function(t){V(t)||K.mousemove.call(this,t)},pointerup:function(t){K.mouseup.call(this,t)},pointerout:function(t){V(t)||K.mouseout.call(this,t)}};i[\"k\"]([\"click\",\"dblclick\",\"contextmenu\"],(function(t){K[t]=function(e){e=Object(l[\"e\"])(this.dom,e),this.trigger(t,e)}}));var J={pointermove:function(t){V(t)||J.mousemove.call(this,t)},pointerup:function(t){J.mouseup.call(this,t)},mousemove:function(t){this.trigger(\"mousemove\",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger(\"mouseup\",t),e&&(t.zrEventControl=\"only_globalout\",this.trigger(\"mouseout\",t))}};function tt(t,e){var r=e.domHandlers;n[\"a\"].pointerEventsSupported?i[\"k\"](W.pointer,(function(n){rt(e,n,(function(e){r[n].call(t,e)}))})):(n[\"a\"].touchEventsSupported&&i[\"k\"](W.touch,(function(n){rt(e,n,(function(i){r[n].call(t,i),U(e)}))})),i[\"k\"](W.mouse,(function(n){rt(e,n,(function(i){i=Object(l[\"c\"])(i),e.touching||r[n].call(t,i)}))})))}function et(t,e){function r(r){function n(n){n=Object(l[\"c\"])(n),G(t,n.target)||(n=Z(t,n),e.domHandlers[r].call(t,n))}rt(e,r,n,{capture:!0})}n[\"a\"].pointerEventsSupported?i[\"k\"](Y.pointer,r):n[\"a\"].touchEventsSupported||i[\"k\"](Y.mouse,r)}function rt(t,e,r,n){t.mounted[e]=r,t.listenerOpts[e]=n,Object(l[\"a\"])(t.domTarget,e,r,n)}function nt(t){var e=t.mounted;for(var r in e)e.hasOwnProperty(r)&&Object(l[\"f\"])(t.domTarget,r,e[r],t.listenerOpts[r]);t.mounted={}}var it=function(){function t(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return t}(),ot=function(t){function e(e,r){var n=t.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=r,n._localHandlerScope=new it(e,K),q&&(n._globalHandlerScope=new it(document,J)),tt(n,n._localHandlerScope),n}return Object(o[\"a\"])(e,t),e.prototype.dispose=function(){nt(this._localHandlerScope),q&&nt(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||\"default\")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,q&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?et(this,e):nt(e)}},e}(h[\"a\"]),at=ot,st=r(\"41ef\"),ut=r(\"2cf4c\"),ct=r(\"2dc5\"),ht={},lt={};function ft(t){delete lt[t]}function dt(t){if(!t)return!1;if(\"string\"===typeof t)return Object(st[\"lum\"])(t,1)<ut[\"b\"];if(t.colorStops){for(var e=t.colorStops,r=0,n=e.length,i=0;i<n;i++)r+=Object(st[\"lum\"])(e[i].color,1);return r/=n,r<ut[\"b\"]}return!1}var pt,vt=function(){function t(t,e,r){var o=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var a=new F,s=r.renderer||\"canvas\";ht[s]||(s=i[\"F\"](ht)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var u=new ht[s](e,a,r,t),c=r.ssr||u.ssrOnly;this.storage=a,this.painter=u;var h,l=n[\"a\"].node||n[\"a\"].worker||c?null:new at(u.getViewportRoot(),u.root),f=r.useCoarsePointer,d=null==f||\"auto\"===f?n[\"a\"].touchEventsSupported:!!f,p=44;d&&(h=i[\"P\"](r.pointerSize,p)),this.handler=new C(a,u,l,u.root,h),this.animation=new N({stage:{update:c?null:function(){return o._flush(!0)}}}),c||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=dt(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,r=B();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=B();e?(this._stillFrameAccum=0,this.trigger(\"rendered\",{elapsedTime:n-r})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&\"canvas\"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,r){return this._disposed||this.handler.on(t,e,r),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof ct[\"a\"]&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,ft(this.id))},t}();function yt(t,e){var r=new vt(i[\"p\"](),t,e);return lt[r.id]=r,r}function gt(t){t.dispose()}function bt(){for(var t in lt)lt.hasOwnProperty(t)&&lt[t].dispose();lt={}}function _t(t){return lt[t]}function mt(t,e){ht[t]=e}function xt(t){if(\"function\"===typeof pt)return pt(t)}function wt(t){pt=t}var Ot=\"5.6.1\"},\"6d8b\":function(t,e,r){\"use strict\";r.d(e,\"p\",(function(){return y})),r.d(e,\"G\",(function(){return g})),r.d(e,\"d\",(function(){return b})),r.d(e,\"I\",(function(){return _})),r.d(e,\"J\",(function(){return m})),r.d(e,\"m\",(function(){return x})),r.d(e,\"i\",(function(){return w})),r.d(e,\"r\",(function(){return O})),r.d(e,\"s\",(function(){return k})),r.d(e,\"K\",(function(){return T})),r.d(e,\"u\",(function(){return S})),r.d(e,\"k\",(function(){return j})),r.d(e,\"H\",(function(){return C})),r.d(e,\"N\",(function(){return A})),r.d(e,\"n\",(function(){return P})),r.d(e,\"o\",(function(){return M})),r.d(e,\"F\",(function(){return L})),r.d(e,\"c\",(function(){return D})),r.d(e,\"h\",(function(){return F})),r.d(e,\"t\",(function(){return z})),r.d(e,\"w\",(function(){return R})),r.d(e,\"C\",(function(){return B})),r.d(e,\"D\",(function(){return E})),r.d(e,\"z\",(function(){return N})),r.d(e,\"A\",(function(){return H})),r.d(e,\"E\",(function(){return W})),r.d(e,\"v\",(function(){return Y})),r.d(e,\"x\",(function(){return X})),r.d(e,\"y\",(function(){return V})),r.d(e,\"B\",(function(){return U})),r.d(e,\"l\",(function(){return $})),r.d(e,\"O\",(function(){return Z})),r.d(e,\"P\",(function(){return G})),r.d(e,\"Q\",(function(){return Q})),r.d(e,\"S\",(function(){return K})),r.d(e,\"M\",(function(){return J})),r.d(e,\"b\",(function(){return tt})),r.d(e,\"T\",(function(){return et})),r.d(e,\"R\",(function(){return nt})),r.d(e,\"f\",(function(){return ct})),r.d(e,\"e\",(function(){return ht})),r.d(e,\"g\",(function(){return lt})),r.d(e,\"j\",(function(){return ft})),r.d(e,\"q\",(function(){return dt})),r.d(e,\"L\",(function(){return pt})),r.d(e,\"a\",(function(){return vt}));var n=r(\"726e\"),i=A([\"Function\",\"RegExp\",\"Date\",\"Error\",\"CanvasGradient\",\"CanvasPattern\",\"Image\",\"Canvas\"],(function(t,e){return t[\"[object \"+e+\"]\"]=!0,t}),{}),o=A([\"Int8\",\"Uint8\",\"Uint8Clamped\",\"Int16\",\"Uint16\",\"Int32\",\"Uint32\",\"Float32\",\"Float64\"],(function(t,e){return t[\"[object \"+e+\"Array]\"]=!0,t}),{}),a=Object.prototype.toString,s=Array.prototype,u=s.forEach,c=s.filter,h=s.slice,l=s.map,f=function(){}.constructor,d=f?f.prototype:null,p=\"__proto__\",v=2311;function y(){return v++}function g(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];\"undefined\"!==typeof console&&console.error.apply(console,t)}function b(t){if(null==t||\"object\"!==typeof t)return t;var e=t,r=a.call(t);if(\"[object Array]\"===r){if(!it(t)){e=[];for(var n=0,s=t.length;n<s;n++)e[n]=b(t[n])}}else if(o[r]){if(!it(t)){var u=t.constructor;if(u.from)e=u.from(t);else{e=new u(t.length);for(n=0,s=t.length;n<s;n++)e[n]=t[n]}}}else if(!i[r]&&!it(t)&&!Y(t))for(var c in e={},t)t.hasOwnProperty(c)&&c!==p&&(e[c]=b(t[c]));return e}function _(t,e,r){if(!H(e)||!H(t))return r?b(e):t;for(var n in e)if(e.hasOwnProperty(n)&&n!==p){var i=t[n],o=e[n];!H(o)||!H(i)||z(o)||z(i)||Y(o)||Y(i)||q(o)||q(i)||it(o)||it(i)?!r&&n in t||(t[n]=b(e[n])):_(i,o,r)}return t}function m(t,e){for(var r=t[0],n=1,i=t.length;n<i;n++)r=_(r,t[n],e);return r}function x(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==p&&(t[r]=e[r]);return t}function w(t,e,r){for(var n=L(e),i=0,o=n.length;i<o;i++){var a=n[i];(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}n[\"d\"].createCanvas;function O(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r}return-1}function k(t,e){var r=t.prototype;function n(){}for(var i in n.prototype=e.prototype,t.prototype=new n,r)r.hasOwnProperty(i)&&(t.prototype[i]=r[i]);t.prototype.constructor=t,t.superClass=e}function T(t,e,r){if(t=\"prototype\"in t?t.prototype:t,e=\"prototype\"in e?e.prototype:e,Object.getOwnPropertyNames)for(var n=Object.getOwnPropertyNames(e),i=0;i<n.length;i++){var o=n[i];\"constructor\"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else w(t,e,r)}function S(t){return!!t&&(\"string\"!==typeof t&&\"number\"===typeof t.length)}function j(t,e,r){if(t&&e)if(t.forEach&&t.forEach===u)t.forEach(e,r);else if(t.length===+t.length)for(var n=0,i=t.length;n<i;n++)e.call(r,t[n],n,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function C(t,e,r){if(!t)return[];if(!e)return K(t);if(t.map&&t.map===l)return t.map(e,r);for(var n=[],i=0,o=t.length;i<o;i++)n.push(e.call(r,t[i],i,t));return n}function A(t,e,r,n){if(t&&e){for(var i=0,o=t.length;i<o;i++)r=e.call(n,r,t[i],i,t);return r}}function P(t,e,r){if(!t)return[];if(!e)return K(t);if(t.filter&&t.filter===c)return t.filter(e,r);for(var n=[],i=0,o=t.length;i<o;i++)e.call(r,t[i],i,t)&&n.push(t[i]);return n}function M(t,e,r){if(t&&e)for(var n=0,i=t.length;n<i;n++)if(e.call(r,t[n],n,t))return t[n]}function L(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}function I(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return function(){return t.apply(e,r.concat(h.call(arguments)))}}var D=d&&R(d.bind)?d.call.bind(d.bind):I;function F(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(h.call(arguments)))}}function z(t){return Array.isArray?Array.isArray(t):\"[object Array]\"===a.call(t)}function R(t){return\"function\"===typeof t}function B(t){return\"string\"===typeof t}function E(t){return\"[object String]\"===a.call(t)}function N(t){return\"number\"===typeof t}function H(t){var e=typeof t;return\"function\"===e||!!t&&\"object\"===e}function q(t){return!!i[a.call(t)]}function W(t){return!!o[a.call(t)]}function Y(t){return\"object\"===typeof t&&\"number\"===typeof t.nodeType&&\"object\"===typeof t.ownerDocument}function X(t){return null!=t.colorStops}function V(t){return null!=t.image}function U(t){return\"[object RegExp]\"===a.call(t)}function $(t){return t!==t}function Z(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,n=t.length;r<n;r++)if(null!=t[r])return t[r]}function G(t,e){return null!=t?t:e}function Q(t,e,r){return null!=t?t:null!=e?e:r}function K(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return h.apply(t,e)}function J(t){if(\"number\"===typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function tt(t,e){if(!t)throw new Error(e)}function et(t){return null==t?null:\"function\"===typeof t.trim?t.trim():t.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g,\"\")}var rt=\"__ec_primitive__\";function nt(t){t[rt]=!0}function it(t){return t[rt]}var ot=function(){function t(){this.data={}}return t.prototype[\"delete\"]=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return L(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},t}(),at=\"function\"===typeof Map;function st(){return at?new Map:new ot}var ut=function(){function t(e){var r=z(e);this.data=st();var n=this;function i(t,e){r?n.set(t,e):n.set(e,t)}e instanceof t?e.each(i):e&&j(e,i)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(r,n){t.call(e,r,n)}))},t.prototype.keys=function(){var t=this.data.keys();return at?Array.from(t):t},t.prototype.removeKey=function(t){this.data[\"delete\"](t)},t}();function ct(t){return new ut(t)}function ht(t,e){for(var r=new t.constructor(t.length+e.length),n=0;n<t.length;n++)r[n]=t[n];var i=t.length;for(n=0;n<e.length;n++)r[n+i]=e[n];return r}function lt(t,e){var r;if(Object.create)r=Object.create(t);else{var n=function(){};n.prototype=t,r=new n}return e&&x(r,e),r}function ft(t){var e=t.style;e.webkitUserSelect=\"none\",e.userSelect=\"none\",e.webkitTapHighlightColor=\"rgba(0,0,0,0)\",e[\"-webkit-touch-callout\"]=\"none\"}function dt(t,e){return t.hasOwnProperty(e)}function pt(){}var vt=180/Math.PI},\"6fd3\":function(t,e,r){\"use strict\";var n=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,r,n){this._$handlers||(this._$handlers={});var i=this._$handlers;if(\"function\"===typeof e&&(n=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),i[t]||(i[t]=[]);for(var a=0;a<i[t].length;a++)if(i[t][a].h===r)return this;var s={h:r,query:e,ctx:n||this,callAtLast:r.zrEventfulCallAtLast},u=i[t].length-1,c=i[t][u];return c&&c.callAtLast?i[t].splice(u,0,s):i[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var n=[],i=0,o=r[t].length;i<o;i++)r[t][i].h!==e&&n.push(r[t][i]);r[t]=n}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},t.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var n=this._$handlers[t],i=this._$eventProcessor;if(n)for(var o=e.length,a=n.length,s=0;s<a;s++){var u=n[s];if(!i||!i.filter||null==u.query||i.filter(t,u.query))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e);break}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var n=this._$handlers[t],i=this._$eventProcessor;if(n)for(var o=e.length,a=e[o-1],s=n.length,u=0;u<s;u++){var c=n[u];if(!i||!i.filter||null==c.query||i.filter(t,c.query))switch(o){case 0:c.h.call(a);break;case 1:c.h.call(a,e[0]);break;case 2:c.h.call(a,e[0],e[1]);break;default:c.h.apply(a,e.slice(1,o-1));break}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t}();e[\"a\"]=n},\"726e\":function(t,e,r){\"use strict\";r.d(e,\"c\",(function(){return n})),r.d(e,\"b\",(function(){return i})),r.d(e,\"a\",(function(){return o})),r.d(e,\"d\",(function(){return l})),r.d(e,\"e\",(function(){return f}));var n=12,i=\"sans-serif\",o=n+\"px \"+i,a=20,s=100,u=\"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";function c(t){var e={};if(\"undefined\"===typeof JSON)return e;for(var r=0;r<t.length;r++){var n=String.fromCharCode(r+32),i=(t.charCodeAt(r)-a)/s;e[n]=i}return e}var h=c(u),l={createCanvas:function(){return\"undefined\"!==typeof document&&document.createElement(\"canvas\")},measureText:function(){var t,e;return function(r,i){if(!t){var a=l.createCanvas();t=a&&a.getContext(\"2d\")}if(t)return e!==i&&(e=t.font=i||o),t.measureText(r);r=r||\"\",i=i||o;var s=/((?:\\d+)?\\.?\\d*)px/.exec(i),u=s&&+s[1]||n,c=0;if(i.indexOf(\"mono\")>=0)c=u*r.length;else for(var f=0;f<r.length;f++){var d=h[r[f]];c+=null==d?u:d*u}return{width:c}}}(),loadImage:function(t,e,r){var n=new Image;return n.onload=e,n.onerror=r,n.src=t,n}};function f(t){for(var e in l)t[e]&&(l[e]=t[e])}},\"72f7\":function(t,e,r){\"use strict\";var n=r(\"ebb5\").exportTypedArrayMethod,i=r(\"d039\"),o=r(\"da84\"),a=o.Uint8Array,s=a&&a.prototype||{},u=[].toString,c=[].join;i((function(){u.call({})}))&&(u=function(){return c.call(this)});var h=s.toString!=u;n(\"toString\",u,h)},\"735e\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"81d5\"),o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"fill\",(function(t){return i.apply(o(this),arguments)}))},\"74e8\":function(t,e,r){\"use strict\";var n=r(\"23e7\"),i=r(\"da84\"),o=r(\"83ab\"),a=r(\"8aa7\"),s=r(\"ebb5\"),u=r(\"621a\"),c=r(\"19aa\"),h=r(\"5c6c\"),l=r(\"9112\"),f=r(\"50c4\"),d=r(\"0b25\"),p=r(\"182d\"),v=r(\"c04e\"),y=r(\"5135\"),g=r(\"f5df\"),b=r(\"861d\"),_=r(\"7c73\"),m=r(\"d2bb\"),x=r(\"241c\").f,w=r(\"a078\"),O=r(\"b727\").forEach,k=r(\"2626\"),T=r(\"9bf2\"),S=r(\"06cf\"),j=r(\"69f3\"),C=r(\"7156\"),A=j.get,P=j.set,M=T.f,L=S.f,I=Math.round,D=i.RangeError,F=u.ArrayBuffer,z=u.DataView,R=s.NATIVE_ARRAY_BUFFER_VIEWS,B=s.TYPED_ARRAY_TAG,E=s.TypedArray,N=s.TypedArrayPrototype,H=s.aTypedArrayConstructor,q=s.isTypedArray,W=\"BYTES_PER_ELEMENT\",Y=\"Wrong length\",X=function(t,e){var r=0,n=e.length,i=new(H(t))(n);while(n>r)i[r]=e[r++];return i},V=function(t,e){M(t,e,{get:function(){return A(this)[e]}})},U=function(t){var e;return t instanceof F||\"ArrayBuffer\"==(e=g(t))||\"SharedArrayBuffer\"==e},$=function(t,e){return q(t)&&\"symbol\"!=typeof e&&e in t&&String(+e)==String(e)},Z=function(t,e){return $(t,e=v(e,!0))?h(2,t[e]):L(t,e)},G=function(t,e,r){return!($(t,e=v(e,!0))&&b(r)&&y(r,\"value\"))||y(r,\"get\")||y(r,\"set\")||r.configurable||y(r,\"writable\")&&!r.writable||y(r,\"enumerable\")&&!r.enumerable?M(t,e,r):(t[e]=r.value,t)};o?(R||(S.f=Z,T.f=G,V(N,\"buffer\"),V(N,\"byteOffset\"),V(N,\"byteLength\"),V(N,\"length\")),n({target:\"Object\",stat:!0,forced:!R},{getOwnPropertyDescriptor:Z,defineProperty:G}),t.exports=function(t,e,r){var o=t.match(/\\d+$/)[0]/8,s=t+(r?\"Clamped\":\"\")+\"Array\",u=\"get\"+t,h=\"set\"+t,v=i[s],y=v,g=y&&y.prototype,T={},S=function(t,e){var r=A(t);return r.view[u](e*o+r.byteOffset,!0)},j=function(t,e,n){var i=A(t);r&&(n=(n=I(n))<0?0:n>255?255:255&n),i.view[h](e*o+i.byteOffset,n,!0)},L=function(t,e){M(t,e,{get:function(){return S(this,e)},set:function(t){return j(this,e,t)},enumerable:!0})};R?a&&(y=e((function(t,e,r,n){return c(t,y,s),C(function(){return b(e)?U(e)?void 0!==n?new v(e,p(r,o),n):void 0!==r?new v(e,p(r,o)):new v(e):q(e)?X(y,e):w.call(y,e):new v(d(e))}(),t,y)})),m&&m(y,E),O(x(v),(function(t){t in y||l(y,t,v[t])})),y.prototype=g):(y=e((function(t,e,r,n){c(t,y,s);var i,a,u,h=0,l=0;if(b(e)){if(!U(e))return q(e)?X(y,e):w.call(y,e);i=e,l=p(r,o);var v=e.byteLength;if(void 0===n){if(v%o)throw D(Y);if(a=v-l,a<0)throw D(Y)}else if(a=f(n)*o,a+l>v)throw D(Y);u=a/o}else u=d(e),a=u*o,i=new F(a);P(t,{buffer:i,byteOffset:l,byteLength:a,length:u,view:new z(i)});while(h<u)L(t,h++)})),m&&m(y,E),g=y.prototype=_(N)),g.constructor!==y&&l(g,\"constructor\",y),B&&l(g,B,s),T[s]=y,n({global:!0,forced:y!=v,sham:!R},T),W in y||l(y,W,o),W in g||l(g,W,o),k(s)}):t.exports=function(){}},\"76a5\":function(t,e,r){\"use strict\";r.d(e,\"c\",(function(){return m})),r.d(e,\"b\",(function(){return w}));var n=r(\"21a1\"),i=r(\"d409\"),o=r(\"dd4f\"),a=r(\"6d8b\"),s=r(\"e86a\"),u=r(\"0da8\"),c=r(\"c7a2\"),h=r(\"9850\"),l=r(\"19eb\"),f=r(\"726e\"),d={fill:\"#000\"},p=2,v={style:Object(a[\"i\"])({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},l[\"a\"].style)},y=function(t){function e(e){var r=t.call(this)||this;return r.type=\"text\",r._children=[],r._defaultStyle=d,r.attr(e),r}return Object(n[\"a\"])(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var r=this._children[e];r.zlevel=this.zlevel,r.z=this.z,r.z2=this.z2,r.culling=this.culling,r.cursor=this.cursor,r.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var r=this.innerTransformable;return r?r.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){this._childCursor=0,O(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var r=0;r<this._children.length;r++)this._children[r].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var r=0;r<this._children.length;r++)this._children[r].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new h[\"a\"](0,0,0,0),e=this._children,r=[],n=null,i=0;i<e.length;i++){var o=e[i],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),n=n||t.clone(),n.union(t)):(n=n||a.clone(),n.union(a))}this._rect=n||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||d},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,n=t.rich||r&&{};return Object(a[\"m\"])(t,e),r&&n?(this._mergeRich(n,r),t.rich=n):n&&(t.rich=n),t},e.prototype._mergeRich=function(t,e){for(var r=Object(a[\"F\"])(e),n=0;n<r.length;n++){var i=r[n];t[i]=t[i]||{},Object(a[\"m\"])(t[i],e[i])}},e.prototype.getAnimationStyleProps=function(){return v},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||f[\"a\"],r=t.padding,n=C(t),a=Object(i[\"a\"])(n,t),u=A(t),c=!!t.backgroundColor,l=a.outerHeight,d=a.outerWidth,v=a.contentWidth,y=a.lines,g=a.lineHeight,b=this._defaultStyle;this.isTruncated=!!a.isTruncated;var _=t.x||0,m=t.y||0,w=t.align||b.align||\"left\",O=t.verticalAlign||b.verticalAlign||\"top\",k=_,P=Object(s[\"b\"])(m,a.contentHeight,O);if(u||r){var M=Object(s[\"a\"])(_,d,w),L=Object(s[\"b\"])(m,l,O);u&&this._renderBackground(t,t,M,L,d,l)}P+=g/2,r&&(k=j(_,w,r),\"top\"===O?P+=r[0]:\"bottom\"===O&&(P-=r[2]));for(var I=0,D=!1,F=(S(\"fill\"in t?t.fill:(D=!0,b.fill))),z=(T(\"stroke\"in t?t.stroke:c||b.autoStroke&&!D?null:(I=p,b.stroke))),R=t.textShadowBlur>0,B=null!=t.width&&(\"truncate\"===t.overflow||\"break\"===t.overflow||\"breakAll\"===t.overflow),E=a.calculatedLineHeight,N=0;N<y.length;N++){var H=this._getOrCreateChild(o[\"a\"]),q=H.createStyle();H.useStyle(q),q.text=y[N],q.x=k,q.y=P,w&&(q.textAlign=w),q.textBaseline=\"middle\",q.opacity=t.opacity,q.strokeFirst=!0,R&&(q.shadowBlur=t.textShadowBlur||0,q.shadowColor=t.textShadowColor||\"transparent\",q.shadowOffsetX=t.textShadowOffsetX||0,q.shadowOffsetY=t.textShadowOffsetY||0),q.stroke=z,q.fill=F,z&&(q.lineWidth=t.lineWidth||I,q.lineDash=t.lineDash,q.lineDashOffset=t.lineDashOffset||0),q.font=e,x(q,t),P+=g,B&&H.setBoundingRect(new h[\"a\"](Object(s[\"a\"])(q.x,v,q.textAlign),Object(s[\"b\"])(q.y,E,q.textBaseline),v,E))}},e.prototype._updateRichTexts=function(){var t=this.style,e=C(t),r=Object(i[\"b\"])(e,t),n=r.width,o=r.outerWidth,a=r.outerHeight,u=t.padding,c=t.x||0,h=t.y||0,l=this._defaultStyle,f=t.align||l.align,d=t.verticalAlign||l.verticalAlign;this.isTruncated=!!r.isTruncated;var p=Object(s[\"a\"])(c,o,f),v=Object(s[\"b\"])(h,a,d),y=p,g=v;u&&(y+=u[3],g+=u[0]);var b=y+n;A(t)&&this._renderBackground(t,t,p,v,o,a);for(var _=!!t.backgroundColor,m=0;m<r.lines.length;m++){var x=r.lines[m],w=x.tokens,O=w.length,k=x.lineHeight,T=x.width,S=0,j=y,P=b,M=O-1,L=void 0;while(S<O&&(L=w[S],!L.align||\"left\"===L.align))this._placeToken(L,t,k,g,j,\"left\",_),T-=L.width,j+=L.width,S++;while(M>=0&&(L=w[M],\"right\"===L.align))this._placeToken(L,t,k,g,P,\"right\",_),T-=L.width,P-=L.width,M--;j+=(n-(j-y)-(b-P)-T)/2;while(S<=M)L=w[S],this._placeToken(L,t,k,g,j+L.width/2,\"center\",_),j+=L.width,S++;g+=k}},e.prototype._placeToken=function(t,e,r,n,i,u,c){var l=e.rich[t.styleName]||{};l.text=t.text;var d=t.verticalAlign,v=n+r/2;\"top\"===d?v=n+t.height/2:\"bottom\"===d&&(v=n+r-t.height/2);var y=!t.isLineHolder&&A(l);y&&this._renderBackground(l,e,\"right\"===u?i-t.width:\"center\"===u?i-t.width/2:i,v-t.height/2,t.width,t.height);var g=!!l.backgroundColor,b=t.textPadding;b&&(i=j(i,u,b),v-=t.height/2-b[0]-t.innerHeight/2);var _=this._getOrCreateChild(o[\"a\"]),m=_.createStyle();_.useStyle(m);var w=this._defaultStyle,O=!1,k=0,C=S(\"fill\"in l?l.fill:\"fill\"in e?e.fill:(O=!0,w.fill)),P=T(\"stroke\"in l?l.stroke:\"stroke\"in e?e.stroke:g||c||w.autoStroke&&!O?null:(k=p,w.stroke)),M=l.textShadowBlur>0||e.textShadowBlur>0;m.text=t.text,m.x=i,m.y=v,M&&(m.shadowBlur=l.textShadowBlur||e.textShadowBlur||0,m.shadowColor=l.textShadowColor||e.textShadowColor||\"transparent\",m.shadowOffsetX=l.textShadowOffsetX||e.textShadowOffsetX||0,m.shadowOffsetY=l.textShadowOffsetY||e.textShadowOffsetY||0),m.textAlign=u,m.textBaseline=\"middle\",m.font=t.font||f[\"a\"],m.opacity=Object(a[\"Q\"])(l.opacity,e.opacity,1),x(m,l),P&&(m.lineWidth=Object(a[\"Q\"])(l.lineWidth,e.lineWidth,k),m.lineDash=Object(a[\"P\"])(l.lineDash,e.lineDash),m.lineDashOffset=e.lineDashOffset||0,m.stroke=P),C&&(m.fill=C);var L=t.contentWidth,I=t.contentHeight;_.setBoundingRect(new h[\"a\"](Object(s[\"a\"])(m.x,L,m.textAlign),Object(s[\"b\"])(m.y,I,m.textBaseline),L,I))},e.prototype._renderBackground=function(t,e,r,n,i,o){var s,h,l=t.backgroundColor,f=t.borderWidth,d=t.borderColor,p=l&&l.image,v=l&&!p,y=t.borderRadius,g=this;if(v||t.lineHeight||f&&d){s=this._getOrCreateChild(c[\"a\"]),s.useStyle(s.createStyle()),s.style.fill=null;var b=s.shape;b.x=r,b.y=n,b.width=i,b.height=o,b.r=y,s.dirtyShape()}if(v){var _=s.style;_.fill=l||null,_.fillOpacity=Object(a[\"P\"])(t.fillOpacity,1)}else if(p){h=this._getOrCreateChild(u[\"a\"]),h.onload=function(){g.dirtyStyle()};var m=h.style;m.image=l.image,m.x=r,m.y=n,m.width=i,m.height=o}if(f&&d){_=s.style;_.lineWidth=f,_.stroke=d,_.strokeOpacity=Object(a[\"P\"])(t.strokeOpacity,1),_.lineDash=t.borderDash,_.lineDashOffset=t.borderDashOffset||0,s.strokeContainThreshold=0,s.hasFill()&&s.hasStroke()&&(_.strokeFirst=!0,_.lineWidth*=2)}var x=(s||h).style;x.shadowBlur=t.shadowBlur||0,x.shadowColor=t.shadowColor||\"transparent\",x.shadowOffsetX=t.shadowOffsetX||0,x.shadowOffsetY=t.shadowOffsetY||0,x.opacity=Object(a[\"Q\"])(t.opacity,e.opacity,1)},e.makeFont=function(t){var e=\"\";return w(t)&&(e=[t.fontStyle,t.fontWeight,m(t.fontSize),t.fontFamily||\"sans-serif\"].join(\" \")),e&&Object(a[\"T\"])(e)||t.textFont||t.font},e}(l[\"c\"]),g={left:!0,right:1,center:1},b={top:1,bottom:1,middle:1},_=[\"fontStyle\",\"fontWeight\",\"fontSize\",\"fontFamily\"];function m(t){return\"string\"!==typeof t||-1===t.indexOf(\"px\")&&-1===t.indexOf(\"rem\")&&-1===t.indexOf(\"em\")?isNaN(+t)?f[\"c\"]+\"px\":t+\"px\":t}function x(t,e){for(var r=0;r<_.length;r++){var n=_[r],i=e[n];null!=i&&(t[n]=i)}}function w(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function O(t){return k(t),Object(a[\"k\"])(t.rich,k),t}function k(t){if(t){t.font=y.makeFont(t);var e=t.align;\"middle\"===e&&(e=\"center\"),t.align=null==e||g[e]?e:\"left\";var r=t.verticalAlign;\"center\"===r&&(r=\"middle\"),t.verticalAlign=null==r||b[r]?r:\"top\";var n=t.padding;n&&(t.padding=Object(a[\"M\"])(t.padding))}}function T(t,e){return null==t||e<=0||\"transparent\"===t||\"none\"===t?null:t.image||t.colorStops?\"#000\":t}function S(t){return null==t||\"none\"===t?null:t.image||t.colorStops?\"#000\":t}function j(t,e,r){return\"right\"===e?t-r[1]:\"center\"===e?t+r[3]/2-r[1]/2:t+r[3]}function C(t){var e=t.text;return null!=e&&(e+=\"\"),e}function A(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}e[\"a\"]=y},\"77a7\":function(t,e){var r=1/0,n=Math.abs,i=Math.pow,o=Math.floor,a=Math.log,s=Math.LN2,u=function(t,e,u){var c,h,l,f=new Array(u),d=8*u-e-1,p=(1<<d)-1,v=p>>1,y=23===e?i(2,-24)-i(2,-77):0,g=t<0||0===t&&1/t<0?1:0,b=0;for(t=n(t),t!=t||t===r?(h=t!=t?1:0,c=p):(c=o(a(t)/s),t*(l=i(2,-c))<1&&(c--,l*=2),t+=c+v>=1?y/l:y*i(2,1-v),t*l>=2&&(c++,l/=2),c+v>=p?(h=0,c=p):c+v>=1?(h=(t*l-1)*i(2,e),c+=v):(h=t*i(2,v-1)*i(2,e),c=0));e>=8;f[b++]=255&h,h/=256,e-=8);for(c=c<<e|h,d+=e;d>0;f[b++]=255&c,c/=256,d-=8);return f[--b]|=128*g,f},c=function(t,e){var n,o=t.length,a=8*o-e-1,s=(1<<a)-1,u=s>>1,c=a-7,h=o-1,l=t[h--],f=127&l;for(l>>=7;c>0;f=256*f+t[h],h--,c-=8);for(n=f&(1<<-c)-1,f>>=-c,c+=e;c>0;n=256*n+t[h],h--,c-=8);if(0===f)f=1-u;else{if(f===s)return n?NaN:l?-r:r;n+=i(2,e),f-=u}return(l?-1:1)*n*i(2,f-e)};t.exports={pack:u,unpack:c}},\"7a29\":function(t,e,r){\"use strict\";(function(t){r.d(e,\"p\",(function(){return s})),r.d(e,\"j\",(function(){return c})),r.d(e,\"q\",(function(){return l})),r.d(e,\"e\",(function(){return f})),r.d(e,\"a\",(function(){return d})),r.d(e,\"b\",(function(){return p})),r.d(e,\"i\",(function(){return v})),r.d(e,\"h\",(function(){return y})),r.d(e,\"l\",(function(){return g})),r.d(e,\"n\",(function(){return _})),r.d(e,\"m\",(function(){return m})),r.d(e,\"o\",(function(){return x})),r.d(e,\"k\",(function(){return w})),r.d(e,\"d\",(function(){return O})),r.d(e,\"f\",(function(){return k})),r.d(e,\"g\",(function(){return T})),r.d(e,\"c\",(function(){return S}));var n=r(\"6d8b\"),i=r(\"41ef\"),o=r(\"22d1\"),a=Math.round;function s(t){var e;if(t&&\"transparent\"!==t){if(\"string\"===typeof t&&t.indexOf(\"rgba\")>-1){var r=Object(i[\"parse\"])(t);r&&(t=\"rgb(\"+r[0]+\",\"+r[1]+\",\"+r[2]+\")\",e=r[3])}}else t=\"none\";return{color:t,opacity:null==e?1:e}}var u=1e-4;function c(t){return t<u&&t>-u}function h(t){return a(1e3*t)/1e3}function l(t){return a(1e4*t)/1e4}function f(t){return\"matrix(\"+h(t[0])+\",\"+h(t[1])+\",\"+h(t[2])+\",\"+h(t[3])+\",\"+l(t[4])+\",\"+l(t[5])+\")\"}var d={left:\"start\",right:\"end\",center:\"middle\",middle:\"middle\"};function p(t,e,r){return\"top\"===r?t+=e/2:\"bottom\"===r&&(t-=e/2),t}function v(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function y(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(\",\")}function g(t){return t&&!!t.image}function b(t){return t&&!!t.svgElement}function _(t){return g(t)||b(t)}function m(t){return\"linear\"===t.type}function x(t){return\"radial\"===t.type}function w(t){return t&&(\"linear\"===t.type||\"radial\"===t.type)}function O(t){return\"url(#\"+t+\")\"}function k(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function T(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*n[\"a\"],o=Object(n[\"P\"])(t.scaleX,1),s=Object(n[\"P\"])(t.scaleY,1),u=t.skewX||0,c=t.skewY||0,h=[];return(e||r)&&h.push(\"translate(\"+e+\"px,\"+r+\"px)\"),i&&h.push(\"rotate(\"+i+\")\"),1===o&&1===s||h.push(\"scale(\"+o+\",\"+s+\")\"),(u||c)&&h.push(\"skew(\"+a(u*n[\"a\"])+\"deg, \"+a(c*n[\"a\"])+\"deg)\"),h.join(\" \")}var S=function(){return o[\"a\"].hasGlobalWindow&&Object(n[\"w\"])(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:\"undefined\"!==typeof t?function(e){return t.from(e).toString(\"base64\")}:function(t){return null}}()}).call(this,r(\"1c35\").Buffer)},\"7efe\":function(t,e,r){\"use strict\";r.d(e,\"d\",(function(){return i})),r.d(e,\"b\",(function(){return o})),r.d(e,\"c\",(function(){return a})),r.d(e,\"a\",(function(){return s})),r.d(e,\"e\",(function(){return u})),r.d(e,\"f\",(function(){return c}));r(\"99af\"),r(\"a623\"),r(\"4de4\"),r(\"4160\"),r(\"c975\"),r(\"d81d\"),r(\"13d5\"),r(\"ace4\"),r(\"b6802\"),r(\"b64b\"),r(\"d3b7\"),r(\"ac1f\"),r(\"3ca3\"),r(\"466d\"),r(\"5319\"),r(\"1276\"),r(\"5cc6\"),r(\"9a8c\"),r(\"a975\"),r(\"735e\"),r(\"c1ac\"),r(\"d139\"),r(\"3a7b\"),r(\"d5d6\"),r(\"82f8\"),r(\"e91f\"),r(\"60bd\"),r(\"5f96\"),r(\"3280\"),r(\"3fcc\"),r(\"ca91\"),r(\"25a1\"),r(\"cd26\"),r(\"3c5d\"),r(\"2954\"),r(\"649e\"),r(\"219c\"),r(\"170b\"),r(\"b39a\"),r(\"72f7\"),r(\"159b\"),r(\"ddb0\"),r(\"2b3d\");var n=r(\"0122\");r(\"720d\"),r(\"4360\");function i(t,e){if(0===arguments.length)return null;var r,i=e||\"{y}-{m}-{d} {h}:{i}:{s}\";\"object\"===Object(n[\"a\"])(t)?r=t:(10===(\"\"+t).length&&(t=1e3*parseInt(t)),r=new Date(t));var o={y:r.getFullYear(),m:r.getMonth()+1,d:r.getDate(),h:r.getHours(),i:r.getMinutes(),s:r.getSeconds(),a:r.getDay()};return i.replace(/{(y|m|d|h|i|s|a)+}/g,(function(t,e){var r=o[e];return\"a\"===e?[\"日\",\"一\",\"二\",\"三\",\"四\",\"五\",\"六\"][r]:(t.length>0&&r<10&&(r=\"0\"+r),r||0)}))}function o(t){if(t||\"object\"===Object(n[\"a\"])(t)){var e=t.constructor===Array?[]:{};return Object.keys(t).forEach((function(r){e[r]=t[r]&&\"object\"===Object(n[\"a\"])(t[r])?o(t[r]):e[r]=t[r]})),e}console.error(\"argument type error\")}function a(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return r.reduce((function(t,e){return Object.keys(e).reduce((function(t,r){var n=e[r];return n.constructor===Object?t[r]=a(t[r]?t[r]:{},n):n.constructor===Array?t[r]=n.map((function(e,n){if(e.constructor===Object){var i=t[r]?t[r]:[];return a(i[n]?i[n]:{},e)}return e})):t[r]=n,t}),t)}),t)}function s(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"children\",n=[],i=[];return t.forEach((function(t){t[e]&&-1===n.indexOf(t[e])&&n.push(t[e])})),n.forEach((function(n){var o={};o[e]=n,o[r]=t.filter((function(t){return n===t[e]})),i.push(o)})),i}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,r=1024,n=[\"B\",\"KB\",\"MB\",\"GB\",\"TB\",\"PB\",\"EB\",\"ZB\",\"YB\"],i=Math.floor(Math.log(t)/Math.log(r));return i>=0?\"\".concat(parseFloat((t/Math.pow(r,i)).toFixed(e))).concat(n[i]):\"\".concat(parseFloat(t.toFixed(e))).concat(n[0])}function c(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,r=1e4,n=[\"\",\"万\",\"亿\",\"兆\",\"万兆\",\"亿兆\"],i=Math.floor(Math.log(t)/Math.log(r));return i>=0?\"\".concat(parseFloat((t/Math.pow(r,i)).toFixed(e))).concat(n[i]):\"\".concat(parseFloat(t.toFixed(e))).concat(n[0])}},\"81d5\":function(t,e,r){\"use strict\";var n=r(\"7b0b\"),i=r(\"23cb\"),o=r(\"50c4\");t.exports=function(t){var e=n(this),r=o(e.length),a=arguments.length,s=i(a>1?arguments[1]:void 0,r),u=a>2?arguments[2]:void 0,c=void 0===u?r:i(u,r);while(c>s)e[s++]=t;return e}},\"82f8\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"4d64\").includes,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"includes\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},\"857d\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return i}));var n=2*Math.PI;function i(t){return t%=n,t<0&&(t+=n),t}},8582:function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return d})),r.d(e,\"b\",(function(){return p}));var n=r(\"1687\"),i=r(\"401b\"),o=n[\"d\"],a=5e-5;function s(t){return t>a||t<-a}var u=[],c=[],h=n[\"c\"](),l=Math.abs,f=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return s(this.rotation)||s(this.x)||s(this.y)||s(this.scaleX-1)||s(this.scaleY-1)||s(this.skewX)||s(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||n[\"c\"](),e?this.getLocalTransform(r):o(r),t&&(e?n[\"f\"](r,t,r):n[\"b\"](r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(o(r),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(u);var r=u[0]<0?-1:1,i=u[1]<0?-1:1,o=((u[0]-r)*e+r)/u[0]||0,a=((u[1]-i)*e+i)/u[1]||0;t[0]*=o,t[1]*=o,t[2]*=a,t[3]*=a}this.invTransform=this.invTransform||n[\"c\"](),n[\"e\"](this.invTransform,t)},t.prototype.getComputedTransform=function(){var t=this,e=[];while(t)e.push(t),t=t.parent;while(t=e.pop())t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],r=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),i=Math.PI/2+n-Math.atan2(t[3],t[2]);r=Math.sqrt(r)*Math.cos(i),e=Math.sqrt(e),this.skewX=i,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=r,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||n[\"c\"](),n[\"f\"](c,t.invTransform,e),e=c);var r=this.originX,i=this.originY;(r||i)&&(h[4]=r,h[5]=i,n[\"f\"](c,e,h),c[4]-=r,c[5]-=i,e=c),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var r=[t,e],n=this.invTransform;return n&&i[\"b\"](r,r,n),r},t.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],n=this.transform;return n&&i[\"b\"](r,r,n),r},t.prototype.getLineScale=function(){var t=this.transform;return t&&l(t[0]-1)>1e-10&&l(t[3]-1)>1e-10?Math.sqrt(l(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){p(this,t)},t.getLocalTransform=function(t,e){e=e||[];var r=t.originX||0,i=t.originY||0,o=t.scaleX,a=t.scaleY,s=t.anchorX,u=t.anchorY,c=t.rotation||0,h=t.x,l=t.y,f=t.skewX?Math.tan(t.skewX):0,d=t.skewY?Math.tan(-t.skewY):0;if(r||i||s||u){var p=r+s,v=i+u;e[4]=-p*o-f*v*a,e[5]=-v*a-d*p*o}else e[4]=e[5]=0;return e[0]=o,e[3]=a,e[1]=d*o,e[2]=f*a,c&&n[\"g\"](e,e,c),e[4]+=r+h,e[5]+=i+l,e},t.initDefaultProps=function(){var e=t.prototype;e.scaleX=e.scaleY=e.globalScaleRatio=1,e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0}(),t}(),d=[\"x\",\"y\",\"originX\",\"originY\",\"anchorX\",\"anchorY\",\"rotation\",\"scaleX\",\"scaleY\",\"skewX\",\"skewY\"];function p(t,e){for(var r=0;r<d.length;r++){var n=d[r];t[n]=e[n]}}e[\"c\"]=f},8728:function(t,e,r){\"use strict\";function n(t,e,r,n,i,o){if(o>e&&o>n||o<e&&o<n)return 0;if(n===e)return 0;var a=(o-e)/(n-e),s=n<e?1:-1;1!==a&&0!==a||(s=n<e?.5:-.5);var u=a*(r-t)+t;return u===i?1/0:u>i?s:0}r.d(e,\"a\",(function(){return n}))},\"87b1\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"4fac\"),a=function(){function t(){this.points=null,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o[\"a\"](t,e,!0)},e}(i[\"b\"]);s.prototype.type=\"polygon\",e[\"a\"]=s},\"8a7b\":function(t,e,r){\"use strict\";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r(\"div\",{staticClass:\"router-wrap-table\"},[r(\"header\",{staticClass:\"table-header\"},[r(\"section\",{staticClass:\"table-header-extend\"},[r(\"el-row\",{attrs:{gutter:24}},[r(\"el-col\",{attrs:{span:10}},[r(\"range-picker\",{attrs:{type:\"ip\",\"start-placeholder\":t.$t(\"event.originalLes.placeholder.srcStartIP\"),\"end-placeholder\":t.$t(\"event.originalLes.placeholder.srcEndIP\")},on:{change:t.inputQuery},model:{value:t.query.form.model.srcIP,callback:function(e){t.$set(t.query.form.model,\"srcIP\",e)},expression:\"query.form.model.srcIP\"}})],1),r(\"el-col\",{attrs:{span:10}},[r(\"range-picker\",{attrs:{type:\"ip\",\"start-placeholder\":t.$t(\"event.originalLes.placeholder.dstStartIP\"),\"end-placeholder\":t.$t(\"event.originalLes.placeholder.dstEndIP\")},on:{change:t.inputQuery},model:{value:t.query.form.model.dstIP,callback:function(e){t.$set(t.query.form.model,\"dstIP\",e)},expression:\"query.form.model.dstIP\"}})],1)],1),r(\"el-row\",{attrs:{gutter:24}},[r(\"el-col\",{attrs:{span:10}},[r(\"el-date-picker\",{staticClass:\"search-input-datepicker\",attrs:{type:\"datetimerange\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",format:\"yyyy-MM-dd HH:mm:ss\",clearable:!1,\"popper-class\":\"no-clearable\",\"picker-options\":t.query.form.pickerOptions,\"range-separator\":t.$t(\"time.option.until\"),\"start-placeholder\":t.$t(\"time.option.startTime\"),\"end-placeholder\":t.$t(\"time.option.endTime\")},on:{change:t.inputQuery},model:{value:t.query.form.model.queryTime,callback:function(e){t.$set(t.query.form.model,\"queryTime\",e)},expression:\"query.form.model.queryTime\"}})],1),r(\"el-col\",{attrs:{span:5}},[r(\"el-select\",{attrs:{clearable:\"\",filterable:\"\",placeholder:t.$t(\"event.originalLes.logName\")},on:{change:t.inputQuery},model:{value:t.query.form.model.type,callback:function(e){t.$set(t.query.form.model,\"type\",e)},expression:\"query.form.model.type\"}},t._l(t.eventTypeOption,(function(t){return r(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),r(\"el-col\",{attrs:{span:4,align:\"right\",offset:5}},[r(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:t.inputQuery}},[t._v(\" \"+t._s(t.$t(\"button.query\"))+\" \")]),r(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:t.resetQuery}},[t._v(\" \"+t._s(t.$t(\"button.reset.default\"))+\" \")])],1)],1)],1)]),r(\"main\",{staticClass:\"table-body\"},[r(\"header\",{staticClass:\"table-body-header\"},[r(\"h2\",{staticClass:\"table-body-title\"},[t._v(\" \"+t._s(t.$t(\"event.originalLes.originalLes\"))+\" \")]),r(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"download\",expression:\"'download'\"}],attrs:{disabled:!t.option.categories||!(t.option.categories.toString().length>0)},on:{click:t.checkOut}},[t._v(\" \"+t._s(t.$t(\"button.export.default\"))+\" \")])],1),r(\"main\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.query.loading,expression:\"query.loading\"}],staticClass:\"table-body-main\"},[r(\"section\",{staticClass:\"main\"},[[r(\"graph-chart\",{attrs:{\"graph-data\":t.option,\"mouse-event\":\"\"}})]],2)])])])},i=[],o=(r(\"4de4\"),r(\"4160\"),r(\"a15b\"),r(\"d3b7\"),r(\"25f0\"),r(\"498a\"),r(\"159b\"),function(){var t=this,e=t.$createElement,r=t._self._c||e;return r(\"div\",{ref:\"lesMiserables\",class:t.className,style:{height:t.height,width:t.width},attrs:{id:t.id}})}),a=[],s=(r(\"99af\"),r(\"b64b\"),r(\"313e\")),u=r(\"b96e\"),c={mixins:[u[\"a\"]],props:{className:{type:String,default:\"chart-les\"},id:{type:String,default:\"\"},width:{type:String,default:\"100%\"},height:{type:String,default:\"100%\"},mouseEvent:{type:Boolean,default:!1},proto:{type:Boolean,default:!1},graphData:{type:Object,default:function(){return{title:{left:\"10px\",textStyle:{fontSize:12},text:\"\"},series:[{nodes:[],lines:[],categories:[]}]}}}},data:function(){return{chart:null}},watch:{graphData:{handler:function(t){this.configChart(t)},deep:!0}},mounted:function(){this.renderChart()},beforeDestroy:function(){this.disposeChart()},methods:{renderChart:function(){this.initChart(),this.configChart()},initChart:function(){arguments.length>0&&void 0!==arguments[0]||this.graphData;this.chart=s[\"b\"](this.$refs.lesMiserables,this.$store.getters.theme)},configChart:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.graphData;this.chart.showLoading(),t&&Object.keys(t).length>0&&t.nodes.length>0?this.drawChart(t):this.empty(),this.chart.hideLoading()},drawChart:function(t){if(this.proto)this.chart.setOption(t,!0);else{var e=this.chartOptionConfig(t);this.chart.setOption(e,!0)}},chartOptionConfig:function(t){var e={backgroundColor:\"transparent\",title:{left:\"10px\",textStyle:{fontSize:12},text:t.title?t.title:\"\"},tooltip:{show:!1},legend:{data:t.categories||[],type:\"scroll\",orient:\"vertical\",right:50,top:10,pageButtonItemGap:5},series:[{name:\"Les Miserables\",type:\"graph\",layout:\"circular\",circular:{rotateLabel:!1},edgeSymbol:[\"circle\",\"arrow\"],edgeSymbolSize:[4,10],edgeLabel:{fontSize:20},data:t.nodes||[],links:t.links||[],categories:t.categories||[],roam:!1,label:{position:\"right\",formatter:\"{b}\"},lineStyle:{color:\"source\",curveness:.3}}]};return this.mouseEvent&&this.chartHasMouseEvent(e),e},chartHasMouseEvent:function(t){var e=this;return t.tooltip=Object.assign(t.tooltip,{show:!0,trigger:\"item\",formatter:function(t){if(Object.keys(t.data).length>3){var r=t.data.value,n=t.data.num;return\"\".concat(e.$t(\"event.originalLes.srcNum\"),\"：\").concat(r)+\"</br>\"+\"\".concat(e.$t(\"event.originalLes.dstNum\"),\"：\").concat(n)}}}),t.series[0]=Object.assign(t.series[0],{roam:!0,circular:{rotateLabel:!0}}),t},disposeChart:function(){this.chart&&(this.chart.dispose(),this.chart=null)}}},h=c,l=r(\"2877\"),f=Object(l[\"a\"])(h,o,a,!1,null,null,null),d=f.exports,p=r(\"2ecb\"),v=r(\"7efe\"),y=r(\"13c3\"),g=r(\"21f4\"),b=r(\"4020\");function _(t){return Object(b[\"a\"])({url:\"/event/original/associated/visualizes\",method:\"get\",params:t||{}})}function m(){return Object(b[\"a\"])({url:\"/event/original/associated/combo/event-types\",method:\"get\"})}var x={name:\"EventOriginalLes\",components:{GraphChart:d,RangePicker:p[\"a\"]},data:function(){return{pageData:\"\",htmlTitle:this.$t(\"event.originalLes.originalLes\"),option:{categories:[],links:[],nodes:[]},eventTypeOption:[],query:{loading:!1,form:{model:{type:\"\",srcIP:[\"\",\"\"],dstIP:[\"\",\"\"],queryTime:\"\"},pickerOptions:{shortcuts:[{text:this.$t(\"time.option.week\"),onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit(\"pick\",[r,e])}},{text:this.$t(\"time.option.month\"),onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit(\"pick\",[r,e])}},{text:this.$t(\"time.option.quarter\"),onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-7776e6),t.$emit(\"pick\",[r,e])}}]}}},queryDebounce:null}},computed:{defaultTimeRange:function(){var t=new Date,e=[Object(v[\"d\"])(t.getTime(),\"{y}-{m}-{d} 00:00:00\"),Object(v[\"d\"])(t.getTime(),\"{y}-{m}-{d} 23:59:59\")],r=e[0],n=e[1];return[r,n]}},mounted:function(){this.query.form.model.queryTime=this.defaultTimeRange,this.initData(),this.initOption(),this.initDebounce(),this.inputQuery()},methods:{initDebounce:function(){var t=this;this.queryDebounce=Object(y[\"a\"])((function(){var e={type:t.query.form.model.type,srcIP:t.ipRange(t.query.form.model.srcIP),dstIP:t.ipRange(t.query.form.model.dstIP),queryTime:t.query.form.model.queryTime.toString()};t.getLesData(e)}),500)},initData:function(){this.option={categories:[],links:[],nodes:[]}},ipRange:function(t){var e=\"\";return t=t.filter((function(t){if(!Object(g[\"b\"])(t))return t.trim()})),t.length>0&&(e=t.join(\"-\")),e},initOption:function(){var t=this;m().then((function(e){t.eventTypeOption=e}))},getLesData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{queryTime:this.query.form.model.queryTime.toString()};this.query.loading=!0,_(e).then((function(e){e&&(e.nodes&&e.nodes.forEach((function(t){t.label={normal:{show:!0}}})),t.option=e,t.query.loading=!1)}))},checkOut:function(){var t=document.getElementsByTagName(\"canvas\"),e=t[0].toDataURL(\"image/png\",1),r=document.createElement(\"a\");r.href=e,r.download=this.$t(\"event.originalLes.originalLes\"),r.click()},inputQuery:function(){this.queryDebounce()},clearQuery:function(){this.query.form.model={type:\"\",srcIP:[\"\",\"\"],dstIP:[\"\",\"\"],queryTime:\"\"}},resetQuery:function(){this.clearQuery(),this.initData(),this.inputQuery()}}},w=x,O=(r(\"2308\"),Object(l[\"a\"])(w,n,i,!1,null,\"b32195da\",null));e[\"default\"]=O.exports},\"8aa7\":function(t,e,r){var n=r(\"da84\"),i=r(\"d039\"),o=r(\"1c7e\"),a=r(\"ebb5\").NATIVE_ARRAY_BUFFER_VIEWS,s=n.ArrayBuffer,u=n.Int8Array;t.exports=!a||!i((function(){u(1)}))||!i((function(){new u(-1)}))||!o((function(t){new u,new u(null),new u(1.5),new u(t)}),!0)||i((function(){return 1!==new u(new s(2),1,void 0).length}))},\"8d1d\":function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return o}));var n=r(\"6d8b\");function i(t,e){return t&&\"solid\"!==t&&e>0?\"dashed\"===t?[4*e,2*e]:\"dotted\"===t?[e]:Object(n[\"z\"])(t)?[t]:Object(n[\"t\"])(t)?t:null:null}function o(t){var e=t.style,r=e.lineDash&&e.lineWidth>0&&i(e.lineDash,e.lineWidth),o=e.lineDashOffset;if(r){var a=e.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(r=Object(n[\"H\"])(r,(function(t){return t/a})),o/=a)}return[r,o]}},\"8d32\":function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultStyle=function(){return{stroke:\"#000\",fill:null}},e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var r=e.cx,n=e.cy,i=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,u=Math.cos(o),c=Math.sin(o);t.moveTo(u*i+r,c*i+n),t.arc(r,n,i,o,a,!s)},e}(i[\"b\"]);a.prototype.type=\"arc\",e[\"a\"]=a},9129:function(t,e,r){var n=r(\"23e7\");n({target:\"Number\",stat:!0},{isNaN:function(t){return t!=t}})},9680:function(t,e,r){\"use strict\";function n(t,e,r,n,i,o,a){if(0===i)return!1;var s=i,u=0,c=t;if(a>e+s&&a>n+s||a<e-s&&a<n-s||o>t+s&&o>r+s||o<t-s&&o<r-s)return!1;if(t===r)return Math.abs(o-t)<=s/2;u=(e-n)/(t-r),c=(t*n-r*e)/(t-r);var h=u*o-a+c,l=h*h/(u*u+1);return l<=s/2*s/2}r.d(e,\"a\",(function(){return n}))},9850:function(t,e,r){\"use strict\";var n=r(\"1687\"),i=r(\"dce8\"),o=Math.min,a=Math.max,s=new i[\"a\"],u=new i[\"a\"],c=new i[\"a\"],h=new i[\"a\"],l=new i[\"a\"],f=new i[\"a\"],d=function(){function t(t,e,r,n){r<0&&(t+=r,r=-r),n<0&&(e+=n,n=-n),this.x=t,this.y=e,this.width=r,this.height=n}return t.prototype.union=function(t){var e=o(t.x,this.x),r=o(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=a(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=a(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,r=t.width/e.width,i=t.height/e.height,o=n[\"c\"]();return n[\"i\"](o,o,[-e.x,-e.y]),n[\"h\"](o,o,[r,i]),n[\"i\"](o,o,[t.x,t.y]),o},t.prototype.intersect=function(e,r){if(!e)return!1;e instanceof t||(e=t.create(e));var n=this,o=n.x,a=n.x+n.width,s=n.y,u=n.y+n.height,c=e.x,h=e.x+e.width,d=e.y,p=e.y+e.height,v=!(a<c||h<o||u<d||p<s);if(r){var y=1/0,g=0,b=Math.abs(a-c),_=Math.abs(h-o),m=Math.abs(u-d),x=Math.abs(p-s),w=Math.min(b,_),O=Math.min(m,x);a<c||h<o?w>g&&(g=w,b<_?i[\"a\"].set(f,-b,0):i[\"a\"].set(f,_,0)):w<y&&(y=w,b<_?i[\"a\"].set(l,b,0):i[\"a\"].set(l,-_,0)),u<d||p<s?O>g&&(g=O,m<x?i[\"a\"].set(f,0,-m):i[\"a\"].set(f,0,x)):w<y&&(y=w,m<x?i[\"a\"].set(l,0,m):i[\"a\"].set(l,0,-x))}return r&&i[\"a\"].copy(r,v?l:f),v},t.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,r,n){if(n){if(n[1]<1e-5&&n[1]>-1e-5&&n[2]<1e-5&&n[2]>-1e-5){var i=n[0],l=n[3],f=n[4],d=n[5];return e.x=r.x*i+f,e.y=r.y*l+d,e.width=r.width*i,e.height=r.height*l,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}s.x=c.x=r.x,s.y=h.y=r.y,u.x=h.x=r.x+r.width,u.y=c.y=r.y+r.height,s.transform(n),h.transform(n),u.transform(n),c.transform(n),e.x=o(s.x,u.x,c.x,h.x),e.y=o(s.y,u.y,c.y,h.y);var p=a(s.x,u.x,c.x,h.x),v=a(s.y,u.y,c.y,h.y);e.width=p-e.x,e.height=v-e.y}else e!==r&&t.copy(e,r)},t}();e[\"a\"]=d},\"98b7\":function(t,e,r){\"use strict\";var n,i=r(\"22d1\");n=i[\"a\"].hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},e[\"a\"]=n},\"9a8c\":function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"145e\"),o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"copyWithin\",(function(t,e){return i.call(o(this),t,e,arguments.length>2?arguments[2]:void 0)}))},\"9cf9\":function(t,e,r){\"use strict\";r.d(e,\"b\",(function(){return i})),r.d(e,\"c\",(function(){return o})),r.d(e,\"a\",(function(){return a}));var n=Math.round;function i(t,e,r){if(e){var i=e.x1,o=e.x2,s=e.y1,u=e.y2;t.x1=i,t.x2=o,t.y1=s,t.y2=u;var c=r&&r.lineWidth;return c?(n(2*i)===n(2*o)&&(t.x1=t.x2=a(i,c,!0)),n(2*s)===n(2*u)&&(t.y1=t.y2=a(s,c,!0)),t):t}}function o(t,e,r){if(e){var n=e.x,i=e.y,o=e.width,s=e.height;t.x=n,t.y=i,t.width=o,t.height=s;var u=r&&r.lineWidth;return u?(t.x=a(n,u,!0),t.y=a(i,u,!0),t.width=Math.max(a(n+o,u,!1)-t.x,0===o?0:1),t.height=Math.max(a(i+s,u,!1)-t.y,0===s?0:1),t):t}}function a(t,e,r){if(!e)return t;var i=n(2*t);return(i+n(e))%2===0?i/2:(i+(r?1:-1))/2}},a078:function(t,e,r){var n=r(\"7b0b\"),i=r(\"50c4\"),o=r(\"35a1\"),a=r(\"e95a\"),s=r(\"0366\"),u=r(\"ebb5\").aTypedArrayConstructor;t.exports=function(t){var e,r,c,h,l,f,d=n(t),p=arguments.length,v=p>1?arguments[1]:void 0,y=void 0!==v,g=o(d);if(void 0!=g&&!a(g)){l=g.call(d),f=l.next,d=[];while(!(h=f.call(l)).done)d.push(h.value)}for(y&&p>2&&(v=s(v,arguments[2],2)),r=i(d.length),c=new(u(this))(r),e=0;r>e;e++)c[e]=y?v(d[e],e):d[e];return c}},a623:function(t,e,r){\"use strict\";var n=r(\"23e7\"),i=r(\"b727\").every,o=r(\"a640\"),a=r(\"ae40\"),s=o(\"every\"),u=a(\"every\");n({target:\"Array\",proto:!0,forced:!s||!u},{every:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},a975:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").every,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"every\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},a981:function(t,e){t.exports=\"undefined\"!==typeof ArrayBuffer&&\"undefined\"!==typeof DataView},ac0f:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"401b\"),a=r(\"4a3f\"),s=[],u=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return t}();function c(t,e,r){var n=t.cpx2,i=t.cpy2;return null!=n||null!=i?[(r?a[\"b\"]:a[\"a\"])(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?a[\"b\"]:a[\"a\"])(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?a[\"i\"]:a[\"h\"])(t.x1,t.cpx1,t.x2,e),(r?a[\"i\"]:a[\"h\"])(t.y1,t.cpy1,t.y2,e)]}var h=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultStyle=function(){return{stroke:\"#000\",fill:null}},e.prototype.getDefaultShape=function(){return new u},e.prototype.buildPath=function(t,e){var r=e.x1,n=e.y1,i=e.x2,o=e.y2,u=e.cpx1,c=e.cpy1,h=e.cpx2,l=e.cpy2,f=e.percent;0!==f&&(t.moveTo(r,n),null==h||null==l?(f<1&&(Object(a[\"n\"])(r,u,i,f,s),u=s[1],i=s[2],Object(a[\"n\"])(n,c,o,f,s),c=s[1],o=s[2]),t.quadraticCurveTo(u,c,i,o)):(f<1&&(Object(a[\"g\"])(r,u,h,i,f,s),u=s[1],h=s[2],i=s[3],Object(a[\"g\"])(n,c,l,o,f,s),c=s[1],l=s[2],o=s[3]),t.bezierCurveTo(u,c,h,l,i,o)))},e.prototype.pointAt=function(t){return c(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=c(this.shape,t,!0);return o[\"m\"](e,e)},e}(i[\"b\"]);h.prototype.type=\"bezier-curve\",e[\"a\"]=h},ace4:function(t,e,r){\"use strict\";var n=r(\"23e7\"),i=r(\"d039\"),o=r(\"621a\"),a=r(\"825a\"),s=r(\"23cb\"),u=r(\"50c4\"),c=r(\"4840\"),h=o.ArrayBuffer,l=o.DataView,f=h.prototype.slice,d=i((function(){return!new h(2).slice(1,void 0).byteLength}));n({target:\"ArrayBuffer\",proto:!0,unsafe:!0,forced:d},{slice:function(t,e){if(void 0!==f&&void 0===e)return f.call(a(this),t);var r=a(this).byteLength,n=s(t,r),i=s(void 0===e?r:e,r),o=new(c(this,h))(u(i-n)),d=new l(this),p=new l(o),v=0;while(n<i)p.setUint8(v++,d.getUint8(n++));return o}})},ae69:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=function(){function t(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var r=.5522848,n=e.cx,i=e.cy,o=e.rx,a=e.ry,s=o*r,u=a*r;t.moveTo(n-o,i),t.bezierCurveTo(n-o,i-u,n-s,i-a,n,i-a),t.bezierCurveTo(n+s,i-a,n+o,i-u,n+o,i),t.bezierCurveTo(n+o,i+u,n+s,i+a,n,i+a),t.bezierCurveTo(n-s,i+a,n-o,i+u,n-o,i),t.closePath()},e}(i[\"b\"]);a.prototype.type=\"ellipse\",e[\"a\"]=a},b362:function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return a}));var n=r(\"4a3f\"),i=r(\"6d8b\"),o=/cubic-bezier\\(([0-9,\\.e ]+)\\)/;function a(t){var e=t&&o.exec(t);if(e){var r=e[1].split(\",\"),a=+Object(i[\"T\"])(r[0]),s=+Object(i[\"T\"])(r[1]),u=+Object(i[\"T\"])(r[2]),c=+Object(i[\"T\"])(r[3]);if(isNaN(a+s+u+c))return;var h=[];return function(t){return t<=0?0:t>=1?1:Object(n[\"f\"])(0,a,u,1,t,h)&&Object(n[\"a\"])(0,s,c,1,h[0])}}}},b39a:function(t,e,r){\"use strict\";var n=r(\"da84\"),i=r(\"ebb5\"),o=r(\"d039\"),a=n.Int8Array,s=i.aTypedArray,u=i.exportTypedArrayMethod,c=[].toLocaleString,h=[].slice,l=!!a&&o((function(){c.call(new a(1))})),f=o((function(){return[1,2].toLocaleString()!=new a([1,2]).toLocaleString()}))||!o((function(){a.prototype.toLocaleString.call([1,2])}));u(\"toLocaleString\",(function(){return c.apply(l?h.call(s(this)):s(this),arguments)}),f)},c1ac:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").filter,o=r(\"4840\"),a=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod;u(\"filter\",(function(t){var e=i(a(this),t,arguments.length>1?arguments[1]:void 0),r=o(this,this.constructor),n=0,u=e.length,c=new(s(r))(u);while(u>n)c[n]=e[n++];return c}))},c54a:function(t,e,r){\"use strict\";r.d(e,\"l\",(function(){return n})),r.d(e,\"m\",(function(){return i})),r.d(e,\"b\",(function(){return o})),r.d(e,\"c\",(function(){return a})),r.d(e,\"a\",(function(){return s})),r.d(e,\"j\",(function(){return u})),r.d(e,\"q\",(function(){return c})),r.d(e,\"d\",(function(){return h})),r.d(e,\"f\",(function(){return l})),r.d(e,\"g\",(function(){return f})),r.d(e,\"e\",(function(){return d})),r.d(e,\"n\",(function(){return p})),r.d(e,\"k\",(function(){return v})),r.d(e,\"p\",(function(){return y})),r.d(e,\"h\",(function(){return g})),r.d(e,\"i\",(function(){return b})),r.d(e,\"o\",(function(){return _}));r(\"ac1f\"),r(\"466d\"),r(\"1276\");function n(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=\"\";switch(e){case 0:r=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break;case 1:r=/^(?![_.\\-])(?!.*?[_.\\-]$)[a-zA-Z0-9_.\\-\\u4e00-\\u9fa5]+$/;break;case 2:r=/^(?![_./\\-])(?!.*?[_./\\-]$)[a-zA-Z0-9_./\\-\\u4e00-\\u9fa5]+$/;break;case 3:r=/^(?![_./\\-\\s])(?!.*?[_./\\-\\s]$)[a-zA-Z0-9_./\\-\\s\\u4e00-\\u9fa5]+$/;break;default:r=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break}return r.test(t)}function i(t){var e=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\\[\\].<>/?\\-%]).{0,}$/;return e.test(t)}function o(t){var e=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return e.test(t)}function a(t){var e=/^([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+\\.[a-zA-Z]{2,3}$/;return e.test(t)}function s(t){var e=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return e.test(t)}function u(t){for(var e=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,r=t.split(\",\"),n=0;n<r.length;n++)if(!e.test(r[n]))return!1;return!0}function c(t){var e=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return e.test(t)}function h(t){var e=/^(\\d{2,5}-)?\\d{6,9}(-\\d{2,4})?$/;return e.test(t)}function l(t){var e=/^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;return e.test(t)}function f(t){var e=/:/.test(t)&&t.match(/:/g).length<8&&/::/.test(t)?1===t.match(/::/g).length&&/^::$|^(::)?([\\da-f]{1,4}(:|::))*[\\da-f]{1,4}(:|::)?$/i.test(t):/^([\\da-f]{1,4}:){7}[\\da-f]{1,4}$/i.test(t);return e}function d(t){return l(t)||f(t)}function p(t){var e=/^([0-9]|[1-9][0-9]{0,4})$/;return e.test(t)}function v(t){for(var e=/^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(\\:([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,r=t.split(\",\"),n=0;n<r.length;n++)if(!e.test(r[n]))return!1;return!0}function y(t){var e=/^[^ ]+$/;return e.test(t)}function g(t){var e=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\\.[A-Fa-f0-9]{4}){2}$/;return e.test(t)}function b(t){var e=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return e.test(t)}function _(t){var e=/[^\\u4E00-\\u9FA5]/;return e.test(t)}},c7a2:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\");function o(t,e){var r,n,i,o,a,s=e.x,u=e.y,c=e.width,h=e.height,l=e.r;c<0&&(s+=c,c=-c),h<0&&(u+=h,h=-h),\"number\"===typeof l?r=n=i=o=l:l instanceof Array?1===l.length?r=n=i=o=l[0]:2===l.length?(r=i=l[0],n=o=l[1]):3===l.length?(r=l[0],n=o=l[1],i=l[2]):(r=l[0],n=l[1],i=l[2],o=l[3]):r=n=i=o=0,r+n>c&&(a=r+n,r*=c/a,n*=c/a),i+o>c&&(a=i+o,i*=c/a,o*=c/a),n+i>h&&(a=n+i,n*=h/a,i*=h/a),r+o>h&&(a=r+o,r*=h/a,o*=h/a),t.moveTo(s+r,u),t.lineTo(s+c-n,u),0!==n&&t.arc(s+c-n,u+n,n,-Math.PI/2,0),t.lineTo(s+c,u+h-i),0!==i&&t.arc(s+c-i,u+h-i,i,0,Math.PI/2),t.lineTo(s+o,u+h),0!==o&&t.arc(s+o,u+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,u+r),0!==r&&t.arc(s+r,u+r,r,Math.PI,1.5*Math.PI)}var a=r(\"9cf9\"),s=function(){function t(){this.x=0,this.y=0,this.width=0,this.height=0}return t}(),u={},c=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var r,n,i,s;if(this.subPixelOptimize){var c=Object(a[\"c\"])(u,e,this.style);r=c.x,n=c.y,i=c.width,s=c.height,c.r=e.r,e=c}else r=e.x,n=e.y,i=e.width,s=e.height;e.r?o(t,e):t.rect(r,n,i,s)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(i[\"b\"]);c.prototype.type=\"rect\";e[\"a\"]=c},ca80:function(t,e,r){\"use strict\";var n=r(\"dce8\"),i=[0,0],o=[0,0],a=new n[\"a\"],s=new n[\"a\"],u=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new n[\"a\"];for(r=0;r<2;r++)this._axes[r]=new n[\"a\"];t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,o=t.x,a=t.y,s=o+t.width,u=a+t.height;if(r[0].set(o,a),r[1].set(s,a),r[2].set(s,u),r[3].set(o,u),e)for(var c=0;c<4;c++)r[c].transform(e);n[\"a\"].sub(i[0],r[1],r[0]),n[\"a\"].sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize();for(c=0;c<2;c++)this._origin[c]=i[c].dot(r[0])},t.prototype.intersect=function(t,e){var r=!0,i=!e;return a.set(1/0,1/0),s.set(0,0),!this._intersectCheckOneSide(this,t,a,s,i,1)&&(r=!1,i)||!this._intersectCheckOneSide(t,this,a,s,i,-1)&&(r=!1,i)||i||n[\"a\"].copy(e,r?a:s),r},t.prototype._intersectCheckOneSide=function(t,e,r,a,s,u){for(var c=!0,h=0;h<2;h++){var l=this._axes[h];if(this._getProjMinMaxOnAxis(h,t._corners,i),this._getProjMinMaxOnAxis(h,e._corners,o),i[1]<o[0]||i[0]>o[1]){if(c=!1,s)return c;var f=Math.abs(o[0]-i[1]),d=Math.abs(i[0]-o[1]);Math.min(f,d)>a.len()&&(f<d?n[\"a\"].scale(a,l,-f*u):n[\"a\"].scale(a,l,d*u))}else if(r){f=Math.abs(o[0]-i[1]),d=Math.abs(i[0]-o[1]);Math.min(f,d)<r.len()&&(f<d?n[\"a\"].scale(r,l,f*u):n[\"a\"].scale(r,l,-d*u))}}return c},t.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var n=this._axes[t],i=this._origin,o=e[0].dot(n)+i[t],a=o,s=o,u=1;u<e.length;u++){var c=e[u].dot(n)+i[t];a=Math.min(c,a),s=Math.max(c,s)}r[0]=a,r[1]=s},t}();e[\"a\"]=u},ca91:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"d58f\").left,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"reduce\",(function(t){return i(o(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},caad:function(t,e,r){\"use strict\";var n=r(\"23e7\"),i=r(\"4d64\").includes,o=r(\"44d2\"),a=r(\"ae40\"),s=a(\"indexOf\",{ACCESSORS:!0,1:0});n({target:\"Array\",proto:!0,forced:!s},{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o(\"includes\")},cb11:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"9cf9\"),a={},s=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return t}(),u=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultStyle=function(){return{stroke:\"#000\",fill:null}},e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var r,n,i,s;if(this.subPixelOptimize){var u=Object(o[\"b\"])(a,e,this.style);r=u.x1,n=u.y1,i=u.x2,s=u.y2}else r=e.x1,n=e.y1,i=e.x2,s=e.y2;var c=e.percent;0!==c&&(t.moveTo(r,n),c<1&&(i=r*(1-c)+i*c,s=n*(1-c)+s*c),t.lineTo(i,s))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(i[\"b\"]);u.prototype.type=\"line\",e[\"a\"]=u},cbe5:function(t,e,r){\"use strict\";r.d(e,\"a\",(function(){return L}));var n=r(\"21a1\"),i=r(\"19eb\"),o=r(\"20c8\"),a=r(\"9680\"),s=r(\"4a3f\");function u(t,e,r,n,i,o,a,u,c,h,l){if(0===c)return!1;var f=c;if(l>e+f&&l>n+f&&l>o+f&&l>u+f||l<e-f&&l<n-f&&l<o-f&&l<u-f||h>t+f&&h>r+f&&h>i+f&&h>a+f||h<t-f&&h<r-f&&h<i-f&&h<a-f)return!1;var d=s[\"e\"](t,e,r,n,i,o,a,u,h,l,null);return d<=f/2}var c=r(\"68ab\"),h=r(\"857d\"),l=2*Math.PI;function f(t,e,r,n,i,o,a,s,u){if(0===a)return!1;var c=a;s-=t,u-=e;var f=Math.sqrt(s*s+u*u);if(f-c>r||f+c<r)return!1;if(Math.abs(n-i)%l<1e-4)return!0;if(o){var d=n;n=Object(h[\"a\"])(i),i=Object(h[\"a\"])(d)}else n=Object(h[\"a\"])(n),i=Object(h[\"a\"])(i);n>i&&(i+=l);var p=Math.atan2(u,s);return p<0&&(p+=l),p>=n&&p<=i||p+l>=n&&p+l<=i}var d=r(\"8728\"),p=o[\"a\"].CMD,v=2*Math.PI,y=1e-4;function g(t,e){return Math.abs(t-e)<y}var b=[-1,-1,-1],_=[-1,-1];function m(){var t=_[0];_[0]=_[1],_[1]=t}function x(t,e,r,n,i,o,a,u,c,h){if(h>e&&h>n&&h>o&&h>u||h<e&&h<n&&h<o&&h<u)return 0;var l=s[\"f\"](e,n,o,u,h,b);if(0===l)return 0;for(var f=0,d=-1,p=void 0,v=void 0,y=0;y<l;y++){var g=b[y],x=0===g||1===g?.5:1,w=s[\"a\"](t,r,i,a,g);w<c||(d<0&&(d=s[\"c\"](e,n,o,u,_),_[1]<_[0]&&d>1&&m(),p=s[\"a\"](e,n,o,u,_[0]),d>1&&(v=s[\"a\"](e,n,o,u,_[1]))),2===d?g<_[0]?f+=p<e?x:-x:g<_[1]?f+=v<p?x:-x:f+=u<v?x:-x:g<_[0]?f+=p<e?x:-x:f+=u<p?x:-x)}return f}function w(t,e,r,n,i,o,a,u){if(u>e&&u>n&&u>o||u<e&&u<n&&u<o)return 0;var c=s[\"m\"](e,n,o,u,b);if(0===c)return 0;var h=s[\"j\"](e,n,o);if(h>=0&&h<=1){for(var l=0,f=s[\"h\"](e,n,o,h),d=0;d<c;d++){var p=0===b[d]||1===b[d]?.5:1,v=s[\"h\"](t,r,i,b[d]);v<a||(b[d]<h?l+=f<e?p:-p:l+=o<f?p:-p)}return l}p=0===b[0]||1===b[0]?.5:1,v=s[\"h\"](t,r,i,b[0]);return v<a?0:o<e?p:-p}function O(t,e,r,n,i,o,a,s){if(s-=e,s>r||s<-r)return 0;var u=Math.sqrt(r*r-s*s);b[0]=-u,b[1]=u;var c=Math.abs(n-i);if(c<1e-4)return 0;if(c>=v-1e-4){n=0,i=v;var h=o?1:-1;return a>=b[0]+t&&a<=b[1]+t?h:0}if(n>i){var l=n;n=i,i=l}n<0&&(n+=v,i+=v);for(var f=0,d=0;d<2;d++){var p=b[d];if(p+t>a){var y=Math.atan2(s,p);h=o?1:-1;y<0&&(y=v+y),(y>=n&&y<=i||y+v>=n&&y+v<=i)&&(y>Math.PI/2&&y<1.5*Math.PI&&(h=-h),f+=h)}}return f}function k(t,e,r,n,i){for(var o,s,h=t.data,l=t.len(),v=0,y=0,b=0,_=0,m=0,k=0;k<l;){var T=h[k++],S=1===k;switch(T===p.M&&k>1&&(r||(v+=Object(d[\"a\"])(y,b,_,m,n,i))),S&&(y=h[k],b=h[k+1],_=y,m=b),T){case p.M:_=h[k++],m=h[k++],y=_,b=m;break;case p.L:if(r){if(a[\"a\"](y,b,h[k],h[k+1],e,n,i))return!0}else v+=Object(d[\"a\"])(y,b,h[k],h[k+1],n,i)||0;y=h[k++],b=h[k++];break;case p.C:if(r){if(u(y,b,h[k++],h[k++],h[k++],h[k++],h[k],h[k+1],e,n,i))return!0}else v+=x(y,b,h[k++],h[k++],h[k++],h[k++],h[k],h[k+1],n,i)||0;y=h[k++],b=h[k++];break;case p.Q:if(r){if(c[\"a\"](y,b,h[k++],h[k++],h[k],h[k+1],e,n,i))return!0}else v+=w(y,b,h[k++],h[k++],h[k],h[k+1],n,i)||0;y=h[k++],b=h[k++];break;case p.A:var j=h[k++],C=h[k++],A=h[k++],P=h[k++],M=h[k++],L=h[k++];k+=1;var I=!!(1-h[k++]);o=Math.cos(M)*A+j,s=Math.sin(M)*P+C,S?(_=o,m=s):v+=Object(d[\"a\"])(y,b,o,s,n,i);var D=(n-j)*P/A+j;if(r){if(f(j,C,P,M,M+L,I,e,D,i))return!0}else v+=O(j,C,P,M,M+L,I,D,i);y=Math.cos(M+L)*A+j,b=Math.sin(M+L)*P+C;break;case p.R:_=y=h[k++],m=b=h[k++];var F=h[k++],z=h[k++];if(o=_+F,s=m+z,r){if(a[\"a\"](_,m,o,m,e,n,i)||a[\"a\"](o,m,o,s,e,n,i)||a[\"a\"](o,s,_,s,e,n,i)||a[\"a\"](_,s,_,m,e,n,i))return!0}else v+=Object(d[\"a\"])(o,m,o,s,n,i),v+=Object(d[\"a\"])(_,s,_,m,n,i);break;case p.Z:if(r){if(a[\"a\"](y,b,_,m,e,n,i))return!0}else v+=Object(d[\"a\"])(y,b,_,m,n,i);y=_,b=m;break}}return r||g(b,m)||(v+=Object(d[\"a\"])(y,b,_,m,n,i)||0),0!==v}function T(t,e,r){return k(t,0,!1,e,r)}function S(t,e,r,n){return k(t,e,!0,r,n)}var j=r(\"6d8b\"),C=r(\"41ef\"),A=r(\"2cf4c\"),P=r(\"4bc4\"),M=r(\"8582\"),L=Object(j[\"i\"])({fill:\"#000\",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:\"butt\",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},i[\"b\"]),I={style:Object(j[\"i\"])({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},i[\"a\"].style)},D=M[\"a\"].concat([\"invisible\",\"culling\",\"z\",\"z2\",\"zlevel\",\"parent\"]),F=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.update=function(){var r=this;t.prototype.update.call(this);var n=this.style;if(n.decal){var i=this._decalEl=this._decalEl||new e;i.buildPath===e.prototype.buildPath&&(i.buildPath=function(t){r.buildPath(t,r.shape)}),i.silent=!0;var o=i.style;for(var a in n)o[a]!==n[a]&&(o[a]=n[a]);o.fill=n.fill?n.decal:null,o.decal=null,o.shadowColor=null,n.strokeFirst&&(o.stroke=null);for(var s=0;s<D.length;++s)i[D[s]]=this[D[s]];i.__dirty|=P[\"a\"]}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var r=Object(j[\"F\"])(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var i=0;i<r.length;i++){var o=r[i],a=e[o];\"style\"===o?this.style?Object(j[\"m\"])(this.style,a):this.useStyle(a):\"shape\"===o?Object(j[\"m\"])(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if(\"none\"!==t){if(Object(j[\"C\"])(t)){var e=Object(C[\"lum\"])(t,0);return e>.5?A[\"a\"]:e>.2?A[\"c\"]:A[\"d\"]}if(t)return A[\"d\"]}return A[\"a\"]},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(Object(j[\"C\"])(e)){var r=this.__zr,n=!(!r||!r.isDarkMode()),i=Object(C[\"lum\"])(t,0)<A[\"b\"];if(n===i)return e}},e.prototype.buildPath=function(t,e,r){},e.prototype.pathUpdated=function(){this.__dirty&=~P[\"b\"]},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new o[\"a\"](!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||\"none\"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&\"none\"!==e},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,r=!t;if(r){var n=!1;this.path||(n=!0,this.createPathProxy());var i=this.path;(n||this.__dirty&P[\"b\"])&&(i.beginPath(),this.buildPath(i,this.shape,!1),this.pathUpdated()),t=i.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||r){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;s=Math.max(s,null==u?4:u)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),i=this.style;if(t=r[0],e=r[1],n.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=i.lineWidth,s=i.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),S(o,a/s,t,e)))return!0}if(this.hasFill())return T(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=P[\"b\"],this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate(\"shape\",t)},e.prototype.updateDuringAnimation=function(t){\"style\"===t?this.dirtyStyle():\"shape\"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,r){\"shape\"===e?this.setShape(r):t.prototype.attrKV.call(this,e,r)},e.prototype.setShape=function(t,e){var r=this.shape;return r||(r=this.shape={}),\"string\"===typeof t?r[t]=e:Object(j[\"m\"])(r,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(this.__dirty&P[\"b\"])},e.prototype.createStyle=function(t){return Object(j[\"g\"])(L,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var r=this._normalState;e.shape&&!r.shape&&(r.shape=Object(j[\"m\"])({},this.shape))},e.prototype._applyStateObj=function(e,r,n,i,o,a){t.prototype._applyStateObj.call(this,e,r,n,i,o,a);var s,u=!(r&&i);if(r&&r.shape?o?i?s=r.shape:(s=Object(j[\"m\"])({},n.shape),Object(j[\"m\"])(s,r.shape)):(s=Object(j[\"m\"])({},i?this.shape:n.shape),Object(j[\"m\"])(s,r.shape)):u&&(s=n.shape),s)if(o){this.shape=Object(j[\"m\"])({},this.shape);for(var c={},h=Object(j[\"F\"])(s),l=0;l<h.length;l++){var f=h[l];\"object\"===typeof s[f]?this.shape[f]=s[f]:c[f]=s[f]}this._transitionState(e,{shape:c},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var r,n=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.shape&&(r=r||{},this._mergeStyle(r,o.shape))}return r&&(n.shape=r),n},e.prototype.getAnimationStyleProps=function(){return I},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var r=function(e){function r(r){var n=e.call(this,r)||this;return t.init&&t.init.call(n,r),n}return Object(n[\"a\"])(r,e),r.prototype.getDefaultStyle=function(){return Object(j[\"d\"])(t.style)},r.prototype.getDefaultShape=function(){return Object(j[\"d\"])(t.shape)},r}(e);for(var i in t)\"function\"===typeof t[i]&&(r.prototype[i]=t[i]);return r},e.initDefaultProps=function(){var t=e.prototype;t.type=\"path\",t.strokeContainThreshold=5,t.segmentIgnoreThreshold=0,t.subPixelOptimize=!1,t.autoBatch=!1,t.__dirty=P[\"a\"]|P[\"c\"]|P[\"b\"]}(),e}(i[\"c\"]);e[\"b\"]=F},cd26:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=n.aTypedArray,o=n.exportTypedArrayMethod,a=Math.floor;o(\"reverse\",(function(){var t,e=this,r=i(e).length,n=a(r/2),o=0;while(o<n)t=e[o],e[o++]=e[--r],e[r]=t;return e}))},d139:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").find,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"find\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},d409:function(t,e,r){\"use strict\";r.d(e,\"c\",(function(){return s})),r.d(e,\"a\",(function(){return f})),r.d(e,\"b\",(function(){return y}));var n=r(\"5e76\"),i=r(\"6d8b\"),o=r(\"e86a\"),a=/\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;function s(t,e,r,n,i){var o={};return u(o,t,e,r,n,i),o.text}function u(t,e,r,n,i,o){if(!r)return t.text=\"\",void(t.isTruncated=!1);var a=(e+\"\").split(\"\\n\");o=c(r,n,i,o);for(var s=!1,u={},l=0,f=a.length;l<f;l++)h(u,a[l],o),a[l]=u.textLine,s=s||u.isTruncated;t.text=a.join(\"\\n\"),t.isTruncated=s}function c(t,e,r,n){n=n||{};var a=Object(i[\"m\"])({},n);a.font=e,r=Object(i[\"P\"])(r,\"...\"),a.maxIterations=Object(i[\"P\"])(n.maxIterations,2);var s=a.minChar=Object(i[\"P\"])(n.minChar,0);a.cnCharWidth=Object(o[\"f\"])(\"国\",e);var u=a.ascCharWidth=Object(o[\"f\"])(\"a\",e);a.placeholder=Object(i[\"P\"])(n.placeholder,\"\");for(var c=t=Math.max(0,t-1),h=0;h<s&&c>=u;h++)c-=u;var l=Object(o[\"f\"])(r,e);return l>c&&(r=\"\",l=0),c=t-l,a.ellipsis=r,a.ellipsisWidth=l,a.contentWidth=c,a.containerWidth=t,a}function h(t,e,r){var n=r.containerWidth,i=r.font,a=r.contentWidth;if(!n)return t.textLine=\"\",void(t.isTruncated=!1);var s=Object(o[\"f\"])(e,i);if(s<=n)return t.textLine=e,void(t.isTruncated=!1);for(var u=0;;u++){if(s<=a||u>=r.maxIterations){e+=r.ellipsis;break}var c=0===u?l(e,a,r.ascCharWidth,r.cnCharWidth):s>0?Math.floor(e.length*a/s):0;e=e.substr(0,c),s=Object(o[\"f\"])(e,i)}\"\"===e&&(e=r.placeholder),t.textLine=e,t.isTruncated=!0}function l(t,e,r,n){for(var i=0,o=0,a=t.length;o<a&&i<e;o++){var s=t.charCodeAt(o);i+=0<=s&&s<=127?r:n}return o}function f(t,e){null!=t&&(t+=\"\");var r,n=e.overflow,a=e.padding,s=e.font,u=\"truncate\"===n,l=Object(o[\"e\"])(s),f=Object(i[\"P\"])(e.lineHeight,l),d=!!e.backgroundColor,p=\"truncate\"===e.lineOverflow,v=!1,y=e.width;r=null==y||\"break\"!==n&&\"breakAll\"!==n?t?t.split(\"\\n\"):[]:t?x(t,e.font,y,\"breakAll\"===n,0).lines:[];var g=r.length*f,b=Object(i[\"P\"])(e.height,g);if(g>b&&p){var _=Math.floor(b/f);v=v||r.length>_,r=r.slice(0,_)}if(t&&u&&null!=y)for(var m=c(y,s,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),w={},O=0;O<r.length;O++)h(w,r[O],m),r[O]=w.textLine,v=v||w.isTruncated;var k=b,T=0;for(O=0;O<r.length;O++)T=Math.max(Object(o[\"f\"])(r[O],s),T);null==y&&(y=T);var S=T;return a&&(k+=a[0]+a[2],S+=a[1]+a[3],y+=a[1]+a[3]),d&&(S=y),{lines:r,height:b,outerWidth:S,outerHeight:k,lineHeight:f,calculatedLineHeight:l,contentWidth:T,contentHeight:g,width:y,isTruncated:v}}var d=function(){function t(){}return t}(),p=function(){function t(t){this.tokens=[],t&&(this.tokens=t)}return t}(),v=function(){function t(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return t}();function y(t,e){var r=new v;if(null!=t&&(t+=\"\"),!t)return r;var s,c=e.width,h=e.height,l=e.overflow,f=\"break\"!==l&&\"breakAll\"!==l||null==c?null:{width:c,accumWidth:0,breakAll:\"breakAll\"===l},d=a.lastIndex=0;while(null!=(s=a.exec(t))){var p=s.index;p>d&&g(r,t.substring(d,p),e,f),g(r,s[2],e,f,s[1]),d=a.lastIndex}d<t.length&&g(r,t.substring(d,t.length),e,f);var y=[],b=0,_=0,m=e.padding,x=\"truncate\"===l,w=\"truncate\"===e.lineOverflow,O={};function k(t,e,r){t.width=e,t.lineHeight=r,b+=r,_=Math.max(_,e)}t:for(var T=0;T<r.lines.length;T++){for(var S=r.lines[T],j=0,C=0,A=0;A<S.tokens.length;A++){var P=S.tokens[A],M=P.styleName&&e.rich[P.styleName]||{},L=P.textPadding=M.padding,I=L?L[1]+L[3]:0,D=P.font=M.font||e.font;P.contentHeight=Object(o[\"e\"])(D);var F=Object(i[\"P\"])(M.height,P.contentHeight);if(P.innerHeight=F,L&&(F+=L[0]+L[2]),P.height=F,P.lineHeight=Object(i[\"Q\"])(M.lineHeight,e.lineHeight,F),P.align=M&&M.align||e.align,P.verticalAlign=M&&M.verticalAlign||\"middle\",w&&null!=h&&b+P.lineHeight>h){var z=r.lines.length;A>0?(S.tokens=S.tokens.slice(0,A),k(S,C,j),r.lines=r.lines.slice(0,T+1)):r.lines=r.lines.slice(0,T),r.isTruncated=r.isTruncated||r.lines.length<z;break t}var R=M.width,B=null==R||\"auto\"===R;if(\"string\"===typeof R&&\"%\"===R.charAt(R.length-1))P.percentWidth=R,y.push(P),P.contentWidth=Object(o[\"f\"])(P.text,D);else{if(B){var E=M.backgroundColor,N=E&&E.image;N&&(N=n[\"b\"](N),n[\"c\"](N)&&(P.width=Math.max(P.width,N.width*F/N.height)))}var H=x&&null!=c?c-C:null;null!=H&&H<P.width?!B||H<I?(P.text=\"\",P.width=P.contentWidth=0):(u(O,P.text,H-I,D,e.ellipsis,{minChar:e.truncateMinChar}),P.text=O.text,r.isTruncated=r.isTruncated||O.isTruncated,P.width=P.contentWidth=Object(o[\"f\"])(P.text,D)):P.contentWidth=Object(o[\"f\"])(P.text,D)}P.width+=I,C+=P.width,M&&(j=Math.max(j,P.lineHeight))}k(S,C,j)}r.outerWidth=r.width=Object(i[\"P\"])(c,_),r.outerHeight=r.height=Object(i[\"P\"])(h,b),r.contentHeight=b,r.contentWidth=_,m&&(r.outerWidth+=m[1]+m[3],r.outerHeight+=m[0]+m[2]);for(T=0;T<y.length;T++){P=y[T];var q=P.percentWidth;P.width=parseInt(q,10)/100*r.width}return r}function g(t,e,r,n,i){var a,s,u=\"\"===e,c=i&&r.rich[i]||{},h=t.lines,l=c.font||r.font,f=!1;if(n){var v=c.padding,y=v?v[1]+v[3]:0;if(null!=c.width&&\"auto\"!==c.width){var g=Object(o[\"g\"])(c.width,n.width)+y;h.length>0&&g+n.accumWidth>n.width&&(a=e.split(\"\\n\"),f=!0),n.accumWidth=g}else{var b=x(e,l,n.width,n.breakAll,n.accumWidth);n.accumWidth=b.accumWidth+y,s=b.linesWidths,a=b.lines}}else a=e.split(\"\\n\");for(var _=0;_<a.length;_++){var m=a[_],w=new d;if(w.styleName=i,w.text=m,w.isLineHolder=!m&&!u,\"number\"===typeof c.width?w.width=c.width:w.width=s?s[_]:Object(o[\"f\"])(m,l),_||f)h.push(new p([w]));else{var O=(h[h.length-1]||(h[0]=new p)).tokens,k=O.length;1===k&&O[0].isLineHolder?O[0]=w:(m||!k||u)&&O.push(w)}}}function b(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}var _=Object(i[\"N\"])(\",&?/;] \".split(\"\"),(function(t,e){return t[e]=!0,t}),{});function m(t){return!b(t)||!!_[t]}function x(t,e,r,n,i){for(var a=[],s=[],u=\"\",c=\"\",h=0,l=0,f=0;f<t.length;f++){var d=t.charAt(f);if(\"\\n\"!==d){var p=Object(o[\"f\"])(d,e),v=!n&&!m(d);(a.length?l+p>r:i+l+p>r)?l?(u||c)&&(v?(u||(u=c,c=\"\",h=0,l=h),a.push(u),s.push(l-h),c+=d,h+=p,u=\"\",l=h):(c&&(u+=c,c=\"\",h=0),a.push(u),s.push(l),u=d,l=p)):v?(a.push(c),s.push(h),c=d,h=p):(a.push(d),s.push(p)):(l+=p,v?(c+=d,h+=p):(c&&(u+=c,c=\"\",h=0),u+=d))}else c&&(u+=c,l+=h),a.push(u),s.push(l),u=\"\",c=\"\",h=0,l=0}return a.length||u||(u=t,c=\"\",h=0),c&&(u+=c),u&&(a.push(u),s.push(l)),1===a.length&&(l+=i),{accumWidth:l,lines:a,linesWidths:s}}},d498:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=r(\"4fac\"),a=function(){function t(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultStyle=function(){return{stroke:\"#000\",fill:null}},e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o[\"a\"](t,e,!1)},e}(i[\"b\"]);s.prototype.type=\"polyline\",e[\"a\"]=s},d4c6:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=\"compound\",e}return Object(n[\"a\"])(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var r=e.paths||[],n=0;n<r.length;n++)r[n].buildPath(t,r[n].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),i[\"b\"].prototype.getBoundingRect.call(this)},e}(i[\"b\"]);e[\"a\"]=o},d51b:function(t,e,r){\"use strict\";var n=function(){function t(t){this.value=t}return t}(),i=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new n(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),o=function(){function t(t){this._list=new i,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var r=this._list,i=this._map,o=null;if(null==i[t]){var a=r.len(),s=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var u=r.head;r.remove(u),delete i[u.key],o=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new n(e),s.key=t,r.insertEntry(s),i[t]=s}return o},t.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();e[\"a\"]=o},d5b7:function(t,e,r){\"use strict\";var n=r(\"8582\"),i=r(\"06ad\"),o=r(\"9850\"),a=r(\"6fd3\"),s=r(\"e86a\"),u=r(\"6d8b\"),c=r(\"2cf4c\"),h=r(\"41ef\"),l=r(\"4bc4\"),f=\"__zr_normal__\",d=n[\"a\"].concat([\"ignore\"]),p=Object(u[\"N\"])(n[\"a\"],(function(t,e){return t[e]=!0,t}),{ignore:!1}),v={},y=new o[\"a\"](0,0,0,0),g=function(){function t(t){this.id=Object(u[\"p\"])(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,r){switch(this.draggable){case\"horizontal\":e=0;break;case\"vertical\":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var r=this.textConfig,n=r.local,i=e.innerTransformable,o=void 0,a=void 0,u=!1;i.parent=n?this:null;var c=!1;if(i.copyTransform(e),null!=r.position){var h=y;r.layoutRect?h.copy(r.layoutRect):h.copy(this.getBoundingRect()),n||h.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(v,r,h):Object(s[\"c\"])(v,r,h),i.x=v.x,i.y=v.y,o=v.align,a=v.verticalAlign;var f=r.origin;if(f&&null!=r.rotation){var d=void 0,p=void 0;\"center\"===f?(d=.5*h.width,p=.5*h.height):(d=Object(s[\"g\"])(f[0],h.width),p=Object(s[\"g\"])(f[1],h.height)),c=!0,i.originX=-i.x+d+(n?0:h.x),i.originY=-i.y+p+(n?0:h.y)}}null!=r.rotation&&(i.rotation=r.rotation);var g=r.offset;g&&(i.x+=g[0],i.y+=g[1],c||(i.originX=-g[0],i.originY=-g[1]));var b=null==r.inside?\"string\"===typeof r.position&&r.position.indexOf(\"inside\")>=0:r.inside,_=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),m=void 0,x=void 0,w=void 0;b&&this.canBeInsideText()?(m=r.insideFill,x=r.insideStroke,null!=m&&\"auto\"!==m||(m=this.getInsideTextFill()),null!=x&&\"auto\"!==x||(x=this.getInsideTextStroke(m),w=!0)):(m=r.outsideFill,x=r.outsideStroke,null!=m&&\"auto\"!==m||(m=this.getOutsideFill()),null!=x&&\"auto\"!==x||(x=this.getOutsideStroke(m),w=!0)),m=m||\"#000\",m===_.fill&&x===_.stroke&&w===_.autoStroke&&o===_.align&&a===_.verticalAlign||(u=!0,_.fill=m,_.stroke=x,_.autoStroke=w,_.align=o,_.verticalAlign=a,e.setDefaultTextStyle(_)),e.__dirty|=l[\"a\"],u&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return\"#fff\"},t.prototype.getInsideTextStroke=function(t){return\"#000\"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?c[\"d\"]:c[\"a\"]},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),r=\"string\"===typeof e&&Object(h[\"parse\"])(e);r||(r=[255,255,255,1]);for(var n=r[3],i=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*n+(i?0:255)*(1-n);return r[3]=1,Object(h[\"stringify\"])(r,\"rgba\")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){\"textConfig\"===t?this.setTextConfig(e):\"textContent\"===t?this.setTextContent(e):\"clipPath\"===t?this.setClipPath(e):\"extra\"===t?(this.extra=this.extra||{},Object(u[\"m\"])(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if(\"string\"===typeof t)this.attrKV(t,e);else if(Object(u[\"A\"])(t))for(var r=t,n=Object(u[\"F\"])(r),i=0;i<n.length;i++){var o=n[i];this.attrKV(o,t[o])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var n=this.animators[r],i=n.__fromStateTransition;if(!(n.getLoop()||i&&i!==f)){var o=n.targetName,a=o?e[o]:e;n.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,d)},t.prototype._savePrimaryToNormal=function(t,e,r){for(var n=0;n<r.length;n++){var i=r[n];null==t[i]||i in e||(e[i]=this[i])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(f,!1,t)},t.prototype.useState=function(t,e,r,n){var i=t===f,o=this.hasState();if(o||!i){var a=this.currentStates,s=this.stateTransition;if(!(Object(u[\"r\"])(a,t)>=0)||!e&&1!==a.length){var c;if(this.stateProxy&&!i&&(c=this.stateProxy(t)),c||(c=this.states&&this.states[t]),c||i){i||this.saveCurrentToNormalState(c);var h=!!(c&&c.hoverLayer||n);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,c,this._normalState,e,!r&&!this.__inHover&&s&&s.duration>0,s);var d=this._textContent,p=this._textGuide;return d&&d.useState(t,e,r,h),p&&p.useState(t,e,r,h),i?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~l[\"a\"]),c}Object(u[\"G\"])(\"State \"+t+\" not exists.\")}}},t.prototype.useStates=function(t,e,r){if(t.length){var n=[],i=this.currentStates,o=t.length,a=o===i.length;if(a)for(var s=0;s<o;s++)if(t[s]!==i[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var u=t[s],c=void 0;this.stateProxy&&(c=this.stateProxy(u,t)),c||(c=this.states[u]),c&&n.push(c)}var h=n[o-1],f=!!(h&&h.hoverLayer||r);f&&this._toggleHoverLayerFlag(!0);var d=this._mergeStates(n),p=this.stateTransition;this.saveCurrentToNormalState(d),this._applyStateObj(t.join(\",\"),d,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var v=this._textContent,y=this._textGuide;v&&v.useStates(t,e,f),y&&y.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~l[\"a\"])}else this.clearStates()},t.prototype.isSilent=function(){var t=this.silent,e=this.parent;while(!t&&e){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=Object(u[\"r\"])(this.currentStates,t);if(e>=0){var r=this.currentStates.slice();r.splice(e,1),this.useStates(r)}},t.prototype.replaceState=function(t,e,r){var n=this.currentStates.slice(),i=Object(u[\"r\"])(n,t),o=Object(u[\"r\"])(n,e)>=0;i>=0?o?n.splice(i,1):n[i]=e:r&&!o&&n.push(e),this.useStates(n)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,r={},n=0;n<t.length;n++){var i=t[n];Object(u[\"m\"])(r,i),i.textConfig&&(e=e||{},Object(u[\"m\"])(e,i.textConfig))}return e&&(r.textConfig=e),r},t.prototype._applyStateObj=function(t,e,r,n,i,o){var a=!(e&&n);e&&e.textConfig?(this.textConfig=Object(u[\"m\"])({},n?this.textConfig:r.textConfig),Object(u[\"m\"])(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},c=!1,h=0;h<d.length;h++){var l=d[h],f=i&&p[l];e&&null!=e[l]?f?(c=!0,s[l]=e[l]):this[l]=e[l]:a&&null!=r[l]&&(f?(c=!0,s[l]=r[l]):this[l]=r[l])}if(!i)for(h=0;h<this.animators.length;h++){var v=this.animators[h],y=v.targetName;v.getLoop()||v.__changeFinalValue(y?(e||r)[y]:e||r)}c&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new n[\"c\"],this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),Object(u[\"m\"])(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=l[\"a\"];var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,r){var n=t?this[t]:this;var o=new i[\"b\"](n,e,r);return t&&(o.targetName=t),this.addAnimator(o,t),o},t.prototype.addAnimator=function(t,e){var r=this.__zr,n=this;t.during((function(){n.updateDuringAnimation(e)})).done((function(){var e=n.animators,r=Object(u[\"r\"])(e,t);r>=0&&e.splice(r,1)})),this.animators.push(t),r&&r.animation.addAnimator(t),r&&r.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var r=this.animators,n=r.length,i=[],o=0;o<n;o++){var a=r[o];t&&t!==a.scope?i.push(a):a.stop(e)}return this.animators=i,this},t.prototype.animateTo=function(t,e,r){b(this,t,e,r)},t.prototype.animateFrom=function(t,e,r){b(this,t,e,r,!0)},t.prototype._transitionState=function(t,e,r,n){for(var i=b(this,e,r,n),o=0;o<i.length;o++)i[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type=\"element\",e.name=\"\",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=l[\"a\"];function r(t,r,n,i){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[n]},set:function(e){t[n]=e}}),Object.defineProperty(e,1,{get:function(){return t[i]},set:function(e){t[i]=e}})}Object.defineProperty(e,t,{get:function(){if(!this[r]){var t=this[r]=[];o(this,t)}return this[r]},set:function(t){this[n]=t[0],this[i]=t[1],this[r]=t,o(this,t)}})}Object.defineProperty&&(r(\"position\",\"_legacyPos\",\"x\",\"y\"),r(\"scale\",\"_legacyScale\",\"scaleX\",\"scaleY\"),r(\"origin\",\"_legacyOrigin\",\"originX\",\"originY\"))}(),t}();function b(t,e,r,n,i){r=r||{};var o=[];k(t,\"\",t,e,r,n,o,i);var a=o.length,s=!1,u=r.done,c=r.aborted,h=function(){s=!0,a--,a<=0&&(s?u&&u():c&&c())},l=function(){a--,a<=0&&(s?u&&u():c&&c())};a||u&&u(),o.length>0&&r.during&&o[0].during((function(t,e){r.during(e)}));for(var f=0;f<o.length;f++){var d=o[f];h&&d.done(h),l&&d.aborted(l),r.force&&d.duration(r.duration),d.start(r.easing)}return o}function _(t,e,r){for(var n=0;n<r;n++)t[n]=e[n]}function m(t){return Object(u[\"u\"])(t[0])}function x(t,e,r){if(Object(u[\"u\"])(e[r]))if(Object(u[\"u\"])(t[r])||(t[r]=[]),Object(u[\"E\"])(e[r])){var n=e[r].length;t[r].length!==n&&(t[r]=new e[r].constructor(n),_(t[r],e[r],n))}else{var i=e[r],o=t[r],a=i.length;if(m(i))for(var s=i[0].length,c=0;c<a;c++)o[c]?_(o[c],i[c],s):o[c]=Array.prototype.slice.call(i[c]);else _(o,i,a);o.length=i.length}else t[r]=e[r]}function w(t,e){return t===e||Object(u[\"u\"])(t)&&Object(u[\"u\"])(e)&&O(t,e)}function O(t,e){var r=t.length;if(r!==e.length)return!1;for(var n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function k(t,e,r,n,o,a,s,c){for(var h=Object(u[\"F\"])(n),l=o.duration,f=o.delay,d=o.additive,p=o.setToFinal,v=!Object(u[\"A\"])(a),y=t.animators,g=[],b=0;b<h.length;b++){var _=h[b],m=n[_];if(null!=m&&null!=r[_]&&(v||a[_]))if(!Object(u[\"A\"])(m)||Object(u[\"u\"])(m)||Object(u[\"x\"])(m))g.push(_);else{if(e){c||(r[_]=m,t.updateDuringAnimation(e));continue}k(t,_,r[_],m,o,a&&a[_],s,c)}else c||(r[_]=m,t.updateDuringAnimation(e),g.push(_))}var O=g.length;if(!d&&O)for(var T=0;T<y.length;T++){var S=y[T];if(S.targetName===e){var j=S.stopTracks(g);if(j){var C=Object(u[\"r\"])(y,S);y.splice(C,1)}}}if(o.force||(g=Object(u[\"n\"])(g,(function(t){return!w(n[t],r[t])})),O=g.length),O>0||o.force&&!s.length){var A=void 0,P=void 0,M=void 0;if(c){P={},p&&(A={});for(T=0;T<O;T++){_=g[T];P[_]=r[_],p?A[_]=n[_]:r[_]=n[_]}}else if(p){M={};for(T=0;T<O;T++){_=g[T];M[_]=Object(i[\"a\"])(r[_]),x(r,n,_)}}S=new i[\"b\"](r,!1,!1,d?Object(u[\"n\"])(y,(function(t){return t.targetName===e})):null);S.targetName=e,o.scope&&(S.scope=o.scope),p&&A&&S.whenWithKeys(0,A,g),M&&S.whenWithKeys(0,M,g),S.whenWithKeys(null==l?500:l,c?P:n,g).delay(f||0),t.addAnimator(S,e),s.push(S)}}Object(u[\"K\"])(g,a[\"a\"]),Object(u[\"K\"])(g,n[\"c\"]),e[\"a\"]=g},d5d6:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"b727\").forEach,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"forEach\",(function(t){i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},d81d:function(t,e,r){\"use strict\";var n=r(\"23e7\"),i=r(\"b727\").map,o=r(\"1dde\"),a=r(\"ae40\"),s=o(\"map\"),u=a(\"map\");n({target:\"Array\",proto:!0,forced:!s||!u},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},d9fc:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"cbe5\"),o=function(){function t(){this.cx=0,this.cy=0,this.r=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return Object(n[\"a\"])(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(i[\"b\"]);a.prototype.type=\"circle\",e[\"a\"]=a},dc20:function(t,e,r){\"use strict\";var n=r(\"7a29\"),i=r(\"cbe5\"),o=r(\"0da8\"),a=r(\"e86a\"),s=r(\"dd4f\"),u=Math.sin,c=Math.cos,h=Math.PI,l=2*Math.PI,f=180/h,d=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str=\"\",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add(\"M\",t,e)},t.prototype.lineTo=function(t,e){this._add(\"L\",t,e)},t.prototype.bezierCurveTo=function(t,e,r,n,i,o){this._add(\"C\",t,e,r,n,i,o)},t.prototype.quadraticCurveTo=function(t,e,r,n){this._add(\"Q\",t,e,r,n)},t.prototype.arc=function(t,e,r,n,i,o){this.ellipse(t,e,r,r,0,n,i,o)},t.prototype.ellipse=function(t,e,r,i,o,a,s,d){var p=s-a,v=!d,y=Math.abs(p),g=Object(n[\"j\"])(y-l)||(v?p>=l:-p>=l),b=p>0?p%l:p%l+l,_=!1;_=!!g||!Object(n[\"j\"])(y)&&b>=h===!!v;var m=t+r*c(a),x=e+i*u(a);this._start&&this._add(\"M\",m,x);var w=Math.round(o*f);if(g){var O=1/this._p,k=(v?1:-1)*(l-O);this._add(\"A\",r,i,w,1,+v,t+r*c(a+k),e+i*u(a+k)),O>.01&&this._add(\"A\",r,i,w,0,+v,m,x)}else{var T=t+r*c(s),S=e+i*u(s);this._add(\"A\",r,i,w,+_,+v,T,S)}},t.prototype.rect=function(t,e,r,n){this._add(\"M\",t,e),this._add(\"l\",r,0),this._add(\"l\",0,n),this._add(\"l\",-r,0),this._add(\"Z\")},t.prototype.closePath=function(){this._d.length>0&&this._add(\"Z\")},t.prototype._add=function(t,e,r,n,i,o,a,s,u){for(var c=[],h=this._p,l=1;l<arguments.length;l++){var f=arguments[l];if(isNaN(f))return void(this._invalid=!0);c.push(Math.round(f*h)/h)}this._d.push(t+c.join(\" \")),this._start=\"Z\"===t},t.prototype.generateStr=function(){this._str=this._invalid?\"\":this._d.join(\"\"),this._d=[]},t.prototype.getStr=function(){return this._str},t}(),p=d,v=r(\"8d1d\"),y=r(\"6d8b\"),g=\"none\",b=Math.round;function _(t){var e=t.fill;return null!=e&&e!==g}function m(t){var e=t.stroke;return null!=e&&e!==g}var x=[\"lineCap\",\"miterLimit\",\"lineJoin\"],w=Object(y[\"H\"])(x,(function(t){return\"stroke-\"+t.toLowerCase()}));function O(t,e,r,a){var s=null==e.opacity?1:e.opacity;if(r instanceof o[\"a\"])t(\"opacity\",s);else{if(_(e)){var u=Object(n[\"p\"])(e.fill);t(\"fill\",u.color);var c=null!=e.fillOpacity?e.fillOpacity*u.opacity*s:u.opacity*s;(a||c<1)&&t(\"fill-opacity\",c)}else t(\"fill\",g);if(m(e)){var h=Object(n[\"p\"])(e.stroke);t(\"stroke\",h.color);var l=e.strokeNoScale?r.getLineScale():1,f=l?(e.lineWidth||0)/l:0,d=null!=e.strokeOpacity?e.strokeOpacity*h.opacity*s:h.opacity*s,p=e.strokeFirst;if((a||1!==f)&&t(\"stroke-width\",f),(a||p)&&t(\"paint-order\",p?\"stroke\":\"fill\"),(a||d<1)&&t(\"stroke-opacity\",d),e.lineDash){var y=Object(v[\"a\"])(r),O=y[0],k=y[1];O&&(k=b(k||0),t(\"stroke-dasharray\",O.join(\",\")),(k||a)&&t(\"stroke-dashoffset\",k))}else a&&t(\"stroke-dasharray\",g);for(var T=0;T<x.length;T++){var S=x[T];if(a||e[S]!==i[\"a\"][S]){var j=e[S]||i[\"a\"][S];j&&t(w[T],j)}}}else a&&t(\"stroke\",g)}}var k=r(\"65ed\"),T=\"http://www.w3.org/2000/svg\",S=\"http://www.w3.org/1999/xlink\",j=\"http://www.w3.org/2000/xmlns/\",C=\"http://www.w3.org/XML/1998/namespace\",A=\"ecmeta_\";function P(t){return document.createElementNS(T,t)}function M(t,e,r,n,i){return{tag:t,attrs:r||{},children:n,text:i,key:e}}function L(t,e){var r=[];if(e)for(var n in e){var i=e[n],o=n;!1!==i&&(!0!==i&&null!=i&&(o+='=\"'+i+'\"'),r.push(o))}return\"<\"+t+\" \"+r.join(\" \")+\">\"}function I(t){return\"</\"+t+\">\"}function D(t,e){e=e||{};var r=e.newline?\"\\n\":\"\";function n(t){var e=t.children,i=t.tag,o=t.attrs,a=t.text;return L(i,o)+(\"style\"!==i?Object(k[\"a\"])(a):a||\"\")+(e?\"\"+r+Object(y[\"H\"])(e,(function(t){return n(t)})).join(r)+r:\"\")+I(i)}return n(t)}function F(t,e,r){r=r||{};var n=r.newline?\"\\n\":\"\",i=\" {\"+n,o=n+\"}\",a=Object(y[\"H\"])(Object(y[\"F\"])(t),(function(e){return e+i+Object(y[\"H\"])(Object(y[\"F\"])(t[e]),(function(r){return r+\":\"+t[e][r]+\";\"})).join(n)+o})).join(n),s=Object(y[\"H\"])(Object(y[\"F\"])(e),(function(t){return\"@keyframes \"+t+i+Object(y[\"H\"])(Object(y[\"F\"])(e[t]),(function(r){return r+i+Object(y[\"H\"])(Object(y[\"F\"])(e[t][r]),(function(n){var i=e[t][r][n];return\"d\"===n&&(i='path(\"'+i+'\")'),n+\":\"+i+\";\"})).join(n)+o})).join(n)+o})).join(n);return a||s?[\"<![CDATA[\",a,s,\"]]>\"].join(n):\"\"}function z(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function R(t,e,r,n){return M(\"svg\",\"root\",{width:t,height:e,xmlns:T,\"xmlns:xlink\":S,version:\"1.1\",baseProfile:\"full\",viewBox:!!n&&\"0 0 \"+t+\" \"+e},r)}var B=r(\"5e76\"),E=r(\"8582\"),N=r(\"20c8\"),H=r(\"d4c6\"),q=r(\"b362\"),W=0;function Y(){return W++}var X={cubicIn:\"0.32,0,0.67,0\",cubicOut:\"0.33,1,0.68,1\",cubicInOut:\"0.65,0,0.35,1\",quadraticIn:\"0.11,0,0.5,0\",quadraticOut:\"0.5,1,0.89,1\",quadraticInOut:\"0.45,0,0.55,1\",quarticIn:\"0.5,0,0.75,0\",quarticOut:\"0.25,1,0.5,1\",quarticInOut:\"0.76,0,0.24,1\",quinticIn:\"0.64,0,0.78,0\",quinticOut:\"0.22,1,0.36,1\",quinticInOut:\"0.83,0,0.17,1\",sinusoidalIn:\"0.12,0,0.39,0\",sinusoidalOut:\"0.61,1,0.88,1\",sinusoidalInOut:\"0.37,0,0.63,1\",exponentialIn:\"0.7,0,0.84,0\",exponentialOut:\"0.16,1,0.3,1\",exponentialInOut:\"0.87,0,0.13,1\",circularIn:\"0.55,0,1,0.45\",circularOut:\"0,0.55,0.45,1\",circularInOut:\"0.85,0,0.15,1\"},V=\"transform-origin\";function U(t,e,r){var i=Object(y[\"m\"])({},t.shape);Object(y[\"m\"])(i,e),t.buildPath(r,i);var o=new p;return o.reset(Object(n[\"f\"])(t)),r.rebuildPath(o,1),o.generateStr(),o.getStr()}function $(t,e){var r=e.originX,n=e.originY;(r||n)&&(t[V]=r+\"px \"+n+\"px\")}var Z={fill:\"fill\",opacity:\"opacity\",lineWidth:\"stroke-width\",lineDashOffset:\"stroke-dashoffset\"};function G(t,e){var r=e.zrId+\"-ani-\"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function Q(t,e,r){var n,i,o=t.shape.paths,a={};if(Object(y[\"k\"])(o,(function(t){var e=z(r.zrId);e.animation=!0,J(t,{},e,!0);var o=e.cssAnims,s=e.cssNodes,u=Object(y[\"F\"])(o),c=u.length;if(c){i=u[c-1];var h=o[i];for(var l in h){var f=h[l];a[l]=a[l]||{d:\"\"},a[l].d+=f.d||\"\"}for(var d in s){var p=s[d].animation;p.indexOf(i)>=0&&(n=p)}}})),n){e.d=!1;var s=G(a,r);return n.replace(i,s)}}function K(t){return Object(y[\"C\"])(t)?X[t]?\"cubic-bezier(\"+X[t]+\")\":Object(q[\"a\"])(t)?t:\"\":\"\"}function J(t,e,r,i){var o=t.animators,a=o.length,s=[];if(t instanceof H[\"a\"]){var u=Q(t,e,r);if(u)s.push(u);else if(!a)return}else if(!a)return;for(var c={},h=0;h<a;h++){var l=o[h],f=[l.getMaxTime()/1e3+\"s\"],d=K(l.getClip().easing),p=l.getDelay();d?f.push(d):f.push(\"linear\"),p&&f.push(p/1e3+\"s\"),l.getLoop()&&f.push(\"infinite\");var v=f.join(\" \");c[v]=c[v]||[v,[]],c[v][1].push(l)}function g(o){var a,s=o[1],u=s.length,c={},h={},l={},f=\"animation-timing-function\";function d(t,e,r){for(var n=t.getTracks(),i=t.getMaxTime(),o=0;o<n.length;o++){var a=n[o];if(a.needsAnimate()){var s=a.keyframes,u=a.propName;if(r&&(u=r(u)),u)for(var c=0;c<s.length;c++){var h=s[c],l=Math.round(h.time/i*100)+\"%\",d=K(h.easing),p=h.rawValue;(Object(y[\"C\"])(p)||Object(y[\"z\"])(p))&&(e[l]=e[l]||{},e[l][u]=h.rawValue,d&&(e[l][f]=d))}}}}for(var p=0;p<u;p++){var v=s[p],g=v.targetName;g?\"shape\"===g&&d(v,h):!i&&d(v,c)}for(var b in c){var _={};Object(E[\"b\"])(_,t),Object(y[\"m\"])(_,c[b]);var m=Object(n[\"g\"])(_),x=c[b][f];l[b]=m?{transform:m}:{},$(l[b],_),x&&(l[b][f]=x)}var w=!0;for(var b in h){l[b]=l[b]||{};var O=!a;x=h[b][f];O&&(a=new N[\"a\"]);var k=a.len();a.reset(),l[b].d=U(t,h[b],a);var T=a.len();if(!O&&k!==T){w=!1;break}x&&(l[b][f]=x)}if(!w)for(var b in l)delete l[b].d;if(!i)for(p=0;p<u;p++){v=s[p],g=v.targetName;\"style\"===g&&d(v,l,(function(t){return Z[t]}))}var S,j=Object(y[\"F\"])(l),C=!0;for(p=1;p<j.length;p++){var A=j[p-1],P=j[p];if(l[A][V]!==l[P][V]){C=!1;break}S=l[A][V]}if(C&&S){for(var b in l)l[b][V]&&delete l[b][V];e[V]=S}if(Object(y[\"n\"])(j,(function(t){return Object(y[\"F\"])(l[t]).length>0})).length){var M=G(l,r);return M+\" \"+o[0]+\" both\"}}for(var b in c){u=g(c[b]);u&&s.push(u)}if(s.length){var _=r.zrId+\"-cls-\"+Y();r.cssNodes[\".\"+_]={animation:s.join(\",\")},e[\"class\"]=_}}var tt=r(\"76a5\"),et=r(\"726e\"),rt=r(\"41ef\");function nt(t,e,r){if(!t.ignore)if(t.isSilent()){var n={\"pointer-events\":\"none\"};it(n,e,r,!0)}else{var i=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},o=i.fill;if(!o){var a=t.style&&t.style.fill,s=t.states.select&&t.states.select.style&&t.states.select.style.fill,u=t.currentStates.indexOf(\"select\")>=0&&s||a;u&&(o=Object(rt[\"liftColor\"])(u))}var c=i.lineWidth;if(c){var h=!i.strokeNoScale&&t.transform?t.transform[0]:1;c/=h}n={cursor:\"pointer\"};o&&(n.fill=o),i.stroke&&(n.stroke=i.stroke),c&&(n[\"stroke-width\"]=c),it(n,e,r,!0)}}function it(t,e,r,n){var i=JSON.stringify(t),o=r.cssStyleCache[i];o||(o=r.zrId+\"-cls-\"+Y(),r.cssStyleCache[i]=o,r.cssNodes[\".\"+o+(n?\":hover\":\"\")]=t),e[\"class\"]=e[\"class\"]?e[\"class\"]+\" \"+o:o}var ot=r(\"697e\"),at=Math.round;function st(t){return t&&Object(y[\"C\"])(t.src)}function ut(t){return t&&Object(y[\"w\"])(t.toDataURL)}function ct(t,e,r,i){O((function(o,a){var s=\"fill\"===o||\"stroke\"===o;s&&Object(n[\"k\"])(a)?kt(e,t,o,i):s&&Object(n[\"n\"])(a)?Tt(r,t,o,i):t[o]=a,s&&i.ssr&&\"none\"===a&&(t[\"pointer-events\"]=\"visible\")}),e,r,!1),Ot(r,t,i)}function ht(t,e){var r=Object(ot[\"getElementSSRData\"])(e);r&&(r.each((function(e,r){null!=e&&(t[(A+r).toLowerCase()]=e+\"\")})),e.isSilent()&&(t[A+\"silent\"]=\"true\"))}function lt(t){return Object(n[\"j\"])(t[0]-1)&&Object(n[\"j\"])(t[1])&&Object(n[\"j\"])(t[2])&&Object(n[\"j\"])(t[3]-1)}function ft(t){return Object(n[\"j\"])(t[4])&&Object(n[\"j\"])(t[5])}function dt(t,e,r){if(e&&(!ft(e)||!lt(e))){var i=r?10:1e4;t.transform=lt(e)?\"translate(\"+at(e[4]*i)/i+\" \"+at(e[5]*i)/i+\")\":Object(n[\"e\"])(e)}}function pt(t,e,r){for(var n=t.points,i=[],o=0;o<n.length;o++)i.push(at(n[o][0]*r)/r),i.push(at(n[o][1]*r)/r);e.points=i.join(\" \")}function vt(t){return!t.smooth}function yt(t){var e=Object(y[\"H\"])(t,(function(t){return\"string\"===typeof t?[t,t]:t}));return function(t,r,n){for(var i=0;i<e.length;i++){var o=e[i],a=t[o[0]];null!=a&&(r[o[1]]=at(a*n)/n)}}}var gt={circle:[yt([\"cx\",\"cy\",\"r\"])],polyline:[pt,vt],polygon:[pt,vt]};function bt(t){for(var e=t.animators,r=0;r<e.length;r++)if(\"shape\"===e[r].targetName)return!0;return!1}function _t(t,e){var r=t.style,i=t.shape,o=gt[t.type],a={},s=e.animation,u=\"path\",c=t.style.strokePercent,h=e.compress&&Object(n[\"f\"])(t)||4;if(!o||e.willUpdate||o[1]&&!o[1](i)||s&&bt(t)||c<1){var l=!t.path||t.shapeChanged();t.path||t.createPathProxy();var f=t.path;l&&(f.beginPath(),t.buildPath(f,t.shape),t.pathUpdated());var d=f.getVersion(),v=t,y=v.__svgPathBuilder;v.__svgPathVersion===d&&y&&c===v.__svgPathStrokePercent||(y||(y=v.__svgPathBuilder=new p),y.reset(h),f.rebuildPath(y,c),y.generateStr(),v.__svgPathVersion=d,v.__svgPathStrokePercent=c),a.d=y.getStr()}else{u=t.type;var g=Math.pow(10,h);o[0](i,a,g)}return dt(a,t.transform),ct(a,r,t,e),ht(a,t),e.animation&&J(t,a,e),e.emphasis&&nt(t,a,e),M(u,t.id+\"\",a)}function mt(t,e){var r=t.style,n=r.image;if(n&&!Object(y[\"C\"])(n)&&(st(n)?n=n.src:ut(n)&&(n=n.toDataURL())),n){var i=r.x||0,o=r.y||0,a=r.width,s=r.height,u={href:n,width:a,height:s};return i&&(u.x=i),o&&(u.y=o),dt(u,t.transform),ct(u,r,t,e),ht(u,t),e.animation&&J(t,u,e),M(\"image\",t.id+\"\",u)}}function xt(t,e){var r=t.style,i=r.text;if(null!=i&&(i+=\"\"),i&&!isNaN(r.x)&&!isNaN(r.y)){var o=r.font||et[\"a\"],s=r.x||0,u=Object(n[\"b\"])(r.y||0,Object(a[\"e\"])(o),r.textBaseline),c=n[\"a\"][r.textAlign]||r.textAlign,h={\"dominant-baseline\":\"central\",\"text-anchor\":c};if(Object(tt[\"b\"])(r)){var l=\"\",f=r.fontStyle,d=Object(tt[\"c\"])(r.fontSize);if(!parseFloat(d))return;var p=r.fontFamily||et[\"b\"],v=r.fontWeight;l+=\"font-size:\"+d+\";font-family:\"+p+\";\",f&&\"normal\"!==f&&(l+=\"font-style:\"+f+\";\"),v&&\"normal\"!==v&&(l+=\"font-weight:\"+v+\";\"),h.style=l}else h.style=\"font: \"+o;return i.match(/\\s/)&&(h[\"xml:space\"]=\"preserve\"),s&&(h.x=s),u&&(h.y=u),dt(h,t.transform),ct(h,r,t,e),ht(h,t),e.animation&&J(t,h,e),M(\"text\",t.id+\"\",h,void 0,i)}}function wt(t,e){return t instanceof i[\"b\"]?_t(t,e):t instanceof o[\"a\"]?mt(t,e):t instanceof s[\"a\"]?xt(t,e):void 0}function Ot(t,e,r){var i=t.style;if(Object(n[\"i\"])(i)){var o=Object(n[\"h\"])(t),a=r.shadowCache,s=a[o];if(!s){var u=t.getGlobalScale(),c=u[0],h=u[1];if(!c||!h)return;var l=i.shadowOffsetX||0,f=i.shadowOffsetY||0,d=i.shadowBlur,p=Object(n[\"p\"])(i.shadowColor),v=p.opacity,y=p.color,g=d/2/c,b=d/2/h,_=g+\" \"+b;s=r.zrId+\"-s\"+r.shadowIdx++,r.defs[s]=M(\"filter\",s,{id:s,x:\"-100%\",y:\"-100%\",width:\"300%\",height:\"300%\"},[M(\"feDropShadow\",\"\",{dx:l/c,dy:f/h,stdDeviation:_,\"flood-color\":y,\"flood-opacity\":v})]),a[o]=s}e.filter=Object(n[\"d\"])(s)}}function kt(t,e,r,i){var o,a=t[r],s={gradientUnits:a.global?\"userSpaceOnUse\":\"objectBoundingBox\"};if(Object(n[\"m\"])(a))o=\"linearGradient\",s.x1=a.x,s.y1=a.y,s.x2=a.x2,s.y2=a.y2;else{if(!Object(n[\"o\"])(a))return void 0;o=\"radialGradient\",s.cx=Object(y[\"P\"])(a.x,.5),s.cy=Object(y[\"P\"])(a.y,.5),s.r=Object(y[\"P\"])(a.r,.5)}for(var u=a.colorStops,c=[],h=0,l=u.length;h<l;++h){var f=100*Object(n[\"q\"])(u[h].offset)+\"%\",d=u[h].color,p=Object(n[\"p\"])(d),v=p.color,g=p.opacity,b={offset:f};b[\"stop-color\"]=v,g<1&&(b[\"stop-opacity\"]=g),c.push(M(\"stop\",h+\"\",b))}var _=M(o,\"\",s,c),m=D(_),x=i.gradientCache,w=x[m];w||(w=i.zrId+\"-g\"+i.gradientIdx++,x[m]=w,s.id=w,i.defs[w]=M(o,w,s,c)),e[r]=Object(n[\"d\"])(w)}function Tt(t,e,r,i){var o,a=t.style[r],s=t.getBoundingRect(),u={},c=a.repeat,h=\"no-repeat\"===c,l=\"repeat-x\"===c,f=\"repeat-y\"===c;if(Object(n[\"l\"])(a)){var d=a.imageWidth,p=a.imageHeight,v=void 0,g=a.image;if(Object(y[\"C\"])(g)?v=g:st(g)?v=g.src:ut(g)&&(v=g.toDataURL()),\"undefined\"===typeof Image){var b=\"Image width/height must been given explictly in svg-ssr renderer.\";Object(y[\"b\"])(d,b),Object(y[\"b\"])(p,b)}else if(null==d||null==p){var _=function(t,e){if(t){var r=t.elm,n=d||e.width,i=p||e.height;\"pattern\"===t.tag&&(l?(i=1,n/=s.width):f&&(n=1,i/=s.height)),t.attrs.width=n,t.attrs.height=i,r&&(r.setAttribute(\"width\",n),r.setAttribute(\"height\",i))}},m=Object(B[\"a\"])(v,null,t,(function(t){h||_(k,t),_(o,t)}));m&&m.width&&m.height&&(d=d||m.width,p=p||m.height)}o=M(\"image\",\"img\",{href:v,width:d,height:p}),u.width=d,u.height=p}else a.svgElement&&(o=Object(y[\"d\"])(a.svgElement),u.width=a.svgWidth,u.height=a.svgHeight);if(o){var x,w;h?x=w=1:l?(w=1,x=u.width/s.width):f?(x=1,w=u.height/s.height):u.patternUnits=\"userSpaceOnUse\",null==x||isNaN(x)||(u.width=x),null==w||isNaN(w)||(u.height=w);var O=Object(n[\"g\"])(a);O&&(u.patternTransform=O);var k=M(\"pattern\",\"\",u,[o]),T=D(k),S=i.patternCache,j=S[T];j||(j=i.zrId+\"-p\"+i.patternIdx++,S[T]=j,u.id=j,k=i.defs[j]=M(\"pattern\",j,u,[o])),e[r]=Object(n[\"d\"])(j)}}function St(t,e,r){var i=r.clipPathCache,o=r.defs,a=i[t.id];if(!a){a=r.zrId+\"-c\"+r.clipPathIdx++;var s={id:a};i[t.id]=a,o[a]=M(\"clipPath\",a,s,[_t(t,r)])}e[\"clip-path\"]=Object(n[\"d\"])(a)}function jt(t){return document.createTextNode(t)}function Ct(t,e,r){t.insertBefore(e,r)}function At(t,e){t.removeChild(e)}function Pt(t,e){t.appendChild(e)}function Mt(t){return t.parentNode}function Lt(t){return t.nextSibling}function It(t,e){t.textContent=e}var Dt=58,Ft=120,zt=M(\"\",\"\");function Rt(t){return void 0===t}function Bt(t){return void 0!==t}function Et(t,e,r){for(var n={},i=e;i<=r;++i){var o=t[i].key;void 0!==o&&(n[o]=i)}return n}function Nt(t,e){var r=t.key===e.key,n=t.tag===e.tag;return n&&r}function Ht(t){var e,r=t.children,n=t.tag;if(Bt(n)){var i=t.elm=P(n);if(Yt(zt,t),Object(y[\"t\"])(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&Pt(i,Ht(o))}else Bt(t.text)&&!Object(y[\"A\"])(t.text)&&Pt(i,jt(t.text))}else t.elm=jt(t.text);return t.elm}function qt(t,e,r,n,i){for(;n<=i;++n){var o=r[n];null!=o&&Ct(t,Ht(o),e)}}function Wt(t,e,r,n){for(;r<=n;++r){var i=e[r];if(null!=i)if(Bt(i.tag)){var o=Mt(i.elm);At(o,i.elm)}else At(t,i.elm)}}function Yt(t,e){var r,n=e.elm,i=t&&t.attrs||{},o=e.attrs||{};if(i!==o){for(r in o){var a=o[r],s=i[r];s!==a&&(!0===a?n.setAttribute(r,\"\"):!1===a?n.removeAttribute(r):\"style\"===r?n.style.cssText=a:r.charCodeAt(0)!==Ft?n.setAttribute(r,a):\"xmlns:xlink\"===r||\"xmlns\"===r?n.setAttributeNS(j,r,a):r.charCodeAt(3)===Dt?n.setAttributeNS(C,r,a):r.charCodeAt(5)===Dt?n.setAttributeNS(S,r,a):n.setAttribute(r,a))}for(r in i)r in o||n.removeAttribute(r)}}function Xt(t,e,r){var n,i,o,a,s=0,u=0,c=e.length-1,h=e[0],l=e[c],f=r.length-1,d=r[0],p=r[f];while(s<=c&&u<=f)null==h?h=e[++s]:null==l?l=e[--c]:null==d?d=r[++u]:null==p?p=r[--f]:Nt(h,d)?(Vt(h,d),h=e[++s],d=r[++u]):Nt(l,p)?(Vt(l,p),l=e[--c],p=r[--f]):Nt(h,p)?(Vt(h,p),Ct(t,h.elm,Lt(l.elm)),h=e[++s],p=r[--f]):Nt(l,d)?(Vt(l,d),Ct(t,l.elm,h.elm),l=e[--c],d=r[++u]):(Rt(n)&&(n=Et(e,s,c)),i=n[d.key],Rt(i)?Ct(t,Ht(d),h.elm):(o=e[i],o.tag!==d.tag?Ct(t,Ht(d),h.elm):(Vt(o,d),e[i]=void 0,Ct(t,o.elm,h.elm))),d=r[++u]);(s<=c||u<=f)&&(s>c?(a=null==r[f+1]?null:r[f+1].elm,qt(t,a,r,u,f)):Wt(t,e,s,c))}function Vt(t,e){var r=e.elm=t.elm,n=t.children,i=e.children;t!==e&&(Yt(t,e),Rt(e.text)?Bt(n)&&Bt(i)?n!==i&&Xt(r,n,i):Bt(i)?(Bt(t.text)&&It(r,\"\"),qt(r,null,i,0,i.length-1)):Bt(n)?Wt(r,n,0,n.length-1):Bt(t.text)&&It(r,\"\"):t.text!==e.text&&(Bt(n)&&Wt(r,n,0,n.length-1),It(r,e.text)))}function Ut(t,e){if(Nt(t,e))Vt(t,e);else{var r=t.elm,n=Mt(r);Ht(e),null!==n&&(Ct(n,e.elm,Lt(r)),Wt(n,[t],0,0))}return e}var $t=r(\"3437\"),Zt=0,Gt=function(){function t(t,e,r){if(this.type=\"svg\",this.refreshHover=Qt(\"refreshHover\"),this.configLayer=Qt(\"configLayer\"),this.storage=e,this._opts=r=Object(y[\"m\"])({},r),this.root=t,this._id=\"zr\"+Zt++,this._oldVNode=R(r.width,r.height),t&&!r.ssr){var n=this._viewport=document.createElement(\"div\");n.style.cssText=\"position:relative;overflow:hidden\";var i=this._svgDom=this._oldVNode.elm=P(\"svg\");Yt(null,this._oldVNode),n.appendChild(i),t.appendChild(n)}this.resize(r.width,r.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style=\"position:absolute;left:0;top:0;user-select:none\",Ut(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return wt(t,z(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,n=this._height,i=z(this._id);i.animation=t.animation,i.willUpdate=t.willUpdate,i.compress=t.compress,i.emphasis=t.emphasis,i.ssr=this._opts.ssr;var o=[],a=this._bgVNode=Kt(r,n,this._backgroundColor,i);a&&o.push(a);var s=t.compress?null:this._mainVNode=M(\"g\",\"main\",{},[]);this._paintList(e,i,s?s.children:o),s&&o.push(s);var u=Object(y[\"H\"])(Object(y[\"F\"])(i.defs),(function(t){return i.defs[t]}));if(u.length&&o.push(M(\"defs\",\"defs\",{},u)),t.animation){var c=F(i.cssNodes,i.cssAnims,{newline:!0});if(c){var h=M(\"style\",\"stl\",{},[],c);o.push(h)}}return R(r,n,o,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},D(this.renderToVNode({animation:Object(y[\"P\"])(t.cssAnimation,!0),emphasis:Object(y[\"P\"])(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:Object(y[\"P\"])(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,r){for(var n,i,o=t.length,a=[],s=0,u=0,c=0;c<o;c++){var h=t[c];if(!h.invisible){var l=h.__clipPaths,f=l&&l.length||0,d=i&&i.length||0,p=void 0;for(p=Math.max(f-1,d-1);p>=0;p--)if(l&&i&&l[p]===i[p])break;for(var v=d-1;v>p;v--)s--,n=a[s-1];for(var y=p+1;y<f;y++){var g={};St(l[y],g,e);var b=M(\"g\",\"clip-g-\"+u++,g,[]);(n?n.children:r).push(b),a[s++]=b,n=b}i=l;var _=wt(h,e);_&&(n?n.children:r).push(_)}}},t.prototype.resize=function(t,e){var r=this._opts,i=this.root,o=this._viewport;if(null!=t&&(r.width=t),null!=e&&(r.height=e),i&&o&&(o.style.display=\"none\",t=Object($t[\"b\"])(i,0,r),e=Object($t[\"b\"])(i,1,r),o.style.display=\"\"),this._width!==t||this._height!==e){if(this._width=t,this._height=e,o){var a=o.style;a.width=t+\"px\",a.height=e+\"px\"}if(Object(n[\"n\"])(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute(\"width\",t),s.setAttribute(\"height\",e));var u=this._bgVNode&&this._bgVNode.elm;u&&(u.setAttribute(\"width\",t),u.setAttribute(\"height\",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=\"\"),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),r=\"data:image/svg+xml;\";return t?(e=Object(n[\"c\"])(e),e&&r+\"base64,\"+e):r+\"charset=UTF-8,\"+encodeURIComponent(e)},t}();function Qt(t){return function(){0}}function Kt(t,e,r,i){var o;if(r&&\"none\"!==r)if(o=M(\"rect\",\"bg\",{width:t,height:e,x:\"0\",y:\"0\"}),Object(n[\"k\"])(r))kt({fill:r},o.attrs,\"fill\",i);else if(Object(n[\"n\"])(r))Tt({style:{fill:r},dirty:y[\"L\"],getBoundingRect:function(){return{width:t,height:e}}},o.attrs,\"fill\",i);else{var a=Object(n[\"p\"])(r),s=a.color,u=a.opacity;o.attrs.fill=s,u<1&&(o.attrs[\"fill-opacity\"]=u)}return o}e[\"a\"]=Gt},dce8:function(t,e,r){\"use strict\";var n=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,r){t.x=e,t.y=r},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},t.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},t.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},t.scaleAndAdd=function(t,e,r,n){t.x=e.x+r.x*n,t.y=e.y+r.y*n},t.lerp=function(t,e,r,n){var i=1-n;t.x=i*e.x+n*r.x,t.y=i*e.y+n*r.y},t}();e[\"a\"]=n},dd4f:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"19eb\"),o=r(\"e86a\"),a=r(\"cbe5\"),s=r(\"6d8b\"),u=r(\"726e\"),c=Object(s[\"i\"])({strokeFirst:!0,font:u[\"a\"],x:0,y:0,textAlign:\"left\",textBaseline:\"top\",miterLimit:2},a[\"a\"]),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(n[\"a\"])(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&\"none\"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&\"none\"!==e},e.prototype.createStyle=function(t){return Object(s[\"g\"])(c,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+=\"\":e=\"\";var r=Object(o[\"d\"])(e,t.font,t.textAlign,t.textBaseline);if(r.x+=t.x||0,r.y+=t.y||0,this.hasStroke()){var n=t.lineWidth;r.x-=n/2,r.y-=n/2,r.width+=n,r.height+=n}this._rect=r}return this._rect},e.initDefaultProps=function(){var t=e.prototype;t.dirtyRectTolerance=10}(),e}(i[\"c\"]);h.prototype.type=\"tspan\",e[\"a\"]=h},dded:function(t,e,r){\"use strict\";var n=r(\"21a1\"),i=r(\"42e5\"),o=function(t){function e(e,r,n,i,o){var a=t.call(this,i)||this;return a.x=null==e?.5:e,a.y=null==r?.5:r,a.r=null==n?.5:n,a.type=\"radial\",a.global=o||!1,a}return Object(n[\"a\"])(e,t),e}(i[\"a\"]);e[\"a\"]=o},e263:function(t,e,r){\"use strict\";r.d(e,\"d\",(function(){return d})),r.d(e,\"c\",(function(){return p})),r.d(e,\"b\",(function(){return g})),r.d(e,\"e\",(function(){return b})),r.d(e,\"a\",(function(){return _}));var n=r(\"401b\"),i=r(\"4a3f\"),o=Math.min,a=Math.max,s=Math.sin,u=Math.cos,c=2*Math.PI,h=n[\"e\"](),l=n[\"e\"](),f=n[\"e\"]();function d(t,e,r){if(0!==t.length){for(var n=t[0],i=n[0],s=n[0],u=n[1],c=n[1],h=1;h<t.length;h++)n=t[h],i=o(i,n[0]),s=a(s,n[0]),u=o(u,n[1]),c=a(c,n[1]);e[0]=i,e[1]=u,r[0]=s,r[1]=c}}function p(t,e,r,n,i,s){i[0]=o(t,r),i[1]=o(e,n),s[0]=a(t,r),s[1]=a(e,n)}var v=[],y=[];function g(t,e,r,n,s,u,c,h,l,f){var d=i[\"c\"],p=i[\"a\"],g=d(t,r,s,c,v);l[0]=1/0,l[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var b=0;b<g;b++){var _=p(t,r,s,c,v[b]);l[0]=o(_,l[0]),f[0]=a(_,f[0])}g=d(e,n,u,h,y);for(b=0;b<g;b++){var m=p(e,n,u,h,y[b]);l[1]=o(m,l[1]),f[1]=a(m,f[1])}l[0]=o(t,l[0]),f[0]=a(t,f[0]),l[0]=o(c,l[0]),f[0]=a(c,f[0]),l[1]=o(e,l[1]),f[1]=a(e,f[1]),l[1]=o(h,l[1]),f[1]=a(h,f[1])}function b(t,e,r,n,s,u,c,h){var l=i[\"j\"],f=i[\"h\"],d=a(o(l(t,r,s),1),0),p=a(o(l(e,n,u),1),0),v=f(t,r,s,d),y=f(e,n,u,p);c[0]=o(t,s,v),c[1]=o(e,u,y),h[0]=a(t,s,v),h[1]=a(e,u,y)}function _(t,e,r,i,o,a,d,p,v){var y=n[\"l\"],g=n[\"k\"],b=Math.abs(o-a);if(b%c<1e-4&&b>1e-4)return p[0]=t-r,p[1]=e-i,v[0]=t+r,void(v[1]=e+i);if(h[0]=u(o)*r+t,h[1]=s(o)*i+e,l[0]=u(a)*r+t,l[1]=s(a)*i+e,y(p,h,l),g(v,h,l),o%=c,o<0&&(o+=c),a%=c,a<0&&(a+=c),o>a&&!d?a+=c:o<a&&d&&(o+=c),d){var _=a;a=o,o=_}for(var m=0;m<a;m+=Math.PI/2)m>o&&(f[0]=u(m)*r+t,f[1]=s(m)*i+e,y(p,f,p),g(v,f,v))}},e86a:function(t,e,r){\"use strict\";r.d(e,\"f\",(function(){return s})),r.d(e,\"d\",(function(){return c})),r.d(e,\"a\",(function(){return h})),r.d(e,\"b\",(function(){return l})),r.d(e,\"e\",(function(){return f})),r.d(e,\"g\",(function(){return d})),r.d(e,\"c\",(function(){return p}));var n=r(\"9850\"),i=r(\"d51b\"),o=r(\"726e\"),a={};function s(t,e){e=e||o[\"a\"];var r=a[e];r||(r=a[e]=new i[\"a\"](500));var n=r.get(t);return null==n&&(n=o[\"d\"].measureText(t,e).width,r.put(t,n)),n}function u(t,e,r,i){var o=s(t,e),a=f(e),u=h(0,o,r),c=l(0,a,i),d=new n[\"a\"](u,c,o,a);return d}function c(t,e,r,i){var o=((t||\"\")+\"\").split(\"\\n\"),a=o.length;if(1===a)return u(o[0],e,r,i);for(var s=new n[\"a\"](0,0,0,0),c=0;c<o.length;c++){var h=u(o[c],e,r,i);0===c?s.copy(h):s.union(h)}return s}function h(t,e,r){return\"right\"===r?t-=e:\"center\"===r&&(t-=e/2),t}function l(t,e,r){return\"middle\"===r?t-=e/2:\"bottom\"===r&&(t-=e),t}function f(t){return s(\"国\",t)}function d(t,e){return\"string\"===typeof t?t.lastIndexOf(\"%\")>=0?parseFloat(t)/100*e:parseFloat(t):t}function p(t,e,r){var n=e.position||\"inside\",i=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,u=r.x,c=r.y,h=\"left\",l=\"top\";if(n instanceof Array)u+=d(n[0],r.width),c+=d(n[1],r.height),h=null,l=null;else switch(n){case\"left\":u-=i,c+=s,h=\"right\",l=\"middle\";break;case\"right\":u+=i+a,c+=s,l=\"middle\";break;case\"top\":u+=a/2,c-=i,h=\"center\",l=\"bottom\";break;case\"bottom\":u+=a/2,c+=o+i,h=\"center\";break;case\"inside\":u+=a/2,c+=s,h=\"center\",l=\"middle\";break;case\"insideLeft\":u+=i,c+=s,l=\"middle\";break;case\"insideRight\":u+=a-i,c+=s,h=\"right\",l=\"middle\";break;case\"insideTop\":u+=a/2,c+=i,h=\"center\";break;case\"insideBottom\":u+=a/2,c+=o-i,h=\"center\",l=\"bottom\";break;case\"insideTopLeft\":u+=i,c+=i;break;case\"insideTopRight\":u+=a-i,c+=i,h=\"right\";break;case\"insideBottomLeft\":u+=i,c+=o-i,l=\"bottom\";break;case\"insideBottomRight\":u+=a-i,c+=o-i,h=\"right\",l=\"bottom\";break}return t=t||{},t.x=u,t.y=c,t.align=h,t.verticalAlign=l,t}},e91f:function(t,e,r){\"use strict\";var n=r(\"ebb5\"),i=r(\"4d64\").indexOf,o=n.aTypedArray,a=n.exportTypedArrayMethod;a(\"indexOf\",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},ebb5:function(t,e,r){\"use strict\";var n,i=r(\"a981\"),o=r(\"83ab\"),a=r(\"da84\"),s=r(\"861d\"),u=r(\"5135\"),c=r(\"f5df\"),h=r(\"9112\"),l=r(\"6eeb\"),f=r(\"9bf2\").f,d=r(\"e163\"),p=r(\"d2bb\"),v=r(\"b622\"),y=r(\"90e3\"),g=a.Int8Array,b=g&&g.prototype,_=a.Uint8ClampedArray,m=_&&_.prototype,x=g&&d(g),w=b&&d(b),O=Object.prototype,k=O.isPrototypeOf,T=v(\"toStringTag\"),S=y(\"TYPED_ARRAY_TAG\"),j=i&&!!p&&\"Opera\"!==c(a.opera),C=!1,A={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},P=function(t){var e=c(t);return\"DataView\"===e||u(A,e)},M=function(t){return s(t)&&u(A,c(t))},L=function(t){if(M(t))return t;throw TypeError(\"Target is not a typed array\")},I=function(t){if(p){if(k.call(x,t))return t}else for(var e in A)if(u(A,n)){var r=a[e];if(r&&(t===r||k.call(r,t)))return t}throw TypeError(\"Target is not a typed array constructor\")},D=function(t,e,r){if(o){if(r)for(var n in A){var i=a[n];i&&u(i.prototype,t)&&delete i.prototype[t]}w[t]&&!r||l(w,t,r?e:j&&b[t]||e)}},F=function(t,e,r){var n,i;if(o){if(p){if(r)for(n in A)i=a[n],i&&u(i,t)&&delete i[t];if(x[t]&&!r)return;try{return l(x,t,r?e:j&&g[t]||e)}catch(s){}}for(n in A)i=a[n],!i||i[t]&&!r||l(i,t,e)}};for(n in A)a[n]||(j=!1);if((!j||\"function\"!=typeof x||x===Function.prototype)&&(x=function(){throw TypeError(\"Incorrect invocation\")},j))for(n in A)a[n]&&p(a[n],x);if((!j||!w||w===O)&&(w=x.prototype,j))for(n in A)a[n]&&p(a[n].prototype,w);if(j&&d(m)!==w&&p(m,w),o&&!u(w,T))for(n in C=!0,f(w,T,{get:function(){return s(this)?this[S]:void 0}}),A)a[n]&&h(a[n],S,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:j,TYPED_ARRAY_TAG:C&&S,aTypedArray:L,aTypedArrayConstructor:I,exportTypedArrayMethod:D,exportTypedArrayStaticMethod:F,isView:P,isTypedArray:M,TypedArray:x,TypedArrayPrototype:w}},f8cd:function(t,e,r){var n=r(\"a691\");t.exports=function(t){var e=n(t);if(e<0)throw RangeError(\"The argument can't be less than 0\");return e}},fa9d:function(t,e,r){\"use strict\";r.d(e,\"d\",(function(){return o})),r.d(e,\"b\",(function(){return a})),r.d(e,\"e\",(function(){return u})),r.d(e,\"c\",(function(){return c})),r.d(e,\"a\",(function(){return h}));r(\"a4d3\"),r(\"e01a\"),r(\"caad\"),r(\"fb6a\"),r(\"a9e3\"),r(\"9129\"),r(\"d3b7\"),r(\"25f0\");var n=r(\"d0ff\"),i=r(\"0122\"),o=\"undefined\"===typeof window;function a(t){return null!==t&&void 0!==t&&\"\"!==t}function s(t){return t.constructor===Object}function u(t){return\"string\"===typeof t||t.constructor===String}function c(t){return\"number\"===typeof t||t.constructor===Number}function h(t,e){var r=function(t){return Object.prototype.toString.call(t).slice(8,-1)};if(!s(t)&&!s(e))return!(!Number.isNaN(t)||!Number.isNaN(e))||t===e;if(!s(t)||!s(e))return!1;if(r(t)!==r(e))return!1;if(t===e)return!0;if([\"Array\"].includes(r(t)))return f(t,e);if([\"Object\"].includes(r(t)))return l(t,e);if([\"Map\",\"Set\"].includes(r(t))){var i=Object(n[\"a\"])(t),o=Object(n[\"a\"])(e);return h(i,o)}return!1}function l(t,e){for(var r in t){if(t.hasOwnProperty(r)!==e.hasOwnProperty(r))return!1;if(Object(i[\"a\"])(t[r])!==Object(i[\"a\"])(e[r]))return!1}for(var n in e){if(t.hasOwnProperty(n)!==e.hasOwnProperty(n))return!1;if(Object(i[\"a\"])(t[n])!==Object(i[\"a\"])(e[n]))return!1;if(t.hasOwnProperty(n))if(t[n]instanceof Array&&e[n]instanceof Array){if(!f(t[n],e[n]))return!1}else if(t[n]instanceof Object&&e[n]instanceof Object){if(!l(t[n],e[n]))return!1}else if(t[n]!==e[n])return!1}return!0}function f(t,e){if(!t||!e)return!1;if(t.length!==e.length)return!1;for(var r=0,n=t.length;r<n;r++)if(t[r]instanceof Array&&e[r]instanceof Array){if(!f(t[r],e[r]))return!1}else if(t[r]instanceof Object&&e[r]instanceof Object){if(!l(t[r],e[r]))return!1}else if(t[r]!==e[r])return!1;return!0}}}]);", "extractedComments": []}