import request from '@util/requestForPy'

// 获取设备列表
export async function getData(params) {
  return request({
    url: '/audit/getData',
    method: 'post',
    data: params,
  })
}

// 获取用户列表
export async function getUser(params) {
  return request({
    url: '/audit/getUser',
    method: 'post',
    data: params,
  })
}

// 添加用户
export async function addUser(params) {
  return request({
    url: '/audit/addUser',
    method: 'post',
    data: params,
  })
}

// 修改用户
export async function modifyUser(params) {
  return request({
    url: '/audit/modifyUser',
    method: 'post',
    data: params,
  })
}

// 删除用户
export async function deleteUser(params) {
  return request({
    url: '/audit/deleteUser',
    method: 'post',
    data: params,
  })
}

// 登录日志
export async function loginForLog(params) {
  return request({
    url: '/audit/loginForLog',
    method: 'post',
    data: params,
  })
}

// 设备Ping
export async function devicePing(params) {
  return request({
    url: '/audit/devicePing',
    method: 'post',
    data: params,
  })
}

// 删除设备
export async function DeleteData(params) {
  return request({
    url: '/audit/DeleteData',
    method: 'post',
    data: params,
  })
}

// 添加设备
export async function addEquipment(params) {
  return request({
    url: '/audit/addEquipment',
    method: 'post',
    data: params,
  })
}

// 编辑设备
export async function editEquipment(params) {
  return request({
    url: '/audit/editEquipment',
    method: 'post',
    data: params,
  })
}

// 获取拓扑数据
export async function getTopoData(params) {
  return request({
    url: '/audit/getTopoData',
    method: 'post',
    data: params,
  })
}

// 保存拓扑数据
export async function setTopoData(params) {
  return request({
    url: '/fireWall/setTopoData',
    method: 'post',
    data: params,
  })
}

// 搜索分组
export async function searchGroup(params) {
  return request({
    url: '/GroupManagement/searchGroup',
    method: 'post',
    data: params,
  })
}

// 清空数据
export async function clearData() {
  return Promise.resolve({ code: 0 })
}
