{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\Hostguardian\\HostGuardianManagement.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\Hostguardian\\HostGuardianManagement.vue", "mtime": 1744862169104}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QuZW50cmllcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5mcm9tLWVudHJpZXMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvciI7CmltcG9ydCAicmVnZW5lcmF0b3ItcnVudGltZS9ydW50aW1lIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6L3dvcmtzcGFjZS9zbXAvbm9kZV9tb2R1bGVzL0B2dWUvYmFiZWwtcHJlc2V0LWFwcC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vYXN5bmNUb0dlbmVyYXRvciI7CmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXkiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJEOi93b3Jrc3BhY2Uvc21wL25vZGVfbW9kdWxlcy9AdnVlL2JhYmVsLXByZXNldC1hcHAvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwppbXBvcnQgQWRkRGV2aWNlRGlhbG9nIGZyb20gJy4vQWRkRGV2aWNlRGlhbG9nLnZ1ZSc7CmltcG9ydCBDb25maWdEaWFsb2cgZnJvbSAnLi9Db25maWdEaWFsb2cudnVlJzsKaW1wb3J0IEVsVHJlZVNlbGVjdCBmcm9tICdAY29tcC9TZWxlY3RUcmVlL1NlbGVjdFRyZWUnOwppbXBvcnQgeyBnZXRTYWZldHlQcm9qZWN0TGlzdCwgYWRkRXF1aXBtZW50LCBlZGl0RXF1aXBtZW50LCBkZWxldGVQcm9qZWN0LCBkZXZpY2VQaW5nIH0gZnJvbSAnQC9hcGkvYXNzZXQvaG9zdC1ndWFyZGlhbi5qcyc7CmltcG9ydCB7IHNlYXJjaEdyb3VwTGlzdCB9IGZyb20gJ0AvYXBpL2Fzc2V0L2hvc3RndWFyZGlhbmdyb3VwJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdIb3N0R3VhcmRpYW5NYW5hZ2VtZW50JywKICBjb21wb25lbnRzOiB7CiAgICBBZGREZXZpY2VEaWFsb2c6IEFkZERldmljZURpYWxvZywKICAgIENvbmZpZ0RpYWxvZzogQ29uZmlnRGlhbG9nLAogICAgRWxUcmVlU2VsZWN0OiBFbFRyZWVTZWxlY3QKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcXVlcnlJbnB1dDogewogICAgICAgIG5hbWU6ICcnLAogICAgICAgIGdyb3VwX2lkOiAnJywKICAgICAgICAvLyDkv67mlLnkuLpncm91cF9pZAogICAgICAgIGlwOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH0sCiAgICAgIGdyb3VwX2lkT3B0aW9uczogW10sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIHNlbGVjdGVkUm93czogW10sCiAgICAgIHBhZ2luYXRpb246IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LAogICAgICAvLyDmlrDlop7orr7lpIflr7nor53moYbnm7jlhbPmlbDmja4KICAgICAgYWRkRGV2aWNlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOmFjee9ruWvueivneahhuebuOWFs+aVsOaNrgogICAgICBjb25maWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY29uZmlnRGlhbG9nVGl0bGU6ICforr7lpIfphY3nva4nLAogICAgICBjdXJyZW50RGV2aWNlOiB7fQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldFRhYmxlRGF0YSgpOwogICAgdGhpcy5sb2FkR3JvdXBPcHRpb25zKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRUYWJsZURhdGE6IGZ1bmN0aW9uIGdldFRhYmxlRGF0YSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CgogICAgICB2YXIgcGF5bG9hZCA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5xdWVyeUlucHV0KSwge30sIHsKICAgICAgICBwYWdlOiB0aGlzLnBhZ2luYXRpb24uY3VycmVudFBhZ2UsCiAgICAgICAgcGVyX3BhZ2U6IHRoaXMucGFnaW5hdGlvbi5wYWdlU2l6ZSwKICAgICAgICBjYXRlZ29yeTogMwogICAgICB9KTsgLy8g5Y675o6JcGF5bG9hZOS4reS4uuepuueahOWxnuaApwoKCiAgICAgIHBheWxvYWQgPSBPYmplY3QuZnJvbUVudHJpZXMoT2JqZWN0LmVudHJpZXMocGF5bG9hZCkuZmlsdGVyKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgdmFyIF9yZWYyID0gX3NsaWNlZFRvQXJyYXkoX3JlZiwgMiksCiAgICAgICAgICAgIF8gPSBfcmVmMlswXSwKICAgICAgICAgICAgdmFsdWUgPSBfcmVmMlsxXTsKCiAgICAgICAgcmV0dXJuIHZhbHVlICE9PSAnJzsKICAgICAgfSkpOwogICAgICBnZXRTYWZldHlQcm9qZWN0TGlzdChwYXlsb2FkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsKICAgICAgICAgIF90aGlzLnRhYmxlRGF0YSA9IHJlcy5kYXRhLml0ZW1zOwogICAgICAgICAgX3RoaXMucGFnaW5hdGlvbi50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKTsKCiAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCk7CiAgICB9LAogICAgaGFuZGxlUmVzZXQ6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0KCkgewogICAgICB0aGlzLnF1ZXJ5SW5wdXQgPSB7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgZ3JvdXBfaWQ6ICcnLAogICAgICAgIGlwOiAnJywKICAgICAgICBzdGF0dXM6ICcnCiAgICAgIH07CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICB0b2dnbGVTaG93OiBmdW5jdGlvbiB0b2dnbGVTaG93KCkgewogICAgICB0aGlzLmlzU2hvdyA9ICF0aGlzLmlzU2hvdzsKICAgIH0sCiAgICBoYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5hZGREZXZpY2VEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVBZGREZXZpY2VTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZUFkZERldmljZVN1Ym1pdChmb3JtRGF0YSkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAwOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gYWRkRXF1aXBtZW50KGZvcm1EYXRhKTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dC5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5zdWNjZXNzKCfmlrDlop7orr7lpIfmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgIF90aGlzMi5nZXRUYWJsZURhdGEoKTsgLy8g5Yi35paw6K6+5aSH5YiX6KGoCgoKICAgICAgICAgICAgICAgICAgX3RoaXMyLmFkZERldmljZURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmlrDlop7orr7lpIflpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDc7CiAgICAgICAgICAgICAgICBfY29udGV4dC50MCA9IF9jb250ZXh0WyJjYXRjaCJdKDApOwoKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign5paw5aKe6K6+5aSH5aSx6LSlJyk7CgogICAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZUNvbmZpZ1N1Ym1pdDogZnVuY3Rpb24gaGFuZGxlQ29uZmlnU3VibWl0KGZvcm1EYXRhKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHBheWxvYWQsIHJlc3BvbnNlOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDA7CiAgICAgICAgICAgICAgICBwYXlsb2FkID0gX29iamVjdFNwcmVhZCh7CiAgICAgICAgICAgICAgICAgIGRldmljZV9pZDogX3RoaXMzLmN1cnJlbnREZXZpY2UuaWQKICAgICAgICAgICAgICAgIH0sIGZvcm1EYXRhKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNDsKICAgICAgICAgICAgICAgIHJldHVybiBlZGl0RXF1aXBtZW50KHBheWxvYWQpOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0Mi5zZW50OwoKICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5zdWNjZXNzKCfkv67mlLnorr7lpIfmiJDlip8nKTsKCiAgICAgICAgICAgICAgICAgIF90aGlzMy5nZXRUYWJsZURhdGEoKTsgLy8g5Yi35paw6K6+5aSH5YiX6KGoCgoKICAgICAgICAgICAgICAgICAgX3RoaXMzLmNvbmZpZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfkv67mlLnorr7lpIflpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDExOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gODsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi50MCA9IF9jb250ZXh0MlsiY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IoJ+S/ruaUueiuvuWkh+Wksei0pScpOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDhdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZUJhdGNoRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVCYXRjaERlbGV0ZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliKDpmaTnmoTorr7lpIcnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oIlx1Nzg2RVx1NUI5QVx1ODk4MVx1NTIyMFx1OTY2NFx1OTAwOVx1NEUyRFx1NzY4NFx1OEJCRVx1NTkwN1x1NTQxN1x1RkYxRiIsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICB2YXIgaWRzVG9EZWxldGUgPSBfdGhpczQuc2VsZWN0ZWRSb3dzLm1hcChmdW5jdGlvbiAocm93KSB7CiAgICAgICAgICByZXR1cm4gcm93LmlkOwogICAgICAgIH0pOwoKICAgICAgICBQcm9taXNlLmFsbChpZHNUb0RlbGV0ZS5tYXAoZnVuY3Rpb24gKGlkKSB7CiAgICAgICAgICByZXR1cm4gZGVsZXRlUHJvamVjdCh7CiAgICAgICAgICAgIGRldmljZV9pZDogaWQKICAgICAgICAgIH0pOwogICAgICAgIH0pKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5zdWNjZXNzKCfmibnph4/liKDpmaTmiJDlip8nKTsKCiAgICAgICAgICBfdGhpczQudGFibGVEYXRhID0gX3RoaXM0LnRhYmxlRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuICFpZHNUb0RlbGV0ZS5pbmNsdWRlcyhpdGVtLmlkKTsKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXM0LnNlbGVjdGVkUm93cyA9IFtdOwogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5lcnJvcign5om56YeP5Yig6Zmk5aSx6LSlJyk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICBoYW5kbGVFZGl0OiBmdW5jdGlvbiBoYW5kbGVFZGl0KHJvdykgewogICAgICB0aGlzLmN1cnJlbnREZXZpY2UgPSBfb2JqZWN0U3ByZWFkKHt9LCByb3cpOwogICAgICB0aGlzLmNvbmZpZ0RpYWxvZ1RpdGxlID0gIlx1NEZFRVx1NjUzOVx1OTE0RFx1N0Y2RSAtICIuY29uY2F0KHJvdy5ub3Rlcyk7CiAgICAgIHRoaXMuY29uZmlnRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlVmlldzogZnVuY3Rpb24gaGFuZGxlVmlldyhyb3cpIHsKICAgICAgaWYgKHJvdy5zdGF0dXMgPT09IDEpIHsKICAgICAgICB3aW5kb3cub3BlbigiaHR0cHM6Ly8iLmNvbmNhdChyb3cuaXAsICI6MTI1Ny9hcGkvdjEvaW5kZXgjL2xvZ2luIikpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iuvuWkh+emu+e6v++8jOaXoOazleafpeeciycpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocmVjb3JkKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgdGhpcy4kY29uZmlybSgiXHU3ODZFXHU1QjlBXHU4OTgxXHU1MjIwXHU5NjY0XHU4QkJFXHU1OTA3ICIuY29uY2F0KHJlY29yZC5ub3RlcywgIiBcdTU0MTdcdUZGMUYiKSwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIGRlbGV0ZVByb2plY3QoewogICAgICAgICAgZGV2aWNlX2lkOiByZWNvcmQuaWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKCiAgICAgICAgICBfdGhpczUudGFibGVEYXRhID0gX3RoaXM1LnRhYmxlRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uaWQgIT09IHJlY29yZC5pZDsKICAgICAgICAgIH0pOwogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlJyk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICBoYW5kbGVQaW5nOiBmdW5jdGlvbiBoYW5kbGVQaW5nKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSAwOwogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAzOwogICAgICAgICAgICAgICAgcmV0dXJuIGRldmljZVBpbmcoewogICAgICAgICAgICAgICAgICBpcDogcm93LmlwCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDMuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuY29kZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSA9PT0gMCkgewogICAgICAgICAgICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5zdWNjZXNzKCdQaW5n5oiQ5YqfJyk7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM2LiRtZXNzYWdlLmVycm9yKCfnvZHnu5zkuI3pgJonKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ1BpbmflpLHotKUnKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gNzsKICAgICAgICAgICAgICAgIF9jb250ZXh0My50MCA9IF9jb250ZXh0M1siY2F0Y2giXSgwKTsKCiAgICAgICAgICAgICAgICBfdGhpczYuJG1lc3NhZ2UuZXJyb3IoJ1BpbmflpLHotKUnKTsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMywgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBzZWxlY3Rpb247CiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5wYWdlU2l6ZSA9IHNpemU7CiAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCk7CiAgICB9LAogICAgaGFuZGxlUGFnZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlUGFnZUNoYW5nZShwYWdlKSB7CiAgICAgIHRoaXMucGFnaW5hdGlvbi5jdXJyZW50UGFnZSA9IHBhZ2U7CiAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCk7CiAgICB9LAogICAgbG9hZEdyb3VwT3B0aW9uczogZnVuY3Rpb24gbG9hZEdyb3VwT3B0aW9ucygpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CgogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgcmVzcG9uc2U7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5wcmV2ID0gMDsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIHJldHVybiBzZWFyY2hHcm91cExpc3QoKTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDQuc2VudDsKCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM3Lmdyb3VwX2lkT3B0aW9ucyA9IHJlc3BvbnNlOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSA3OwogICAgICAgICAgICAgICAgX2NvbnRleHQ0LnQwID0gX2NvbnRleHQ0WyJjYXRjaCJdKDApOwoKICAgICAgICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcign5Yqg6L296K6+5aSH5YiG57uE5aSx6LSlJyk7CgogICAgICAgICAgICAgICAgX3RoaXM3Lmdyb3VwX2lkT3B0aW9ucyA9IFt7CiAgICAgICAgICAgICAgICAgIGlkOiAyMiwKICAgICAgICAgICAgICAgICAgZ3JvdXBOYW1lOiAn5LiA57qn57uE57uHJywKICAgICAgICAgICAgICAgICAgcGFyZW50SWQ6IC0xLAogICAgICAgICAgICAgICAgICBkZXNjOiBudWxsLAogICAgICAgICAgICAgICAgICBjaGlsZExpc3Q6IFt7CiAgICAgICAgICAgICAgICAgICAgaWQ6IDI2LAogICAgICAgICAgICAgICAgICAgIGdyb3VwTmFtZTogJ3Rlc3QnLAogICAgICAgICAgICAgICAgICAgIHBhcmVudElkOiAyMiwKICAgICAgICAgICAgICAgICAgICBkZXNjOiBudWxsLAogICAgICAgICAgICAgICAgICAgIGNoaWxkTGlzdDogW10KICAgICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICAgIH1dOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0LCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfQogIH0KfTs="}, null]}