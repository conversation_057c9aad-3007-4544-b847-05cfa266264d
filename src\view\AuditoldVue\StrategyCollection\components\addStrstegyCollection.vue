<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :title="title"
      size="800px"
      direction="rtl"
      @close="onDrawerClose"
    >
      <div style="padding: 20px;">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          v-loading="loading"
        >
          <!-- 隐藏字段 -->
          <el-form-item style="display: none;">
            <el-input v-model="form.id" />
          </el-form-item>
          <el-form-item style="display: none;">
            <el-input v-model="form.tacticsCode" />
          </el-form-item>
          <el-form-item style="display: none;">
            <el-input v-model="form.deviceIds" />
          </el-form-item>

          <!-- IP地址 -->
          <el-form-item label="IP地址" prop="collectIpList">
            <div class="multi-ip-container">
              <!-- IP标签显示 -->
              <div class="ip-tags">
                <el-tag
                  v-for="(ip, index) in collectIpList"
                  :key="index"
                  closable
                  @close="handleCloseTag(ip)"
                  style="margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ ip }}
                </el-tag>

                <!-- 输入框 -->
                <el-input
                  v-if="inputVisible && !allChecked"
                  ref="ipInput"
                  v-model="inputValue"
                  size="small"
                  style="width: 200px; margin-right: 8px;"
                  :placeholder="allChecked ? 'all' : '请输入ip地址'"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                />

                <!-- 添加按钮 -->
                <el-button
                  v-if="!allChecked"
                  size="small"
                  @click="showInput"
                  style="margin-right: 8px;"
                >
                  + 添加
                </el-button>

                <!-- all按钮 -->
                <el-button
                  v-if="allChecked"
                  size="small"
                  disabled
                  style="margin-right: 8px;"
                >
                  all
                </el-button>
              </div>

              <!-- 工具提示和全选 -->
              <div style="display: flex; align-items: center; margin-top: 10px;">
                <el-tooltip placement="top" effect="light">
                  <div slot="content">
                    示例：**********<br />
                    **********/24<br />
                    **********-100<br />
                    **********-************
                  </div>
                  <i class="el-icon-info" style="color: #909399; cursor: pointer; margin-right: 20px;"></i>
                </el-tooltip>

                <el-checkbox
                  v-model="allChecked"
                  @change="isAllIPChange"
                >
                  全选
                </el-checkbox>
              </div>
            </div>
          </el-form-item>

          <!-- 协议 -->
          <el-form-item label="协议" prop="protocolType">
            <el-radio-group v-model="form.protocolType" @change="protocolChange">
              <el-radio :label="0">全部协议</el-radio>
              <el-radio :label="1">指定协议</el-radio>
            </el-radio-group>
            <el-button
              v-if="form.protocolType === 1"
              type="text"
              style="margin-left: 20px; color: #1890ff;"
              @click="handleSelectProtocols"
            >
              已关联{{ protocolIds.length }}个
            </el-button>
          </el-form-item>
        </el-form>

        <div style="text-align: center; margin-top: 40px;">
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button style="margin-left: 20px;" @click="onDrawerClose">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 协议选择模态框 -->
    <protocol-select-modal
      ref="protocolSelectModalRef"
      type="checkbox"
      :default-protocol-ids="protocolIds"
      :default-protocol-names="protocolNames"
      @saveData="saveData"
    />
  </div>
</template>

<script>
import { tacticsAdd, tacticsUpdate } from '@api/auditold/strategyCollection'
import ProtocolSelectModal from './protocolSelectModal'

export default {
  name: 'AddStrategyCollection',
  components: {
    ProtocolSelectModal,
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      form: {
        id: '',
        tacticsCode: '',
        collectIpList: '',
        protocolType: 0,
        protocolIds: '',
        deviceIds: '',
      },
      collectIpList: [],
      protocolIds: [],
      protocolNames: [],
      inputVisible: false,
      inputValue: '',
      allChecked: false,
      rules: {},
    }
  },
  created() {
    // 在created钩子中设置验证规则
    this.rules = {
      collectIpList: [
        { required: true, message: '请输入IP地址', trigger: 'blur' },
        { validator: this.validatorIP, trigger: 'blur' },
      ],
      protocolType: [
        { required: true, message: '请选择协议类型', trigger: 'change' },
      ],
    }
  },
  methods: {
    showDrawer(record = {}) {
      this.visible = true

      if (record.id) {
        this.title = '编辑采集策略'
        setTimeout(() => {
          this.form = {
            id: record.id,
            tacticsCode: record.tacticsCode || '',
            collectIpList: record.collectIpList || '',
            protocolType: record.protocolType || 0,
            protocolIds: record.protocolIds || '',
            deviceIds: record.deviceIds || '',
          }

          // 处理IP列表
          if (record.collectIpList) {
            if (record.collectIpList === 'all') {
              this.allChecked = true
              this.collectIpList = []
            } else {
              this.collectIpList = record.collectIpList.split(',')
              this.allChecked = false
            }
          } else {
            this.collectIpList = []
            this.allChecked = false
          }

          // 设置协议数据
          if (record.protocolIds) {
            this.protocolIds = record.protocolIds.split(',').map(Number)
          } else {
            this.protocolIds = []
          }

          if (record.protocolNames) {
            this.protocolNames = record.protocolNames.split(',')
          } else {
            this.protocolNames = []
          }
        })
      } else {
        this.title = '新增采集策略'
        this.resetForm()
      }
    },

    resetForm() {
      this.form = {
        id: '',
        tacticsCode: '',
        collectIpList: '',
        protocolType: 0,
        protocolIds: '',
        deviceIds: '',
      }
      this.collectIpList = []
      this.protocolIds = []
      this.protocolNames = []
      this.inputVisible = false
      this.inputValue = ''
      this.allChecked = false
    },

    // IP地址验证器
    validatorIP(_, value, callback) {
      if (this.allChecked) {
        callback()
        return
      }

      if (this.collectIpList.length === 0) {
        callback(new Error('请输入IP地址'))
        return
      }

      // 验证IP格式
      const ipRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const cidrRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\/([0-9]|[1-2][0-9]|3[0-2])$/
      const rangeRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])-(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const fullRangeRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])-(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/

      for (const ip of this.collectIpList) {
        if (!ipRegex.test(ip) && !cidrRegex.test(ip) && !rangeRegex.test(ip) && !fullRangeRegex.test(ip)) {
          callback(new Error(`IP地址格式不正确: ${ip}`))
          return
        }
      }

      callback()
    },

    // IP地址全选改变
    isAllIPChange(checked) {
      this.allChecked = checked
      if (checked) {
        this.collectIpList = []
        this.$refs.form && this.$refs.form.clearValidate(['collectIpList'])
      }
    },

    // 显示IP输入框
    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.ipInput && this.$refs.ipInput.focus()
      })
    },

    // 确认输入IP
    handleInputConfirm() {
      if (this.inputValue && this.inputValue.trim()) {
        if (!this.collectIpList.includes(this.inputValue.trim())) {
          this.collectIpList.push(this.inputValue.trim())
        }
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    // 移除IP标签
    handleCloseTag(tag) {
      const index = this.collectIpList.indexOf(tag)
      if (index > -1) {
        this.collectIpList.splice(index, 1)
      }
    },

    // 协议类型改变
    protocolChange(value) {
      if (value === 0) {
        this.protocolNames = []
        this.protocolIds = []
      }
    },

    // 打开协议选择弹框
    handleSelectProtocols() {
      this.$refs.protocolSelectModalRef.showModal()
    },

    // 保存选中的协议数据
    saveData(obj) {
      this.protocolIds = obj.ids
      this.protocolNames = obj.names
    },

    onDrawerClose() {
      this.visible = false
      this.loading = false
      this.resetForm()
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    // 提交表单
    handleSubmit() {
      // 检查协议选择
      if (this.form.protocolType === 1 && this.protocolIds.length === 0) {
        this.$message.warning('请选择协议!')
        return
      }

      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const data = {
              ipAddr: this.allChecked ? 'all' : this.collectIpList.join(';'),
              protocolIds: this.form.protocolType === 1 ? this.protocolIds.join(',') : 0,
              id: this.form.id,
              deviceIds: '',
              tacticsCode: '',
            }

            let res
            if (this.title.indexOf('新增') !== -1) {
              res = await tacticsAdd(data)
            } else {
              res = await tacticsUpdate(data)
            }

            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('getSourceData')
              this.onDrawerClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.multi-ip-container {
  .ip-tags {
    min-height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px;
    background-color: #fff;
  }
}
</style>
