<template>
  <el-drawer
    :visible.sync="visible"
    :title="isEdit ? '编辑采集策略' : '新建采集策略'"
    size="50%"
    direction="rtl"
    @close="onClose"
  >
    <div style="padding: 20px;">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="IP地址" prop="ipAddr">
          <el-input v-model="form.ipAddr" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="协议" prop="protocolNames">
          <el-input v-model="form.protocolNames" placeholder="请输入协议" />
        </el-form-item>
      </el-form>
      <div style="text-align: right; margin-top: 20px;">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'AddStrategyCollection',
  data() {
    return {
      visible: false,
      loading: false,
      isEdit: false,
      form: {
        ipAddr: '',
        protocolNames: '',
      },
      rules: {
        ipAddr: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
        ],
        protocolNames: [
          { required: true, message: '请输入协议', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    showDrawer(record = {}) {
      this.isEdit = !!(record && record.id)
      if (this.isEdit) {
        this.form = {
          ipAddr: record.ipAddr || '',
          protocolNames: record.protocolNames || '',
        }
      } else {
        this.form = {
          ipAddr: '',
          protocolNames: '',
        }
      }
      this.visible = true
    },

    onClose() {
      this.visible = false
      this.loading = false
      this.isEdit = false
      this.form = {
        ipAddr: '',
        protocolNames: '',
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          setTimeout(() => {
            this.$emit('getSourceData')
            this.loading = false
            this.onClose()
          }, 1000)
        }
      })
    },
  },
}
</script>
