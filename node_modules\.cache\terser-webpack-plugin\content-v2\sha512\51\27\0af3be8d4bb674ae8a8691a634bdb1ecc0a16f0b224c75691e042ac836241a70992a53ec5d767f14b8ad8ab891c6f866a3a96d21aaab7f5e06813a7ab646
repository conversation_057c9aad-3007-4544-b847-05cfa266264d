{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-164be1d1\"],{\"078a\":function(e,t,a){\"use strict\";var i=a(\"2b0e\"),o=(a(\"99af\"),a(\"caad\"),a(\"ac1f\"),a(\"2532\"),a(\"5319\"),{bind:function(e,t,a){var i=[e.querySelector(\".el-dialog__header\"),e.querySelector(\".el-dialog\")],o=i[0],n=i[1];o.style.cssText+=\";cursor:move;\",n.style.cssText+=\";top:0px;\";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();o.onmousedown=function(e){var t=[e.clientX-o.offsetLeft,e.clientY-o.offsetTop,n.offsetWidth,n.offsetHeight,document.body.clientWidth,document.body.clientHeight],i=t[0],l=t[1],s=t[2],d=t[3],c=t[4],u=t[5],m=[n.offsetLeft,c-n.offsetLeft-s,n.offsetTop,u-n.offsetTop-d],f=m[0],p=m[1],b=m[2],h=m[3],g=[r(n,\"left\"),r(n,\"top\")],v=g[0],$=g[1];v.includes(\"%\")?(v=+document.body.clientWidth*(+v.replace(/%/g,\"\")/100),$=+document.body.clientHeight*(+$.replace(/%/g,\"\")/100)):(v=+v.replace(/px/g,\"\"),$=+$.replace(/px/g,\"\")),document.onmousemove=function(e){var t=e.clientX-i,o=e.clientY-l;-t>f?t=-f:t>p&&(t=p),-o>b?o=-b:o>h&&(o=h),n.style.cssText+=\";left:\".concat(t+v,\"px;top:\").concat(o+$,\"px;\"),a.child.$emit(\"dragDialog\")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),n=function(e){e.directive(\"el-dialog-drag\",o)};window.Vue&&(window[\"el-dialog-drag\"]=o,i[\"default\"].use(n)),o.elDialogDrag=n;t[\"a\"]=o},\"1f93\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return o})),a.d(t,\"i\",(function(){return n})),a.d(t,\"g\",(function(){return r})),a.d(t,\"c\",(function(){return l})),a.d(t,\"f\",(function(){return s})),a.d(t,\"h\",(function(){return d})),a.d(t,\"n\",(function(){return c})),a.d(t,\"m\",(function(){return u})),a.d(t,\"k\",(function(){return m})),a.d(t,\"l\",(function(){return f})),a.d(t,\"b\",(function(){return p})),a.d(t,\"o\",(function(){return b})),a.d(t,\"j\",(function(){return h})),a.d(t,\"e\",(function(){return g})),a.d(t,\"d\",(function(){return v}));var i=a(\"4020\");function o(e){return Object(i[\"a\"])({url:\"/event/original/accessControlLog\",method:\"get\",params:e||{}})}function n(e){return Object(i[\"a\"])({url:\"/event/original/networkOperationLog\",method:\"get\",params:e||{}})}function r(e){return Object(i[\"a\"])({url:\"/event/original/industrialControlOperationLog\",method:\"get\",params:e||{}})}function l(e){return Object(i[\"a\"])({url:\"/event/original/fileTransferLog\",method:\"get\",params:e||{}})}function s(e){return Object(i[\"a\"])({url:\"/event/original/industrialControlFileTransferLog\",method:\"get\",params:e||{}})}function d(e){return Object(i[\"a\"])({url:\"/event/original/kvmOperationLog\",method:\"get\",params:e||{}})}function c(e){return Object(i[\"a\"])({url:\"/event/original/udiskWebTransmission\",method:\"get\",params:e||{}})}function u(e){return Object(i[\"a\"])({url:\"/event/original/udiskWebMapTransmission\",method:\"get\",params:e||{}})}function m(e){return Object(i[\"a\"])({url:\"/event/original/serialPort\",method:\"get\",params:e||{}})}function f(e){return Object(i[\"a\"])({url:\"/event/original/serialPortConsole\",method:\"get\",params:e||{}})}function p(e){return Object(i[\"a\"])({url:\"/event/original/downFile\",method:\"get\",params:e||{}},\"download\")}function b(e){return Object(i[\"a\"])({url:\"/event/serialport/combo/workmode\",method:\"get\",params:e||{}})}function h(e){return Object(i[\"a\"])({url:\"/event/original/getProtocols\",method:\"get\",params:e||{}})}function g(e){return Object(i[\"a\"])({url:\"/event/original/getVideoUrl\",method:\"get\",params:e||{}})}function v(){return Object(i[\"a\"])({url:\"/platform/all\",method:\"get\"})}},2532:function(e,t,a){\"use strict\";var i=a(\"23e7\"),o=a(\"5a34\"),n=a(\"1d80\"),r=a(\"ab13\");i({target:\"String\",proto:!0,forced:!r(\"includes\")},{includes:function(e){return!!~String(n(this)).indexOf(o(e),arguments.length>1?arguments[1]:void 0)}})},\"483d\":function(e,t,a){\"use strict\";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"el-select\",{staticClass:\"platform\",staticStyle:{width:\"100%\"},attrs:{clearable:\"\",placeholder:\"来源平台\"},on:{change:e.handleChange},model:{value:e.platformValue.domainToken,callback:function(t){e.$set(e.platformValue,\"domainToken\",t)},expression:\"platformValue.domainToken\"}},e._l(e.platformOption,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.platformName,value:e.domainToken}})})),1)},o=[],n=a(\"1f93\"),r={props:{platformValue:{required:!0,type:Object}},data:function(){return{platformOption:[]}},mounted:function(){var e=this;Object(n[\"d\"])().then((function(t){e.platformOption=t}))},methods:{handleChange:function(){this.$emit(\"change\",this.platformValue)}}},l=r,s=a(\"2877\"),d=Object(s[\"a\"])(l,i,o,!1,null,\"7b618a7a\",null);t[\"a\"]=d.exports},\"5a34\":function(e,t,a){var i=a(\"44e7\");e.exports=function(e){if(i(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"6ff7\":function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"router-wrap-table\"},[a(\"table-header\",{attrs:{condition:e.query},on:{\"update:condition\":function(t){e.query=t},\"on-change\":e.changeQueryTable,\"on-add\":e.clickAdd,\"on-batch-delete\":e.clickBatchDelete}}),a(\"table-body\",{attrs:{\"title-name\":e.title,\"table-loading\":e.table.loading,\"table-data\":e.table.data},on:{\"on-select\":e.clickSelectRows,\"on-detail\":e.clickDetail,\"on-update\":e.clickUpdate,\"on-delete\":e.clickDelete}}),a(\"table-footer\",{attrs:{pagination:e.pagination},on:{\"update:pagination\":function(t){e.pagination=t},\"size-change\":e.tableSizeChange,\"page-change\":e.tablePageChange}}),a(\"add-dialog\",{attrs:{visible:e.dialog.add.visible,\"title-name\":e.title,model:e.dialog.add.model},on:{\"update:visible\":function(t){return e.$set(e.dialog.add,\"visible\",t)},\"on-submit\":e.addSubmit}}),a(\"upd-dialog\",{attrs:{visible:e.dialog.update.visible,\"title-name\":e.title,model:e.dialog.update.model},on:{\"update:visible\":function(t){return e.$set(e.dialog.update,\"visible\",t)},\"on-submit\":e.updSubmit}}),a(\"detail-dialog\",{attrs:{visible:e.dialog.detail.visible,\"title-name\":e.title,model:e.dialog.detail.model},on:{\"update:visible\":function(t){return e.$set(e.dialog.detail,\"visible\",t)}}})],1)},o=[],n=(a(\"d81d\"),a(\"d3b7\"),a(\"ac1f\"),a(\"25f0\"),a(\"1276\"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"header\",{staticClass:\"table-header\"},[a(\"section\",{staticClass:\"table-header-main\"},[a(\"section\",{staticClass:\"table-header-search\"},[a(\"section\",{directives:[{name:\"show\",rawName:\"v-show\",value:!e.filterCondition.senior,expression:\"!filterCondition.senior\"}],staticClass:\"table-header-search-input\"},[a(\"el-input\",{attrs:{\"prefix-icon\":\"soc-icon-search\",clearable:\"\",placeholder:e.$t(\"tip.placeholder.query\",[e.$t(\"asset.area.prop.domaName\")])},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.fuzzyField,callback:function(t){e.$set(e.filterCondition.form,\"fuzzyField\",\"string\"===typeof t?t.trim():t)},expression:\"filterCondition.form.fuzzyField\"}})],1),a(\"section\",{staticClass:\"table-header-search-button\"},[e.filterCondition.senior?e._e():a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:e.changeQueryCondition}},[e._v(\" \"+e._s(e.$t(\"button.query\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:e.clickExactQuery}},[e._v(\" \"+e._s(e.$t(\"button.search.exact\"))+\" \"),a(\"i\",{staticClass:\"el-icon--right\",class:e.filterCondition.senior?\"el-icon-arrow-up\":\"el-icon-arrow-down\"})])],1)]),a(\"section\",{staticClass:\"table-header-button\"},[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"add\",expression:\"'add'\"}],on:{click:e.clickAdd}},[e._v(\" \"+e._s(e.$t(\"button.add\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"delete\",expression:\"'delete'\"}],on:{click:e.clickBatchDelete}},[e._v(\" \"+e._s(e.$t(\"button.batch.delete\"))+\" \")])],1)]),a(\"section\",{staticClass:\"table-header-extend\"},[a(\"el-collapse-transition\",[a(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.filterCondition.senior,expression:\"filterCondition.senior\"}],staticClass:\"table-header-query\"},[a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:6}},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:e.$t(\"asset.area.prop.domaName\")},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.domaName,callback:function(t){e.$set(e.filterCondition.form,\"domaName\",t)},expression:\"filterCondition.form.domaName\"}})],1),a(\"el-col\",{attrs:{span:6}},[a(\"el-input\",{attrs:{clearable:\"\",placeholder:e.$t(\"asset.area.prop.domaAbbr\")},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.domaAbbreviation,callback:function(t){e.$set(e.filterCondition.form,\"domaAbbreviation\",t)},expression:\"filterCondition.form.domaAbbreviation\"}})],1),a(\"el-col\",{attrs:{span:6}},[a(\"PlatformSelect\",{attrs:{platformValue:e.filterCondition.form},on:{\"update:platformValue\":function(t){return e.$set(e.filterCondition,\"form\",t)},\"update:platform-value\":function(t){return e.$set(e.filterCondition,\"form\",t)},change:e.changeQueryCondition}})],1),a(\"el-col\",{attrs:{align:\"right\",span:6}},[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:e.changeQueryCondition}},[e._v(\" \"+e._s(e.$t(\"button.query\"))+\" \")]),a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],on:{click:e.resetQuery}},[e._v(\" \"+e._s(e.$t(\"button.reset.default\"))+\" \")]),a(\"el-button\",{ref:\"shrinkButton\",on:{click:e.clickUpButton}},[a(\"i\",{staticClass:\"soc-icon-scroller-top-all\"})])],1)],1)],1)])],1)])}),r=[],l=a(\"13c3\"),s=a(\"483d\"),d={props:{condition:{required:!0,type:Object}},components:{PlatformSelect:s[\"a\"]},data:function(){return{filterCondition:this.condition,debounce:null}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit(\"update:condition\",e)}},mounted:function(){this.initDebounceQuery()},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(l[\"a\"])((function(){e.$emit(\"on-change\")}),400)},changeQueryCondition:function(){this.debounce()},clickExactQuery:function(){this.filterCondition.senior=!this.filterCondition.senior,this.resetQuery()},clickUpButton:function(){this.filterCondition.senior=!1,this.resetQuery()},resetQuery:function(){this.filterCondition.form={fuzzyField:\"\",domaName:\"\",domaAbbreviation:\"\",domainToken:\"\"},this.changeQueryCondition()},clickAdd:function(){this.$emit(\"on-add\")},clickBatchDelete:function(){this.$emit(\"on-batch-delete\")}}},c=d,u=a(\"2877\"),m=Object(u[\"a\"])(c,n,r,!1,null,null,null),f=m.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"main\",{staticClass:\"table-body\"},[a(\"header\",{staticClass:\"table-body-header\"},[a(\"h2\",{staticClass:\"table-body-title\"},[e._v(\" \"+e._s(e.titleName)+\" \")])]),a(\"main\",{staticClass:\"table-body-main\"},[a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.tableLoading,expression:\"tableLoading\"}],attrs:{data:e.tableData,\"element-loading-background\":\"rgba(0, 0, 0, 0.3)\",size:\"mini\",\"highlight-current-row\":\"\",\"tooltip-effect\":\"light\",height:\"100%\"},on:{\"selection-change\":e.clickSelectRows}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"50\",align:\"center\"}}),a(\"el-table-column\",{attrs:{prop:\"domaName\",label:e.$t(\"asset.area.prop.domaName\"),\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"domaAbbreviation\",label:e.$t(\"asset.area.prop.domaAbbr\"),\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"officeTel\",label:e.$t(\"asset.area.prop.officeTel\"),\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"email\",label:e.$t(\"asset.area.prop.email\"),\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"deviceNum\",label:e.$t(\"asset.area.prop.deviceNum\"),\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"domainName\",label:\"来源平台\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{fixed:\"right\",width:\"210\",align:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"query\",expression:\"'query'\"}],staticClass:\"el-button--blue\",on:{click:function(a){return e.clickDetail(t.row)}}},[e._v(\" \"+e._s(e.$t(\"button.detail\"))+\" \")]),t.row.canEdit?a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"update\",expression:\"'update'\"}],staticClass:\"el-button--blue\",on:{click:function(a){return e.clickUpdate(t.row)}}},[e._v(\" \"+e._s(e.$t(\"button.update\"))+\" \")]):e._e(),t.row.canDelete?a(\"el-button\",{directives:[{name:\"has\",rawName:\"v-has\",value:\"delete\",expression:\"'delete'\"}],staticClass:\"el-button--red\",on:{click:function(a){return e.clickDelete(t.row)}}},[e._v(\" \"+e._s(e.$t(\"button.delete\"))+\" \")]):e._e()]}}])})],1)],1)])},b=[],h={props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{required:!0,type:Array}},methods:{clickSelectRows:function(e){this.$emit(\"on-select\",e)},clickDetail:function(e){this.$emit(\"on-detail\",e)},clickUpdate:function(e){this.$emit(\"on-update\",e)},clickDelete:function(e){this.$emit(\"on-delete\",e)}}},g=h,v=Object(u[\"a\"])(g,p,b,!1,null,null,null),$=v.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"section\",{staticClass:\"table-footer\"},[e.filterCondition.visible?a(\"el-pagination\",{attrs:{small:\"\",background:\"\",align:\"right\",\"current-page\":e.filterCondition.pageNum,\"page-sizes\":[10,20,50,100],\"page-size\":e.filterCondition.pageSize,layout:\"total, sizes, prev, pager, next, jumper\",total:e.filterCondition.total},on:{\"size-change\":e.clickSize,\"current-change\":e.clickPage}}):e._e()],1)},k=[],C={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit(\"update:pagination\",e)}},methods:{clickSize:function(e){this.$emit(\"size-change\",e)},clickPage:function(e){this.$emit(\"page-change\",e)}}},w=C,A=Object(u[\"a\"])(w,y,k,!1,null,null,null),x=A.exports,O=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"custom-dialog\",{ref:\"dialogDom\",attrs:{visible:e.visible,title:e.$t(\"dialog.title.add\",[e.titleName]),width:\"60%\"},on:{\"on-close\":e.clickCancel,\"on-submit\":e.clickSubmit}},[a(\"el-form\",{ref:\"formTemplate\",attrs:{model:e.model,rules:e.rules,\"label-width\":\"120px\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaName\",label:e.$t(\"asset.area.prop.domaName\")}},[a(\"el-input\",{attrs:{maxlength:\"128\"},model:{value:e.model.domaName,callback:function(t){e.$set(e.model,\"domaName\",\"string\"===typeof t?t.trim():t)},expression:\"model.domaName\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaAbbreviation\",label:e.$t(\"asset.area.prop.domaAbbr\")}},[a(\"el-input\",{attrs:{maxlength:\"16\"},model:{value:e.model.domaAbbreviation,callback:function(t){e.$set(e.model,\"domaAbbreviation\",t)},expression:\"model.domaAbbreviation\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"officeTel\",label:e.$t(\"asset.area.prop.officeTel\")}},[a(\"el-input\",{attrs:{maxlength:\"16\"},model:{value:e.model.officeTel,callback:function(t){e.$set(e.model,\"officeTel\",t)},expression:\"model.officeTel\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"email\",label:e.$t(\"asset.area.prop.email\")}},[a(\"el-input\",{attrs:{maxlength:\"64\"},model:{value:e.model.email,callback:function(t){e.$set(e.model,\"email\",t)},expression:\"model.email\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaFunc\",label:e.$t(\"asset.area.prop.domaFunc\")}},[a(\"el-input\",{attrs:{maxlength:\"256\"},model:{value:e.model.domaFunc,callback:function(t){e.$set(e.model,\"domaFunc\",t)},expression:\"model.domaFunc\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaAddress\",label:e.$t(\"asset.area.prop.domaAddress\")}},[a(\"el-input\",{attrs:{maxlength:\"256\"},model:{value:e.model.domaAddress,callback:function(t){e.$set(e.model,\"domaAddress\",t)},expression:\"model.domaAddress\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{prop:\"domaMemo\",label:e.$t(\"asset.area.prop.domaMemo\")}},[a(\"el-input\",{attrs:{type:\"textarea\",rows:4,maxlength:\"2048\"},model:{value:e.model.domaMemo,callback:function(t){e.$set(e.model,\"domaMemo\",t)},expression:\"model.domaMemo\"}})],1)],1)],1)],1)],1)},N=[],_=a(\"d465\"),j=a(\"f7b5\"),T=a(\"c54a\"),z={components:{CustomDialog:_[\"a\"]},props:{visible:{required:!0,type:Boolean},titleName:{required:!0,type:String},model:{required:!0,type:Object}},data:function(){var e=this,t=function(t,a,i){\"\"===a||Object(T[\"d\"])(a)||Object(T[\"a\"])(a)?i():i(new Error(e.$t(\"validate.telephone\")))},a=function(t,a,i){\"\"===a||Object(T[\"c\"])(a)?i():i(new Error(e.$t(\"validate.email.incorrect\")))};return{dialogVisible:this.visible,rules:{domaName:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],officeTel:[{trigger:\"blur\",validator:t}],email:[{trigger:\"blur\",validator:a}]}}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit(\"update:visible\",e)}},methods:{clickCancel:function(){this.$refs.dialogDom.end(),this.dialogVisible=!1},clickSubmit:function(){var e=this;this.$refs.formTemplate.validate((function(t){t?e.$confirm(e.$t(\"tip.confirm.submit\"),e.$t(\"tip.confirm.tip\"),{closeOnClickModal:!1}).then((function(){e.$emit(\"on-submit\",e.model),e.clickCancel()})):Object(j[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){return!1}))})),this.$refs.dialogDom.end()}}},q=z,D=Object(u[\"a\"])(q,O,N,!1,null,null,null),S=D.exports,F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"custom-dialog\",{ref:\"dialogDom\",attrs:{visible:e.visible,title:e.$t(\"dialog.title.update\",[e.titleName]),width:\"60%\"},on:{\"on-close\":e.clickCancel,\"on-submit\":e.clickSubmit}},[a(\"el-form\",{ref:\"formTemplate\",attrs:{model:e.model,rules:e.rules,\"label-width\":\"120px\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaName\",label:e.$t(\"asset.area.prop.domaName\")}},[a(\"el-input\",{attrs:{maxlength:\"128\"},model:{value:e.model.domaName,callback:function(t){e.$set(e.model,\"domaName\",\"string\"===typeof t?t.trim():t)},expression:\"model.domaName\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaAbbreviation\",label:e.$t(\"asset.area.prop.domaAbbr\")}},[a(\"el-input\",{attrs:{maxlength:\"16\"},model:{value:e.model.domaAbbreviation,callback:function(t){e.$set(e.model,\"domaAbbreviation\",t)},expression:\"model.domaAbbreviation\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"officeTel\",label:e.$t(\"asset.area.prop.officeTel\")}},[a(\"el-input\",{attrs:{maxlength:\"16\"},model:{value:e.model.officeTel,callback:function(t){e.$set(e.model,\"officeTel\",t)},expression:\"model.officeTel\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"email\",label:e.$t(\"asset.area.prop.email\")}},[a(\"el-input\",{attrs:{maxlength:\"64\"},model:{value:e.model.email,callback:function(t){e.$set(e.model,\"email\",t)},expression:\"model.email\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaFunc\",label:e.$t(\"asset.area.prop.domaFunc\")}},[a(\"el-input\",{attrs:{maxlength:\"256\"},model:{value:e.model.domaFunc,callback:function(t){e.$set(e.model,\"domaFunc\",t)},expression:\"model.domaFunc\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:\"domaAddress\",label:e.$t(\"asset.area.prop.domaAddress\")}},[a(\"el-input\",{attrs:{maxlength:\"256\"},model:{value:e.model.domaAddress,callback:function(t){e.$set(e.model,\"domaAddress\",t)},expression:\"model.domaAddress\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{prop:\"domaMemo\",label:e.$t(\"asset.area.prop.domaMemo\")}},[a(\"el-input\",{attrs:{type:\"textarea\",rows:4,maxlength:\"2048\"},model:{value:e.model.domaMemo,callback:function(t){e.$set(e.model,\"domaMemo\",t)},expression:\"model.domaMemo\"}})],1)],1)],1)],1)],1)},Q=[],V={components:{CustomDialog:_[\"a\"]},props:{visible:{required:!0,type:Boolean},titleName:{required:!0,type:String},model:{required:!0,type:Object}},data:function(){var e=this,t=function(t,a,i){\"\"===a||null===a||Object(T[\"d\"])(a)||Object(T[\"a\"])(a)?i():i(new Error(e.$t(\"validate.telephone\")))},a=function(t,a,i){\"\"===a||null===a||Object(T[\"c\"])(a)?i():i(new Error(e.$t(\"validate.email.incorrect\")))};return{dialogVisible:this.visible,rules:{domaName:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],officeTel:[{trigger:\"blur\",validator:t}],email:[{trigger:\"blur\",validator:a}]}}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit(\"update:visible\",e)}},methods:{clickCancel:function(){this.$refs.dialogDom.end(),this.dialogVisible=!1},clickSubmit:function(){var e=this;this.$refs.formTemplate.validate((function(t){t?e.$confirm(e.$t(\"tip.confirm.submit\"),e.$t(\"tip.confirm.tip\"),{closeOnClickModal:!1}).then((function(){e.$emit(\"on-submit\",e.model),e.clickCancel()})):Object(j[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){return!1}))})),this.$refs.dialogDom.end()}}},M=V,E=Object(u[\"a\"])(M,F,Q,!1,null,null,null),Z=E.exports,B=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"custom-dialog\",{ref:\"dialogDom\",attrs:{visible:e.visible,title:e.$t(\"dialog.title.detail\",[e.titleName]),width:\"60%\",action:!1},on:{\"on-close\":e.clickCancel}},[a(\"section\",[a(\"el-form\",{attrs:{model:e.model,\"label-width\":\"120px\"}},[a(\"el-row\",e._l(e.columnOption,(function(t,i){return a(\"el-col\",{key:i,attrs:{span:12}},[a(\"el-form-item\",{attrs:{prop:t.key,label:t.label}},[e._v(\" \"+e._s(e.model[t.key])+\" \")])],1)})),1)],1)],1)])},L=[],P={components:{CustomDialog:_[\"a\"]},props:{visible:{required:!0,type:Boolean},titleName:{type:String,default:\"\"},model:{required:!0,type:Object}},data:function(){return{dialogVisible:this.visible,columnOption:[{key:\"domaName\",label:this.$t(\"asset.area.prop.domaName\")},{key:\"domaAbbreviation\",label:this.$t(\"asset.area.prop.domaAbbr\")},{key:\"officeTel\",label:this.$t(\"asset.area.prop.officeTel\")},{key:\"email\",label:this.$t(\"asset.area.prop.email\")},{key:\"domaFunc\",label:this.$t(\"asset.area.prop.domaFunc\")},{key:\"deviceNum\",label:this.$t(\"asset.area.prop.deviceNum\")},{key:\"domaAddress\",label:this.$t(\"asset.area.prop.domaAddress\")},{key:\"domaMemo\",label:this.$t(\"asset.area.prop.domaMemo\")}]}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit(\"update:visible\",e)}},methods:{clickCancel:function(){this.$refs.dialogDom.end(),this.dialogVisible=!1}}},U=P,R=Object(u[\"a\"])(U,B,L,!1,null,null,null),W=R.exports,H=a(\"4020\");function I(e){return Object(H[\"a\"])({url:\"/domainmanagement/domains\",method:\"get\",params:e||{}})}function J(e){return Object(H[\"a\"])({url:\"/domainmanagement/addDomain\",method:\"post\",data:e||{}})}function X(e){return Object(H[\"a\"])({url:\"/domainmanagement/updateDomain\",method:\"put\",data:e||{}})}function Y(e){return Object(H[\"a\"])({url:\"/domainmanagement/deleteDomains/\".concat(e),method:\"delete\"})}var G={components:{TableHeader:f,TableBody:$,TableFooter:x,AddDialog:S,UpdDialog:Z,DetailDialog:W},data:function(){return{title:this.$t(\"asset.area.header\"),query:{senior:!1,form:{fuzzyField:\"\",domaName:\"\",domaAbbreviation:\"\",domainToken:\"\"}},table:{loading:!1,data:[],selected:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0},dialog:{add:{visible:!1,model:{}},update:{visible:!1,model:{domaName:\"\",domaAbbreviation:\"\",officeTel:\"\",email:\"\",domaAddress:\"\",domaFunc:\"\",domaMemo:\"\"}},detail:{visible:!1,model:{}}}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){\"turn-page\"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){var e={};return e=this.query.senior?Object.assign(e,{domaName:this.query.form.domaName,domaAbbreviation:this.query.form.domaAbbreviation,domainToken:this.query.form.domainToken}):Object.assign(e,{fuzzyField:this.query.form.fuzzyField}),e},clickSelectRows:function(e){this.table.selected=e},clickAdd:function(){this.dialog.add.visible=!0,this.dialog.add.model={domaName:\"\",domaAbbreviation:\"\",officeTel:\"\",email:\"\",domaAddress:\"\",domaFunc:\"\",domaMemo:\"\"}},clickDetail:function(e){this.dialog.detail.visible=!0,this.dialog.detail.model=e},clickUpdate:function(e){this.dialog.update.model={domaId:e.domaId,domainToken:e.domainToken,domaName:e.domaName,domaAbbreviation:e.domaAbbreviation,officeTel:e.officeTel,email:e.email,domaAddress:e.domaAddress,domaFunc:e.domaFunc,domaMemo:e.domaMemo},this.dialog.update.visible=!0},clickDelete:function(e){var t=e.domaId;this.deleteArea(t)},clickBatchDelete:function(){if(this.table.selected.length>0){var e=this.table.selected.map((function(e){return e.domaId})).toString();this.deleteArea(e)}else Object(j[\"a\"])({i18nCode:\"tip.delete.prompt\",type:\"warning\",print:!0})},addSubmit:function(e){var t=Object.assign({},e);this.addArea(t)},updSubmit:function(e){var t=Object.assign({},e);this.updateArea(t)},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable(\"turn-page\")},queryTableData:function(e){var t=this;e=Object.assign({},e,{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum}),this.table.loading=!0,this.pagination.visible=!1,I(e).then((function(e){e&&(t.table.data=e.rows,t.pagination.total=e.total,t.pagination.pageNum=e.pageNum,t.pagination.pageSize=e.pageSize),t.table.loading=!1,t.pagination.visible=!0}))},addArea:function(e){var t=this;J(e).then((function(e){\"success\"===e?Object(j[\"a\"])({i18nCode:\"tip.add.success\",type:\"success\"},(function(){t.queryTableData()})):\"existName\"===e?Object(j[\"a\"])({i18nCode:\"tip.add.repeat\",type:\"error\"}):Object(j[\"a\"])({i18nCode:\"tip.add.error\",type:\"error\"})}))},updateArea:function(e){var t=this;X(e).then((function(e){\"success\"===e?Object(j[\"a\"])({i18nCode:\"tip.update.success\",type:\"success\"},(function(){t.changeQueryTable()})):\"existName\"===e?Object(j[\"a\"])({i18nCode:\"tip.update.repeat\",type:\"warning\"}):Object(j[\"a\"])({i18nCode:\"tip.update.error\",type:\"error\"})}))},deleteArea:function(e){var t=this;this.$confirm(this.$t(\"tip.confirm.batchDelete\"),this.$t(\"tip.confirm.tip\"),{closeOnClickModal:!1}).then((function(){Y(e).then((function(a){\"success\"===a?Object(j[\"a\"])({i18nCode:\"tip.delete.success\",type:\"success\"},(function(){t.query.form={fuzzyField:\"\",domaName:\"\",domaAbbreviation:\"\"};var a=[t.pagination.pageNum,e.split(\",\")],i=a[0],o=a[1];o.length===t.table.data.length&&(t.pagination.pageNum=1===i?1:i-1),t.queryTableData()})):\"inUse\"===a?Object(j[\"a\"])({i18nCode:\"tip.delete.use\",type:\"error\"}):Object(j[\"a\"])({i18nCode:\"tip.delete.error\",type:\"error\"})}))}))}}},K=G,ee=Object(u[\"a\"])(K,i,o,!1,null,null,null);t[\"default\"]=ee.exports},ab13:function(e,t,a){var i=a(\"b622\"),o=i(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[o]=!1,\"/./\"[e](t)}catch(i){}}return!1}},c54a:function(e,t,a){\"use strict\";a.d(t,\"l\",(function(){return i})),a.d(t,\"m\",(function(){return o})),a.d(t,\"b\",(function(){return n})),a.d(t,\"c\",(function(){return r})),a.d(t,\"a\",(function(){return l})),a.d(t,\"j\",(function(){return s})),a.d(t,\"q\",(function(){return d})),a.d(t,\"d\",(function(){return c})),a.d(t,\"f\",(function(){return u})),a.d(t,\"g\",(function(){return m})),a.d(t,\"e\",(function(){return f})),a.d(t,\"n\",(function(){return p})),a.d(t,\"k\",(function(){return b})),a.d(t,\"p\",(function(){return h})),a.d(t,\"h\",(function(){return g})),a.d(t,\"i\",(function(){return v})),a.d(t,\"o\",(function(){return $}));a(\"ac1f\"),a(\"466d\"),a(\"1276\");function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=\"\";switch(t){case 0:a=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break;case 1:a=/^(?![_.\\-])(?!.*?[_.\\-]$)[a-zA-Z0-9_.\\-\\u4e00-\\u9fa5]+$/;break;case 2:a=/^(?![_./\\-])(?!.*?[_./\\-]$)[a-zA-Z0-9_./\\-\\u4e00-\\u9fa5]+$/;break;case 3:a=/^(?![_./\\-\\s])(?!.*?[_./\\-\\s]$)[a-zA-Z0-9_./\\-\\s\\u4e00-\\u9fa5]+$/;break;default:a=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break}return a.test(e)}function o(e){var t=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\\[\\].<>/?\\-%]).{0,}$/;return t.test(e)}function n(e){var t=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return t.test(e)}function r(e){var t=/^([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+\\.[a-zA-Z]{2,3}$/;return t.test(e)}function l(e){var t=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return t.test(e)}function s(e){for(var t=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,a=e.split(\",\"),i=0;i<a.length;i++)if(!t.test(a[i]))return!1;return!0}function d(e){var t=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return t.test(e)}function c(e){var t=/^(\\d{2,5}-)?\\d{6,9}(-\\d{2,4})?$/;return t.test(e)}function u(e){var t=/^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;return t.test(e)}function m(e){var t=/:/.test(e)&&e.match(/:/g).length<8&&/::/.test(e)?1===e.match(/::/g).length&&/^::$|^(::)?([\\da-f]{1,4}(:|::))*[\\da-f]{1,4}(:|::)?$/i.test(e):/^([\\da-f]{1,4}:){7}[\\da-f]{1,4}$/i.test(e);return t}function f(e){return u(e)||m(e)}function p(e){var t=/^([0-9]|[1-9][0-9]{0,4})$/;return t.test(e)}function b(e){for(var t=/^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(\\:([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,a=e.split(\",\"),i=0;i<a.length;i++)if(!t.test(a[i]))return!1;return!0}function h(e){var t=/^[^ ]+$/;return t.test(e)}function g(e){var t=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\\.[A-Fa-f0-9]{4}){2}$/;return t.test(e)}function v(e){var t=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return t.test(e)}function $(e){var t=/[^\\u4E00-\\u9FA5]/;return t.test(e)}},caad:function(e,t,a){\"use strict\";var i=a(\"23e7\"),o=a(\"4d64\").includes,n=a(\"44d2\"),r=a(\"ae40\"),l=r(\"indexOf\",{ACCESSORS:!0,1:0});i({target:\"Array\",proto:!0,forced:!l},{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),n(\"includes\")},d81d:function(e,t,a){\"use strict\";var i=a(\"23e7\"),o=a(\"b727\").map,n=a(\"1dde\"),r=a(\"ae40\"),l=n(\"map\"),s=r(\"map\");i({target:\"Array\",proto:!0,forced:!l||!s},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);", "extractedComments": []}