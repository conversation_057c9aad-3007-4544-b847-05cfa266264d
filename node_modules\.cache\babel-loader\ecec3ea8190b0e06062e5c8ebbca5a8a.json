{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyAuditRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyAuditRecord\\index.vue", "mtime": 1750323702933}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}