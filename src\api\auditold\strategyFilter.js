import request from '@util/requestForPy'

// 策略过滤-查询
export async function tacticsSearch(params) {
  return request({
    url: '/home_dev/filter_tactics/search',
    method: 'post',
    data: params,
  })
}

// 策略过滤-删除
export async function tacticsDelete(params) {
  return request({
    url: '/home_dev/filter_tactics/delete',
    method: 'post',
    data: params,
  })
}

// 策略过滤-下发
export async function tacticsDistrib(params) {
  return request({
    url: '/home_dev/filter_tactics/distrib',
    method: 'post',
    data: params,
  })
}
