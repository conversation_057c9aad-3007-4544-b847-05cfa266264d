<template>
  <div class="test-container">
    <h2>addStrstegyFilter 组件功能测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>功能验证</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>测试按钮</h3>
          <el-button type="primary" @click="testNewAdd">测试新增功能</el-button>
          <el-button type="success" @click="testEdit">测试编辑功能</el-button>
        </el-col>
        
        <el-col :span="12">
          <h3>功能检查清单</h3>
          <el-checkbox-group v-model="checkedFeatures">
            <el-checkbox label="地址类型选择">地址类型选择（单个/一对）</el-checkbox>
            <el-checkbox label="动态表单">动态表单字段显示</el-checkbox>
            <el-checkbox label="地址验证">IP/MAC地址验证</el-checkbox>
            <el-checkbox label="协议选择">协议选择模态框</el-checkbox>
            <el-checkbox label="表单提交">表单提交和API调用</el-checkbox>
            <el-checkbox label="工具提示">工具提示说明</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </el-card>

    <el-card>
      <div slot="header">
        <span>测试数据</span>
      </div>
      
      <el-table :data="testData" style="width: 100%">
        <el-table-column prop="field" label="字段名" width="150" />
        <el-table-column prop="react" label="React版本" />
        <el-table-column prop="vue" label="Vue版本" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '一致' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 测试组件 -->
    <add-strategy-filter ref="addStrategyFilterRef" @getSourceData="handleGetSourceData" />
  </div>
</template>

<script>
import AddStrategyFilter from './addStrstegyFilter'

export default {
  name: 'TestAddStrategyFilter',
  components: {
    AddStrategyFilter,
  },
  data() {
    return {
      checkedFeatures: [],
      testData: [
        {
          field: 'ipType',
          react: '地址类型选择（0/1）',
          vue: '地址类型选择（0/1）',
          status: '一致'
        },
        {
          field: 'firstIp',
          react: 'IP/MAC地址验证',
          vue: 'IP/MAC地址验证',
          status: '一致'
        },
        {
          field: 'secondIp',
          react: '动态显示/隐藏',
          vue: '动态显示/隐藏',
          status: '一致'
        },
        {
          field: 'protocolId',
          react: '隐藏字段存储ID',
          vue: '隐藏字段存储ID',
          status: '一致'
        },
        {
          field: 'protocolName',
          react: '协议选择模态框',
          vue: '协议选择模态框',
          status: '一致'
        },
        {
          field: 'validation',
          react: '复杂验证规则',
          vue: '复杂验证规则',
          status: '一致'
        },
        {
          field: 'api',
          react: 'tacticsAdd/Update',
          vue: 'tacticsAdd/Update',
          status: '一致'
        }
      ]
    }
  },
  methods: {
    testNewAdd() {
      this.$refs.addStrategyFilterRef.showDrawer()
      this.$message.success('已打开新增过滤策略弹框')
    },

    testEdit() {
      const mockData = {
        id: '123',
        ipType: 1,
        firstIp: '***********',
        secondIp: '***********',
        protocolId: '1',
        protocolName: 'HTTP',
      }
      this.$refs.addStrategyFilterRef.showDrawer(mockData)
      this.$message.success('已打开编辑过滤策略弹框')
    },

    handleGetSourceData() {
      this.$message.success('数据刷新回调触发成功')
    },
  },
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  
  h2 {
    color: #409eff;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #666;
    margin-bottom: 15px;
  }
  
  .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  .el-checkbox {
    display: block;
    margin-bottom: 8px;
  }
}
</style>
