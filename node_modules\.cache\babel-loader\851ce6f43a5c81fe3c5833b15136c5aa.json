{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\view\\auditold\\StrategyAuditRecord\\index.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\auditold\\StrategyAuditRecord\\index.js", "mtime": 1710321718000}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}