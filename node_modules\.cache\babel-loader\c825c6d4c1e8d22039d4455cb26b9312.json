{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\api\\auditold\\strategyAuditRecord.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\api\\auditold\\strategyAuditRecord.js", "mtime": 1750323771625}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}