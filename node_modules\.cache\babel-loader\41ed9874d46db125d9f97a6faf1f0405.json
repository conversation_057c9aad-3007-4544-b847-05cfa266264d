{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\view\\auditold\\StrategyProtocol\\index.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\auditold\\StrategyProtocol\\index.js", "mtime": 1706151048000}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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"}, null]}