<template>
  <div>
    <el-form
      class="searchBg"
      :model="searchForm"
      label-width="120px"
      :inline="true"
      @submit.native.prevent="handleSearch"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="工控协议">
            <el-select v-model="searchForm.isIndcontrolType" placeholder="请选择">
              <el-option label="工控协议" :value="1" />
              <el-option label="非工控协议" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="协议名称/描述">
            <el-input
              v-model="searchForm.nameOrDesc"
              maxlength="50"
              placeholder="请输入协议名称/描述"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="searchBtn">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button style="margin-left: 15px" @click="handleReset">清除</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="tableBg">
      <el-table
        :data="tableList.list || []"
        :loading="loading"
        row-key="id"
        style="width: 100%"
      >
        <el-table-column prop="protocolName" label="协议名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <i
              v-if="scope.row.isIndcontrolType == 1"
              class="el-icon-star-on"
              style="color: #e6a23c; margin-right: 5px"
            ></i>
            {{ scope.row.protocolName }}
          </template>
        </el-table-column>
        <el-table-column prop="protocolDesc" label="描述" show-overflow-tooltip />
        <el-table-column prop="tcpPort" label="TCP端口" />
        <el-table-column prop="udpPort" label="UDP端口" />
        <el-table-column prop="isIndcontrolType" label="工控协议" width="90">
          <template slot-scope="scope">
            {{ scope.row.isIndcontrolType == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
      </el-table>
      <el-row type="flex" justify="end" style="margin-top: 20px">
        <el-pagination
          :current-page="pagination.pageIndex"
          :page-size="pagination.pageSize"
          :total="tableList.total || 0"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onShowSizeChange"
          @current-change="handlePageChange"
        />
      </el-row>
    </div>
  </div>
</template>

<script>
import { tacticsPages } from '@api/auditold/strategyProtocol'

export default {
  name: 'StrategyProtocol',
  data() {
    return {
      tableList: {},
      searchForm: {
        isIndcontrolType: undefined,
        nameOrDesc: '',
      },
      queryValue: {},
      loading: false,
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
    }
  },
  watch: {
    queryValue: {
      handler() {
        this.getSourceData(true)
      },
      deep: true,
    },
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
              ...this.queryValue,
            }
          : {
              ...this.pagination,
              ...this.queryValue,
            }
        const res = await tacticsPages(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 条件查询
    handleSearch() {
      this.queryValue = { ...this.searchForm }
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 条件清除
    handleReset() {
      this.searchForm = {
        isIndcontrolType: undefined,
        nameOrDesc: '',
      }
      this.queryValue = {}
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },
  },
}
</script>

<style scoped lang="scss">
.searchBg {
  .el-form-item {
    margin-bottom: 0;
  }
}

.searchBtn {
  text-align: right;
}

.tableBg {
  .el-table {
    border: 1px solid #ebeef5;
  }
}
</style>
