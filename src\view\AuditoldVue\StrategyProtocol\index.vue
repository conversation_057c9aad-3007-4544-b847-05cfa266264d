<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input v-model="searchForm.nameOrDesc" clearable placeholder="协议名称/描述" prefix-icon="soc-icon-search" @change="handleSearch" />
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="toggleShow">
              高级搜索
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-select v-model="searchForm.isIndcontrolType" clearable placeholder="工控协议" @change="handleSearch">
                  <el-option label="工控协议" :value="1" />
                  <el-option label="非工控协议" :value="0" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-input v-model="searchForm.nameOrDesc" clearable placeholder="协议名称/描述" @change="handleSearch" />
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24" align="right">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">协议策略管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableList.rows || []"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column prop="protocolName" label="协议名称" show-overflow-tooltip>
            <template slot-scope="scope">
              <i v-if="scope.row.isIndcontrolType == 1" class="el-icon-star-on" style="color: #e6a23c; margin-right: 5px"></i>
              {{ scope.row.protocolName }}
            </template>
          </el-table-column>
          <el-table-column prop="protocolDesc" label="描述" show-overflow-tooltip />
          <el-table-column prop="tcpPort" label="TCP端口" />
          <el-table-column prop="udpPort" label="UDP端口" />
          <el-table-column prop="isIndcontrolType" label="工控协议" width="90">
            <template slot-scope="scope">
              {{ scope.row.isIndcontrolType == 1 ? '是' : '否' }}
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="tableList.total > 0"
        small
        background
        align="right"
        :current-page="pagination.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableList.total || 0"
        @size-change="onShowSizeChange"
        @current-change="handlePageChange"
      />
    </footer>
  </div>
</template>

<script>
import { tacticsPages } from '@api/auditold/strategyProtocol'

export default {
  name: 'StrategyProtocol',
  data() {
    return {
      isShow: false,
      tableList: {},
      searchForm: {
        isIndcontrolType: undefined,
        nameOrDesc: '',
      },
      queryValue: {},
      loading: false,
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
    }
  },
  watch: {
    queryValue: {
      handler() {
        this.getSourceData(true)
      },
      deep: true,
    },
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },

    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
              ...this.queryValue,
            }
          : {
              ...this.pagination,
              ...this.queryValue,
            }
        const res = await tacticsPages(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 条件查询
    handleSearch() {
      this.queryValue = { ...this.searchForm }
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 条件清除
    handleReset() {
      this.searchForm = {
        isIndcontrolType: undefined,
        nameOrDesc: '',
      }
      this.queryValue = {}
      this.pagination.pageIndex = 1
      this.getSourceData(true)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },
  },
}
</script>

<style scoped lang="scss">
// 样式已统一到全局布局类中
</style>
