{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\UpgradeManage\\components\\ProgressModal.vue", "mtime": 1750125804410}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}