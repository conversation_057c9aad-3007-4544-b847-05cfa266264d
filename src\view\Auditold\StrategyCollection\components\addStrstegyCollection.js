import React, {
    useState,
    useEffect,
    forwardRef,
    createRef,
    useImperativeHandle,
    useRef
  } from "react";
  import {
    message,
    Form,
    Row,
    Col,
    Spin,
    Button,
    Select,
    Input,
    Radio,
    Tooltip,
    Icon,
    Checkbox,
    Tag
  } from "antd";
  import SbrDrawer from "@/components/SbrDrawer";
  import {tacticsAdd,tacticsUpdate } from "../services";
  import styles from "../index.less";
  import ProtocolSelectModal from './protocolSelectModal'
  const protocolSelectModalRef = createRef();
  let Add = (props) => {
    const { refInstance, getSourceData } = props;
    const { getFieldDecorator, validateFields, getFieldsValue, setFields, setFieldsValue } = props.form;
    // 是否展示抽屉
    const [visible, setVisible] = useState(false);
    //标题
    const [title, setTitle] = useState('');
    // 查看数据
    const [record, setRecord] = useState({});
    const [loading, setLoading] = useState(false);

    // ip地址集合
    const [collectIpList, setCollectIpList] = useState([]);
    const [inputVisible, setInputVisible] = useState(false);
    const [allChecked, setAllChecked] = useState(true);
    const [inputValue, setInputValue] = useState('');
    
    // 协议集合
    const [protocolNames, setProtocolNames] = useState([]);
    const [protocolIds, setProtocolIds] = useState([]);
    useImperativeHandle(refInstance, () => ({
      showDrawer,
    }));
  
    useEffect(() => {
  
    }, []);
  
  
    // 打开抽屉
    const showDrawer = async (record = {}) => {
      setVisible(true);
      if (record.id) {
        setTitle('编辑采集策略');
        setTimeout(()=>{
          if (record.ipAddr === 'all') {
            setFieldsValue({ipAddr: null})
            setCollectIpList([])
            setAllChecked(true)
          } else {
            setCollectIpList(record.ipAddr.split(';'))
            setAllChecked(false)
          }
          if (record.protocolIds == 0) {
            setFieldsValue({protocolType: 0})
            setProtocolNames([])
            setProtocolIds([])
          } else {
            setFieldsValue({protocolType: 1})
            record.protocolNames && setProtocolNames(record.protocolNames.split(','))
            record.protocolIds && setProtocolIds(record.protocolIds.split(',').map(Number))
          }
          setFieldsValue({
            id: record.id,
            tacticsCode: record.tacticsCode,
            deviceIds: record.deviceIds
          })
        })
      }else{
        setTitle('新增采集策略');
        setAllChecked(true)
        setInputVisible(false)
        setInputValue('')
        setCollectIpList([])
        setFieldsValue({protocolType: 1})
        setProtocolNames([])
        setProtocolIds([])
      }
    };

   const validatorIP  =(rule, value, callback) =>{
      if (!allChecked) {
        if (collectIpList.length === 0 && !value) {
          callback(new Error("请输入IP"));
        }else if (collectIpList.length > 0) {
          let falg2 = true
          for (let i in collectIpList) {
            if (!checkIp(collectIpList[i])) {
              falg2 = false
              break
            }
          }
          if (!falg2) {
            callback(new Error("请输入正确格式的IP"));
          }
        }
        if (value) {
          if (!checkIp(value)) {
            callback(new Error("请输入正确格式的IP"));
          }
        }
        callback()
      }
      callback()
    }
    const checkIp = (value) => {
      let flag = true
      let IP = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      let integer = /^[1-9]\d*$/
      if (value.indexOf('-') !== -1) {
        let ipPath = false,ipFull = false
        if (integer.test(value.split("-")[1])) {
          let start = parseInt(value.split("-")[0].split('.')[3])
          let end = parseInt(value.split("-")[1])
          ipPath = end > start
        }
        if (IP.test(value.split("-")[0]) && IP.test(value.split("-")[1])) {
          let pre = ipToLong(value.split("-")[0])
          let next = ipToLong(value.split("-")[1])
          ipFull = next > pre
        }
        if (IP.test(value.split("-")[0]) && ((IP.test(value.split("-")[1]) && ipFull) || (integer.test(value.split("-")[1]) && ipPath))) {
          flag = true
        } else {
          flag = false
        }
      } else if (value.indexOf('/') !== -1) {
        if (!IP.test(value.split("/")[0]) || value.split("/")[1] < 0 || value.split("/")[1] > 32) {
          flag = false
        } else {
          flag = true
        }
      } else {
        if (!IP.test(value)) {
          flag = false
        } else {
          flag = true
        }
      }
      return flag
    }
    const ipToLong = (ip) => {
      let ipArr = ip.split('.')
      let a = parseInt(ipArr[0])
      let b = parseInt(ipArr[1])
      let c = parseInt(ipArr[2])
      let d = parseInt(ipArr[3])
      return  (a << 24) | (b << 16) | (c << 8) | d;
    }
    // ip地址全选改变
    const isAllIPChange = (e) => {
      setAllChecked(e.target.checked)
      if (e.target.checked) {
        setCollectIpList([])
        setFields({ipAddr: {errors: null}})
      }
    }
    // IP地址不为all
    const showInput = () => {
      setInputVisible(true)
    }
    // ip地址不为all事件
    const handleInputConfirm = (e) => {
      e.target.value && setCollectIpList([...collectIpList, e.target.value])
      setInputVisible(false)
      setInputValue(null)
    }
    // 移除ip地址
    const handleCloseTag = (tag) => {
      const splitArr = collectIpList.filter(item=> item!==tag)
      setCollectIpList(splitArr)
      if (splitArr.length==0) {
        setFields({collectIpList: { errors: [new Error("请输入IP")]}})
      } else {
        let falg = true
        for (let i in splitArr) {
          if (!checkIp(splitArr[i])){
            falg = false
            break
          }
        }
        if (!falg) {
          setFields({collectIpList: { errors: [new Error("请输入正确格式的IP")]}})
        } else {
          setFields({collectIpList: { errors: null}})
        }
      }
    }

    // 已选指定协议数据
    const saveData = (obj) => {
      setProtocolIds(obj.ids)
      setProtocolNames(obj.names)
    }
    // 协议切换
    const protocolChange = (e) => {
      if (e.target.value === 0) {
        setProtocolNames([])
        setProtocolIds([])
      }
    }
    // 打开协议弹框
    const handleSelectProtocols = (record = {}) => {
      protocolSelectModalRef.current.showModal(record)
    }
    //关闭抽屉
    const onDrawerClose = () => {
      setVisible(false);
    };
    // 提交
    const handleSubmit = (e) => {
      e.preventDefault();
      if (getFieldsValue().protocolType === 1 && protocolIds.length === 0) {
        message.warning('请选择协议!');
        return
      }
      validateFields(async (err, values) => {
        let data = {
          ipAddr: allChecked ? 'all' : collectIpList.join(';'),
          protocolIds: getFieldsValue().protocolType === 1 ? protocolIds.join(',') : 0,
          id: getFieldsValue().id,
          deviceIds: "",
          tacticsCode: ""
        }
        if (!err) {
          let res 
          if(title.indexOf('新增')!=-1){
            res = await tacticsAdd(data);
          }else{
            res = await tacticsUpdate(data);
          }      
          if (res.retcode == 0) {
            message.success('操作成功');
            getSourceData();
            onDrawerClose();
          } else {
            message.error(res.msg);
          }
          setLoading(false);
        }
      });
    };
  
    return (
      <>
      <SbrDrawer title={title} width={800} onClose={onDrawerClose} visible={visible}>
        <Form onSubmit={handleSubmit} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <div className="public-block">
            <Spin spinning={loading}>
              <Form.Item hidden>
                {getFieldDecorator("id")}
              </Form.Item>
              <Form.Item label="IP地址">
                {getFieldDecorator("collectIpList", {
                    rules: [
                      { validator: validatorIP },                 
                    ],
                })(
                    <div className={styles.multiIp}>
                      {collectIpList.map((item,index)=>{
                        return <Tag key={index} closable onClose={() => handleCloseTag(item)} visible={true}>
                                {item}
                              </Tag>
                      })}
                      {inputVisible && !allChecked? <Input placeholder={allChecked ? 'all' : '请输入ip地址'}
                                                            defaultValue={inputValue}
                                                            onPressEnter={handleInputConfirm}
                                                            onBlur={handleInputConfirm}></Input> : null}
                      {allChecked ? <Button disabled size="small">all</Button>:<Button size="small" onClick={showInput}>+ 添加</Button>}
                    </div>
                )}
                <Tooltip title={<span>示例：********** <br/> **********/24 <br/> **********-100 <br/> **********-************</span>}>
                      <Icon type="info-circle-o" style={{marginTop: '18px',position: 'absolute',right:-30}}/>
                    </Tooltip>
                    <Checkbox
                      style={{marginLeft: '20px',}}
                      checked={allChecked}
                      onChange={isAllIPChange}
                      >全选</Checkbox>
              </Form.Item>
              <Form.Item label="协议">
                {getFieldDecorator("protocolType", {
                  initialValue: 1,
                })(<Radio.Group onChange={protocolChange}>
                    <Radio value={0} key={"0"}>
                      全部协议
                    </Radio>
                    <Radio value={1} key={"1"}>
                      指定协议
                    </Radio>
                  </Radio.Group>)}
                  <a style={{display: getFieldsValue().protocolType ===1 ? '' : 'none'}} onClick={handleSelectProtocols}>已关联{protocolIds.length}个</a>
              </Form.Item>
            </Spin>
          </div>
          <Row type="flex" justify="center" className="drawer_btns">
            <Button htmlType="submit" type="primary">
              保存
            </Button>
            <Button className="spacing_btn" onClick={onDrawerClose}>
              关闭
            </Button>
          </Row>
        </Form>
      </SbrDrawer>
      <ProtocolSelectModal ref={protocolSelectModalRef} type='checkbox' defaultProtocolIds={protocolIds} defaultProtocolNames={protocolNames} saveData={saveData}></ProtocolSelectModal>
      </>
      );
  };
  Add = Form.create()(Add);
  export default forwardRef((props, ref) => <Add {...props} refInstance={ref} />);
  