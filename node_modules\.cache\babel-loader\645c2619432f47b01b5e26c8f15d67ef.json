{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\StrategyManage\\components\\DeviceComponent.vue", "mtime": 1750125455390}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}