{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue?vue&type=template&id=36a803c3&scoped=true&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue", "mtime": 1750387212981}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745219693993}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}