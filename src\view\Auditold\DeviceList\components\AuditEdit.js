import React, {Component, Fragment} from 'react';
import {Modal, Form, Input,Select,TreeSelect} from 'antd';


const FormItem = Form.Item;
const Option = Select.Option;
const { TreeNode } = TreeSelect;

@Form.create()
export default class EditConfigModal extends Component {

    constructor(props) {
        super(props);
        this.state = {
            visible: false
        }
    }

    onShow = () => {
        this.setState({
            visible: true
        })
    }


    onHide = () =>{
        this.setState({
            visible: false
        })
    }

    handleCancelClick = () => {
        this.setState({
            visible: false
        })
    }
    renderTreeNodes = (data) => {
      return data.map((item) => {
        if (item.childList) {
          return (
            <TreeNode title={item.groupName} key={item.id} value={item.id}>
              {this.renderTreeNodes(item.childList)}
            </TreeNode>
          );
        }
        return <TreeNode title={item.groupName} key={item.id} value={item.id} />;
      });
    };

    render() {
        const {visible} = this.state;
        const {form:{
                  getFieldDecorator
               },
               currentConfig:currentPageInfo,saveOnClick} = this.props;

        return (
            <Fragment>
                <Modal
                    visible={visible}
                    title='修改配置'
                    okText="确定"
                    destroyOnClose
                    centered
                    onCancel={this.handleCancelClick}
                    onOk={saveOnClick.bind(this,this.props.form)}
                    width={650}
                    maskClosable={false}
                >
                    <Form>
                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="设备重要程度：">
                            {getFieldDecorator('importance', {
                                initialValue: currentPageInfo !== null ? (currentPageInfo.importance !== null ? String(currentPageInfo.importance) : "") : ""
                            })(
                                <Select>
                                    <Option value="1">高</Option>
                                    <Option value="2">中</Option>
                                    <Option value="3">低</Option>
                                </Select>
                            )}
                        </FormItem>
                        <FormItem labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} label="设备分组:">
                          {getFieldDecorator("group_id", {
                            initialValue: currentPageInfo !== null ? (currentPageInfo.group_id !== null ? (currentPageInfo.group_id) : "") : ""
                          })(
                              <TreeSelect
                              style={{ width: '100%' }}
                              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                              placeholder="请选择分组"
                            >
                              {this.renderTreeNodes(this.props.groupData)}
                            </TreeSelect>
                          )}
                        </FormItem>
                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="所处位置："
                        >
                            {getFieldDecorator('position', {
                                initialValue: currentPageInfo !== null ? currentPageInfo.position : null
                            })(
                                <Input placeholder='请输入所处位置'/>
                            )}
                        </FormItem>

                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="安全责任人："
                        >
                            {getFieldDecorator('person_liable', {
                                initialValue: currentPageInfo !== null ? currentPageInfo.person_liable : null
                            })(
                                <Input placeholder='请输入安全责任人'/>
                            )}
                        </FormItem>

                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="电话："
                        >
                            {getFieldDecorator('contact', {
                                initialValue: currentPageInfo !== null ? currentPageInfo.contact  : null
                            })(
                                <Input placeholder='请输入电话'/>
                            )}
                        </FormItem>

                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="管理IP："
                        >
                            {getFieldDecorator('ip', {
                                initialValue: currentPageInfo !== null ? currentPageInfo.ip : ""
                            })(
                                <Input disabled/>
                            )}
                        </FormItem>

                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="版本："
                        >
                            {getFieldDecorator('version', {
                                initialValue: currentPageInfo !== null ? currentPageInfo.version : ""
                            })(
                                <Input disabled/>
                            )}
                        </FormItem>

                        <FormItem
                            labelCol={{span: 6}}
                            wrapperCol={{span: 16}}
                            label="硬件型号："
                        >
                            {getFieldDecorator('model_number', {
                                initialValue: currentPageInfo !== null ? (currentPageInfo.model_number !== null ? currentPageInfo.model_number : "") : ""
                            })(
                                <Input disabled/>
                            )}
                        </FormItem>
                    </Form>
                </Modal>
            </Fragment>
        )
    }
}