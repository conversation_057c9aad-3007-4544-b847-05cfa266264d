{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\workspace\\smp\\src\\view\\auditold\\DeviceList\\index.js", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\auditold\\DeviceList\\index.js", "mtime": 1726724920000}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\eslint-loader\\index.js", "mtime": 1745219686848}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}