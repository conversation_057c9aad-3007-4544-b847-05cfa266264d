{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyFilter\\components\\addStrstegyFilter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyFilter\\components\\addStrstegyFilter.vue", "mtime": 1750325586761}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}