{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ServiceSet\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\ServiceSet\\index.vue", "mtime": 1750149353544}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}