<template>
  <el-dialog
    :visible.sync="visible"
    title="添加设备"
    width="600px"
    :close-on-click-modal="false"
    @close="onHide"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="设备名称" prop="notes">
        <el-input v-model="form.notes" placeholder="请输入设备名称" />
      </el-form-item>
      <el-form-item label="设备IP" prop="ip">
        <el-input v-model="form.ip" placeholder="请输入设备IP" />
      </el-form-item>
      <el-form-item label="重要性" prop="importance">
        <el-select v-model="form.importance" placeholder="请选择重要性">
          <el-option label="高" :value="1" />
          <el-option label="中" :value="2" />
          <el-option label="低" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备分组" prop="group_id">
        <el-select v-model="form.group_id" placeholder="请选择设备分组">
          <el-option
            v-for="group in groupData"
            :key="group.id"
            :label="group.groupName"
            :value="group.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="位置" prop="position">
        <el-input v-model="form.position" placeholder="请输入位置" />
      </el-form-item>
      <el-form-item label="负责人" prop="person_liable">
        <el-input v-model="form.person_liable" placeholder="请输入负责人" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="form.contact" placeholder="请输入联系方式" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onHide">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddDeviceModal',
  props: {
    groupData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        notes: '',
        ip: '',
        importance: undefined,
        group_id: undefined,
        position: '',
        person_liable: '',
        contact: '',
      },
      rules: {
        notes: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/, message: '请输入汉字、字母、数字、短横线或下划线', trigger: 'blur' },
        ],
        ip: [
          { required: true, message: '请输入设备IP', trigger: 'blur' },
          { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入正确的IP地址', trigger: 'blur' },
        ],
        importance: [
          { required: true, message: '请选择重要性', trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    onShow() {
      this.visible = true
      this.resetForm()
    },

    onHide() {
      this.visible = false
      this.loading = false
      this.resetForm()
    },

    resetForm() {
      this.form = {
        notes: '',
        ip: '',
        importance: undefined,
        group_id: undefined,
        position: '',
        person_liable: '',
        contact: '',
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('save', this.form)
          setTimeout(() => {
            this.loading = false
            this.onHide()
          }, 1000)
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
