{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\protocolSelectModal.vue", "mtime": 1750325921339}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}