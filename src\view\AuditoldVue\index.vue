<template>
  <div class="auditold-vue-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="设备列表" name="deviceList">
        <device-list />
      </el-tab-pane>
      <el-tab-pane label="策略审计记录" name="strategyAuditRecord">
        <strategy-audit-record />
      </el-tab-pane>
      <el-tab-pane label="策略采集" name="strategyCollection">
        <strategy-collection />
      </el-tab-pane>
      <el-tab-pane label="策略过滤" name="strategyFilter">
        <strategy-filter />
      </el-tab-pane>
      <el-tab-pane label="协议策略" name="strategyProtocol">
        <strategy-protocol />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DeviceList from './DeviceList/index.vue'
import StrategyAuditRecord from './StrategyAuditRecord/index.vue'
import StrategyCollection from './StrategyCollection/index.vue'
import StrategyFilter from './StrategyFilter/index.vue'
import StrategyProtocol from './StrategyProtocol/index.vue'

export default {
  name: 'AuditoldVue',
  components: {
    DeviceList,
    StrategyAuditRecord,
    StrategyCollection,
    StrategyFilter,
    StrategyProtocol,
  },
  data() {
    return {
      activeTab: 'deviceList',
    }
  },
}
</script>

<style scoped lang="scss">
.auditold-vue-container {
  padding: 20px;
  
  .el-tabs {
    .el-tab-pane {
      padding-top: 20px;
    }
  }
}
</style>
