<template>
  <el-dialog :visible.sync="visible" title="选择协议" width="800px" :close-on-click-modal="false" center @close="handleCancel">
    <div class="protocol-select-container">
      <div class="table-container">
        <el-table
          ref="protocolTable"
          v-loading="loading"
          :data="tableList.rows || []"
          row-key="id"
          @selection-change="onSelectionChange"
          @select="onSelect"
          @select-all="onSelectAll"
          style="width: 600px"
        >
          <el-table-column v-if="type === 'checkbox'" type="selection" width="55" :selectable="() => true" />
          <el-table-column v-if="type === 'radio'" label="选择" width="55">
            <template slot-scope="scope">
              <el-radio v-model="selectedRadioValue" @change="handleRadioChange(scope.row)">
                &nbsp;
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column label="序号" width="60">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="protocolName" label="协议名称" />
          <el-table-column prop="isIndcontrolType" label="工控协议" width="80">
            <template slot-scope="scope">
              {{ scope.row.isIndcontrolType == 1 ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="protocolDesc" label="描述" show-overflow-tooltip />
        </el-table>
        <el-pagination
          v-if="tableList.total > 0"
          small
          background
          :current-page="pagination.pageIndex"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableList.total || 0"
          @size-change="onShowSizeChange"
          @current-change="handlePageChange"
          style="margin-top: 20px"
        />
      </div>
      <div class="selected-container">
        <div class="selected-tags">
          <el-tag v-for="(item, index) in selectedRowData" :key="index" closable @close="removeTag(item)">
            {{ item }}
          </el-tag>
        </div>
        <div v-if="selectedRowData.length > 0" class="selected-count">(已选{{ selectedRowData.length }})</div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="saveOnClick">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { tacticsPages } from '@api/auditold/strategyProtocol'

export default {
  name: 'ProtocolSelectModal',
  props: {
    type: {
      type: String,
      default: 'checkbox', // 'checkbox' or 'radio'
    },
    defaultProtocolIds: {
      type: Array,
      default: () => [],
    },
    defaultProtocolNames: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      tableList: {},
      selectedRowKeys: [],
      selectedRowData: [],
      selectedRadioValue: null,
      needSyncSelection: false, // 标志位：是否需要同步选中状态
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    async showModal() {
      this.visible = true

      // 设置默认选中的协议
      if (this.defaultProtocolIds && this.defaultProtocolIds.length > 0) {
        this.selectedRowKeys = [...this.defaultProtocolIds]

        // 单选模式设置radio值
        if (this.type === 'radio') {
          this.selectedRadioValue = this.defaultProtocolIds[0]
        }
      } else {
        this.selectedRowKeys = []
        this.selectedRadioValue = null
      }

      if (this.defaultProtocolNames && this.defaultProtocolNames.length > 0) {
        this.selectedRowData = [...this.defaultProtocolNames]
      } else {
        this.selectedRowData = []
      }

      // 设置需要同步选中状态的标志
      this.needSyncSelection = true

      // 如果表格数据已经存在，立即同步选中状态
      if (this.tableList.rows && this.tableList.rows.length > 0) {
        this.$nextTick(() => {
          this.syncTableSelection()
          this.needSyncSelection = false
        })
      }
      // 如果表格数据不存在，等待数据加载完成后会自动同步（在getSourceData中）
    },

    // 同步表格选中状态
    syncTableSelection() {
      const table = this.$refs.protocolTable
      if (!table) return

      const tableData = this.tableList.rows || []

      if (this.type === 'checkbox') {
        // 多选模式：设置所有选中的行
        table.clearSelection()
        tableData.forEach((row) => {
          if (this.selectedRowKeys.includes(row.id)) {
            table.toggleRowSelection(row, true)
          }
        })
      } else {
        // 单选模式：radio值已经在showModal中设置
        // 这里不需要额外处理，因为radio是通过v-model绑定的
      }
    },

    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
            }
          : {
              ...this.pagination,
            }
        const res = await tacticsPages(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false

          // 数据加载完成后同步表格选中状态
          this.$nextTick(() => {
            if (this.needSyncSelection || this.selectedRowKeys.length > 0) {
              this.syncTableSelection()
              this.needSyncSelection = false
            }
          })
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('获取协议列表失败:', err)
        this.loading = false
      }
    },

    onSelectionChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)

      // 同步更新右侧显示的协议名称标签
      if (this.type === 'checkbox') {
        // 多选模式：更新为当前选中的所有协议名称
        this.selectedRowData = selectedRows.map((item) => item.protocolName)
      } else {
        // 单选模式：只保留最后选中的协议名称
        this.selectedRowData = selectedRows.length > 0 ? [selectedRows[selectedRows.length - 1].protocolName] : []
      }
    },

    onSelect(selectedRow, checked) {
      if (this.type === 'checkbox') {
        if (checked && this.selectedRowData.indexOf(selectedRow.protocolName) === -1) {
          this.selectedRowData.push(selectedRow.protocolName)
        } else if (!checked) {
          const index = this.selectedRowData.indexOf(selectedRow.protocolName)
          if (index > -1) {
            this.selectedRowData.splice(index, 1)
          }
        }
      } else {
        // radio mode
        this.selectedRowData = [selectedRow.protocolName]
      }
    },

    // 处理单选模式的选择
    handleRadioChange(selectedRow) {
      this.selectedRowKeys = [selectedRow.id]
      this.selectedRowData = [selectedRow.protocolName]
      this.selectedRadioValue = selectedRow.id
    },

    onSelectAll(checked, _, operRow) {
      if (checked) {
        operRow.forEach((item) => {
          if (this.selectedRowData.indexOf(item.protocolName) === -1) {
            this.selectedRowData.push(item.protocolName)
          }
        })
      } else {
        for (let i = operRow.length - 1; i >= 0; i--) {
          const index = this.selectedRowData.indexOf(operRow[i].protocolName)
          if (index > -1) {
            this.selectedRowData.splice(index, 1)
          }
        }
      }
    },

    removeTag(tag) {
      const index = this.selectedRowData.indexOf(tag)
      if (index > -1) {
        this.selectedRowData.splice(index, 1)

        // 同时移除对应的选中行
        const tableData = this.tableList.rows || []
        const targetRow = tableData.find((item) => item.protocolName === tag)
        if (targetRow) {
          const keyIndex = this.selectedRowKeys.indexOf(targetRow.id)
          if (keyIndex > -1) {
            this.selectedRowKeys.splice(keyIndex, 1)
          }

          // 更新表格选中状态
          this.$nextTick(() => {
            if (this.type === 'checkbox') {
              // 多选模式：通过toggleRowSelection取消选中
              const table = this.$refs.protocolTable
              if (table) {
                table.toggleRowSelection(targetRow, false)
              }
            } else {
              // 单选模式：清空radio选中状态
              if (this.selectedRadioValue === targetRow.id) {
                this.selectedRadioValue = null
              }
            }
          })
        }
      }
    },

    handleCancel() {
      this.visible = false
    },

    saveOnClick() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择协议！')
        return
      }

      this.$emit('saveData', {
        ids: this.selectedRowKeys,
        names: this.selectedRowData,
      })
      this.visible = false
    },

    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog .el-dialog__body {
    max-height: 600px;
  }
}
.protocol-select-container {
  display: flex;
  gap: 20px;

  .table-container {
    flex: 1;
  }

  .selected-container {
    width: 150px;
    border: 1px solid #ebeef5;
    .selected-tags {
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-height: 400px;
      overflow-y: auto;

      .el-tag {
        margin: 0;
      }
    }

    .selected-count {
      margin-top: 10px;
      color: #666;
      font-size: 12px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
