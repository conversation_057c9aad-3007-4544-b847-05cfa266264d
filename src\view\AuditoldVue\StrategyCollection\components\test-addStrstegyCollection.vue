<template>
  <div class="test-container">
    <h2>addStrstegyCollection 组件功能测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>功能验证</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>测试按钮</h3>
          <el-button type="primary" @click="testNewAdd">测试新增功能</el-button>
          <el-button type="success" @click="testEdit">测试编辑功能</el-button>
          <el-button type="info" @click="testComplexEdit">测试复杂编辑</el-button>
        </el-col>
        
        <el-col :span="12">
          <h3>功能检查清单</h3>
          <el-checkbox-group v-model="checkedFeatures">
            <el-checkbox label="IP地址管理">IP地址标签式管理</el-checkbox>
            <el-checkbox label="IP格式验证">多种IP格式验证</el-checkbox>
            <el-checkbox label="全选功能">IP地址全选功能</el-checkbox>
            <el-checkbox label="协议选择">协议类型选择</el-checkbox>
            <el-checkbox label="协议模态框">协议选择模态框</el-checkbox>
            <el-checkbox label="动态输入">动态IP输入功能</el-checkbox>
            <el-checkbox label="API集成">API集成和提交</el-checkbox>
            <el-checkbox label="工具提示">工具提示说明</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </el-card>

    <el-card>
      <div slot="header">
        <span>功能对比表</span>
      </div>
      
      <el-table :data="comparisonData" style="width: 100%">
        <el-table-column prop="feature" label="功能项" width="200" />
        <el-table-column prop="react" label="React版本" />
        <el-table-column prop="vue" label="Vue版本" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '一致' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>IP地址格式测试</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>支持的IP格式：</h4>
          <ul>
            <li>单个IP：***********</li>
            <li>CIDR格式：***********/24</li>
            <li>范围格式：***********-100</li>
            <li>完整范围：***********-***********00</li>
          </ul>
        </el-col>
        
        <el-col :span="12">
          <h4>测试IP地址：</h4>
          <el-input
            v-model="testIp"
            placeholder="输入IP地址进行格式测试"
            @change="validateTestIp"
          />
          <div style="margin-top: 10px;">
            <el-tag :type="ipValidResult.valid ? 'success' : 'danger'">
              {{ ipValidResult.message }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 测试组件 -->
    <add-strategy-collection ref="addStrategyCollectionRef" @getSourceData="handleGetSourceData" />
  </div>
</template>

<script>
import AddStrategyCollection from './addStrstegyCollection'

export default {
  name: 'TestAddStrategyCollection',
  components: {
    AddStrategyCollection,
  },
  data() {
    return {
      checkedFeatures: [],
      testIp: '',
      ipValidResult: { valid: false, message: '请输入IP地址' },
      comparisonData: [
        {
          feature: 'IP地址管理',
          react: '标签式动态管理',
          vue: '标签式动态管理',
          status: '一致'
        },
        {
          feature: 'IP格式验证',
          react: '4种格式支持',
          vue: '4种格式支持',
          status: '一致'
        },
        {
          feature: '全选功能',
          react: '全选/指定切换',
          vue: '全选/指定切换',
          status: '一致'
        },
        {
          feature: '协议选择',
          react: '全部/指定协议',
          vue: '全部/指定协议',
          status: '一致'
        },
        {
          feature: '协议模态框',
          react: 'protocolSelectModal',
          vue: 'protocolSelectModal',
          status: '一致'
        },
        {
          feature: '动态输入',
          react: '动态添加/删除IP',
          vue: '动态添加/删除IP',
          status: '一致'
        },
        {
          feature: 'API集成',
          react: 'tacticsAdd/Update',
          vue: 'tacticsAdd/Update',
          status: '一致'
        },
        {
          feature: '工具提示',
          react: 'IP格式说明',
          vue: 'IP格式说明',
          status: '一致'
        }
      ]
    }
  },
  methods: {
    testNewAdd() {
      this.$refs.addStrategyCollectionRef.showDrawer()
      this.$message.success('已打开新增采集策略弹框')
    },

    testEdit() {
      const mockData = {
        id: '123',
        tacticsCode: 'TC001',
        collectIpList: '***********;***********/24',
        protocolType: 1,
        protocolIds: '1,2',
        protocolNames: 'HTTP,FTP',
        deviceIds: '',
      }
      this.$refs.addStrategyCollectionRef.showDrawer(mockData)
      this.$message.success('已打开编辑采集策略弹框')
    },

    testComplexEdit() {
      const mockData = {
        id: '456',
        tacticsCode: 'TC002',
        collectIpList: 'all',
        protocolType: 0,
        protocolIds: '',
        protocolNames: '',
        deviceIds: '',
      }
      this.$refs.addStrategyCollectionRef.showDrawer(mockData)
      this.$message.success('已打开复杂编辑采集策略弹框（全选模式）')
    },

    handleGetSourceData() {
      this.$message.success('数据刷新回调触发成功')
    },

    validateTestIp() {
      if (!this.testIp) {
        this.ipValidResult = { valid: false, message: '请输入IP地址' }
        return
      }

      const ipRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const cidrRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\/([0-9]|[1-2][0-9]|3[0-2])$/
      const rangeRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])-(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const fullRangeRegex = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])-(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/

      if (ipRegex.test(this.testIp)) {
        this.ipValidResult = { valid: true, message: '单个IP格式正确' }
      } else if (cidrRegex.test(this.testIp)) {
        this.ipValidResult = { valid: true, message: 'CIDR格式正确' }
      } else if (rangeRegex.test(this.testIp)) {
        this.ipValidResult = { valid: true, message: '范围格式正确' }
      } else if (fullRangeRegex.test(this.testIp)) {
        this.ipValidResult = { valid: true, message: '完整范围格式正确' }
      } else {
        this.ipValidResult = { valid: false, message: 'IP格式不正确' }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  
  h2 {
    color: #409eff;
    margin-bottom: 20px;
  }
  
  h3, h4 {
    color: #666;
    margin-bottom: 15px;
  }
  
  .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  .el-checkbox {
    display: block;
    margin-bottom: 8px;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
    }
  }
}
</style>
