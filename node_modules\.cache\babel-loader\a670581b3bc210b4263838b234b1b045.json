{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\baseline\\BaselineTemplate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\baseline\\BaselineTemplate.vue", "mtime": 1749027599673}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}