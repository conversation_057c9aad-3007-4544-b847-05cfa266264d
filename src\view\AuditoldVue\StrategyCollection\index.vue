<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-button">
          <el-button type="primary" @click="handleAdd()">新建采集策略</el-button>
          <el-button type="primary" @click="batchDistribute()">批量下发</el-button>
          <el-button type="danger" @click="batchDeleteProtocol()">批量删除</el-button>
        </section>
      </section>
    </header>

    <main class="table-body">
      <section class="table-body-header">
        <h2 class="table-body-title">策略采集管理</h2>
      </section>
      <section v-loading="loading" class="table-body-main">
        <el-table
          :data="tableList.rows || []"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="onSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" width="80" align="center">
            <template slot-scope="scope">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="ipAddr" label="IP地址" show-overflow-tooltip />
          <el-table-column prop="protocolNames" label="协议" show-overflow-tooltip />
          <el-table-column prop="lastDistributeTime" label="上次下发时间">
            <template slot-scope="scope">
              {{
                scope.row.lastDistributeTime
                  ? formatTime(scope.row.lastDistributeTime)
                  : ''
              }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceNotes" label="应用设备" show-overflow-tooltip />
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button class="el-button--blue" type="text" @click="handleAdd(scope.row)">编辑</el-button>
                <el-button class="el-button--blue" type="text" @click="deleteProtocol(scope.row)">删除</el-button>
                <el-button class="el-button--blue" type="text" @click="distribute(scope.row)">下发</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="tableList.total > 0"
        small
        background
        align="right"
        :current-page="pagination.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableList.total || 0"
        @size-change="onShowSizeChange"
        @current-change="handlePageChange"
      />
    </footer>

    <device-component
      ref="deviceRef"
      :type-button="type"
      :tactics-distrib="tacticsDistrib"
      @getSourceData="getSourceData"
    />
    <add-strategy-collection ref="addStrategyCollectionRef" @getSourceData="getSourceData" />
  </div>
</template>

<script>
import { tacticsSearch, tacticsDelete, tacticsDistrib } from '@api/auditold/strategyCollection'
import DeviceComponent from './components/deviceComponent'
import AddStrategyCollection from './components/addStrstegyCollection'
import moment from 'moment'

export default {
  name: 'StrategyCollection',
  components: {
    DeviceComponent,
    AddStrategyCollection,
  },
  data() {
    return {
      tableList: {},
      type: '',
      loading: false,
      selectedRowKeys: [],
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
      tacticsDistrib,
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
            }
          : {
              ...this.pagination,
            }
        const res = await tacticsSearch(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
          this.selectedRowKeys = []
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 新建/编辑
    handleAdd(record = {}) {
      this.$refs.addStrategyCollectionRef.showDrawer(record)
    },

    // 删除
    deleteProtocol(record = {}) {
      this.$confirm('确定要删除选中采集策略吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(async () => {
          try {
            const res = await tacticsDelete({ idList: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.calcPageNo(this.tableList, 1)
              this.getSourceData()
            } else {
              this.$message.error(res.msg)
            }
          } catch (err) {
            console.error('删除失败:', err)
          }
        })
        .catch(() => {
          console.log('取消删除')
        })
    },

    // 批量删除
    batchDeleteProtocol() {
      if (this.selectedRowKeys.length) {
        this.$confirm('确定要删除选中采集策略吗?删除后不可恢复', '删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
        })
          .then(async () => {
            try {
              const res = await tacticsDelete({ idList: this.selectedRowKeys.join(',') })
              if (res.retcode === 0) {
                this.$message.success('删除成功')
                this.calcPageNo(this.tableList, this.selectedRowKeys.length)
                this.getSourceData()
              } else {
                this.$message.error(res.msg)
              }
            } catch (err) {
              console.error('批量删除失败:', err)
            }
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 下发
    async distribute(record = {}) {
      this.type = '1'
      this.$refs.deviceRef.showDrawer(record, [record.id])
    },

    // 批量下发
    async batchDistribute() {
      this.type = '1'
      if (this.selectedRowKeys.length) {
        this.$refs.deviceRef.showDrawer({}, this.selectedRowKeys)
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 选择改变
    onSelectionChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },

    // 计算页码
    calcPageNo(tableList, deleteCount) {
      const { pageIndex, pageSize } = this.pagination
      const total = tableList.total || 0
      const currentPageCount = tableList.rows ? tableList.rows.length : 0

      if (currentPageCount <= deleteCount && pageIndex > 1) {
        this.pagination.pageIndex = pageIndex - 1
      }
    },

    // 格式化时间
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : ''
    },
  },
}
</script>

<style scoped lang="scss">
.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
