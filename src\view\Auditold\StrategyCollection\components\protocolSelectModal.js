import React, {
    useState,
    useEffect,
    forwardRef,
    useImperativeHandle,
    createRef
  } from "react";
  import {
    message,
    Form,
    Row,
    Col,
    Spin,
    Button,
    Select,
    Input,
    Radio,
    Tooltip,
    Icon,
    Checkbox,
    Tag,
    Modal
  } from "antd";
  import { tacticsPages } from "../../StrategyProtocol/services";
  import SbrTable from "@/components/SbrTable";
  import styles from "../index.less";
  const tableRef = createRef();
  let SelectPro = (props) => {
    const { refInstance, saveData } = props;
    const { getFieldDecorator, validateFields, setFieldsValue } = props.form;
    // 是否展示弹框
    const [visible, setVisible] = useState(false);
    //标题
    const [title, setTitle] = useState('');
    // 查看数据
    const [loading, setLoading] = useState(false);
    const [ruleT, setRuleT] = useState(1);
    // 列表数据
    const [tableList, setTableList] = useState([]); 

    // 编辑数据
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRowData, setSelectedRowData] = useState([]);
    // const [multipleSelection, setMultipleSelection] = useState([]);

    useImperativeHandle(refInstance, () => ({
      showModal,
    }));
    useEffect(() => {
      getSourceData(true)
    }, []);
  
    const getSourceData = async(isSearch=false) =>{
      try {
        setLoading(true);
        const res = await tacticsPages(isSearch?{
          pageIndex: 1,
          pageSize: 10,
        }:{
          ...tableRef?.current?.getValue(),
        });
        if (res.retcode == 0) {
          setTableList(res.data);
          setLoading(false);
        } else {
          message.error(res.msg);
        }
      } catch (err) {
        console.log(err);
      }
    }
  
    // 打开弹框
    const showModal = async (record = {}) => {
      // getSourceData(true)
      setVisible(true);
      setTitle('选择协议');
      
      let protocolids = [],protocolName = []
      // if (Object.keys(props.defaultProtocol).length > 0) {
      //   props.defaultProtocol.map(item=>{
      //     protocolids = item.protocolId.split(',').map(Number)
      //     protocolName = item.protocolName.split(',')
      //   })
      //   setSelectedRowKeys(protocolids)
      //   setSelectedRowData(protocolName)
      //   setMultipleSelection(protocolids)
      // } else {
      //   setSelectedRowKeys([])
      //   setSelectedRowData([])
      //   setMultipleSelection([])
      // }
      if (props.defaultProtocolIds && props.defaultProtocolIds.length>0) {
        protocolids = props.defaultProtocolIds
        setSelectedRowKeys(protocolids)
      } else {
        setSelectedRowKeys([])
      }
      if (props.defaultProtocolNames && props.defaultProtocolNames.length>0) {
        protocolName = props.defaultProtocolNames
        setSelectedRowData(protocolName)
      } else {
        setSelectedRowData([])
      }
    }

    //关闭弹框
    const handleCancelClick = () => {
      setVisible(false);
    }
    // 确定
    const saveOnClick = () => {
      if (selectedRowKeys.length === 0) {
        message.warning('请选择协议！')
      } else {
        props.saveData({ids:selectedRowKeys,names: selectedRowData})
        setVisible(false);
      }
    }
  
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      type: props.type,
      onChange: (selectedRowKeys, selectedRows) => {
        // let arr = selectedRowData
        // selectedRows.map(item=>{
        //   selectedRowKeys.map(val=>{
        //     if (item.id === val && arr.indexOf(item.protocolName)===-1) {
        //       arr.push(item.protocolName)
        //     }
        //   })
        // })
        setSelectedRowKeys(selectedRowKeys);
        // setSelectedRowData(arr)
        // setMultipleSelection(selectedRows)
      },
      onSelect: (selectedRow, checked) => {
        if (props.type == 'checkbox') {
          if (checked && selectedRowData.indexOf(selectedRow.protocolName) === -1) {
            selectedRowData.push(selectedRow.protocolName)
          } else if (!checked) {
            let index = selectedRowData.indexOf(selectedRow.protocolName)
            index > -1 && selectedRowData.splice(index,1)
          }
          setSelectedRowData(selectedRowData)
        } else {
          setSelectedRowData([selectedRow.protocolName])
        }
      },
      onSelectAll: (checked, curRow, operRow) => {
        if (checked) {
          operRow.forEach(item=>{
            if (selectedRowData.indexOf(item.protocolName) === -1) {
              selectedRowData.push(item.protocolName)
            }
          })
        } else {
          for(let i=operRow.length-1; i>=0; i--){
            let index = selectedRowData.indexOf(operRow[i].protocolName) 
            index>-1 && selectedRowData.splice(index,1)
          }
        }
        setSelectedRowData(selectedRowData)
      }
    };
    // 移除已选协议
    // const removetag = (tag) => {
    //   const splitArr = selectedRowData.filter(item=> item!==tag)
    //   setSelectedRowData(splitArr)
    // }
    const columns = [
      {
        title: "序号",
        key: "key",
        dataIndex: "key",
        width: 50,
        render: (text, record, index) => {
          return `${index + 1}`;
        },
      },{
        title: "协议名称",
        key: "protocolName",
        dataIndex: "protocolName",
      },{
        title: "工控协议",
        key: "isIndcontrolType",
        dataIndex: "isIndcontrolType",
        render: (text, record) => {
          return record.isIndcontrolType == 1 ? "是" : "否";
        },
      },{
        title: "描述",
        key: "protocolDesc",
        dataIndex: "protocolDesc",
      }
    ]
    return <Modal visible={visible}
                  title={title}
                  okText="确定"
                  destroyOnClose
                  centered
                  width={800}
                  onCancel={handleCancelClick}
                  maskClosable={false}
                  footer={[
                    <Button key="back" onClick={handleCancelClick}>
                      取消
                    </Button>,
                    <Button key="submit" type="primary" loading={loading} onClick={saveOnClick}>
                      确认
                    </Button>
                  ]}>
                    <div className={styles.multiBox}>
                      <div className={`tableBg`} style={{width:'600px'}}>
                        <SbrTable
                          columns={columns}
                          scroll={false}
                          ref={tableRef}
                          tableList={tableList}
                          getSourceData={getSourceData}
                          style={{ wordWrap: "break-word", wordBreak: "break-all" }}
                          rowKey={(record) => record.id}
                          loading={loading}
                          rowSelection={rowSelection}
                        />
                      </div>
                      <div className={styles.right}>
                        {selectedRowData.map((item,index)=>{
                          return <Tag>{item}</Tag>
                        })}
                        {selectedRowData.length >0 ? <a>(已选{selectedRowData.length})</a> : null}
                      </div>
                    </div>
                  </Modal>
  };
  SelectPro = Form.create()(SelectPro);
  export default forwardRef((props, ref) => <SelectPro {...props} refInstance={ref} />);
  