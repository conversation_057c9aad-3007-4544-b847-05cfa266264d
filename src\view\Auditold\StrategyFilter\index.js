import React, { useState, useEffect, createRef } from "react";
import { message, Modal, Button, Form, Row, Col, Input, Select, DatePicker } from "antd";
import moment from "moment";
import SbrTable from "@/components/SbrTable";
import DeviceComponent from "./components/deviceComponent";
import AddStrstegyFilter from './components/addStrstegyFilter';
import {tacticsSearch,  tacticsDelete, tacticsDistrib } from "./services";
const tableRef = createRef();
const deviceRef = createRef();
const addStrstegyFilterRef = createRef();
const List = (props) => {
  // 列表数据
  const [tableList, setTableList] = useState([]);
  //  设备组件传参类型
  const [type, setType] = useState('');
  // 查询条件
  // const [queryValue, setQueryValue] = useState({});
  const [loading, setLoading] = useState(false);
  // 编辑数据
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  useEffect(
    () => {
      getSourceData(true);
    },
    []
  );

  // 查询列表
  const getSourceData = async (isSearch=false) => {
    try {
      setLoading(true);
      const res = await tacticsSearch(isSearch?{
        pageIndex: 1,
        pageSize: 10,
        // ...queryValue,
      }:{
        ...tableRef?.current?.getValue(),
        // ...queryValue,
      });
      if (res.retcode == 0) {
        setTableList(res.data);
        setLoading(false);
        setSelectedRowKeys([])
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      console.log(err);
    }
  };
  // 新建/编辑
  const handleAdd = (record = {}) =>{
    addStrstegyFilterRef.current.showDrawer(record)
  }
  // 删除
  const deleteProtocol = (record={}) => {
      Modal.confirm({
        title: "删除",
        content: "确定要删除选中过滤策略吗?删除后不可恢复",
        centered: true,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            const res = await tacticsDelete({ ids: record.id });
            if (res.retcode == 0) {
              message.success("删除成功");
              tableRef.current.calcPageNo(tableList, 1)
              getSourceData();
            } else {
              message.error(res.msg);
            }
          } catch (err) {
            console.log(err);
          }
        },
      });
  };
  // 批量删除
  const batchDeleteProtocol = (record={}) => {
    if (selectedRowKeys.length) {
      Modal.confirm({
        title: "删除",
        content: "确定要删除选中过滤策略吗?删除后不可恢复",
        centered: true,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            const res = await tacticsDelete({ ids: selectedRowKeys.join(',') });
            if (res.retcode == 0) {
              message.success("删除成功");    
              tableRef.current.calcPageNo(tableList, selectedRowKeys.length)            
              getSourceData();
            } else {
              message.error(res.msg);
            }
          } catch (err) {
            console.log(err);
          }
        },
      });
    } else {
      message.error("至少选中一条数据");
    }
  };
  // 下发
  const distribute = async (record={}) => {
    setType('1')
    deviceRef.current.showDrawer(record, [record.id]);
  }
  // 批量下发
  const batchDistribute = async (record = {}) => {
    setType('1')
    if (selectedRowKeys.length) {
      deviceRef.current.showDrawer(record, selectedRowKeys);
    } else {
      message.error("至少选中一条数据");
    }
  }
  // 列表数据
  const columns = [
    {
      title: "序号",
      key: "key",
      dataIndex: "key",
      width: 50,
      render: (text, record, index) => {
        return `${index + 1}`;
      },
    },
    {
      title: "过滤地址1",
      key: "firstIp",
      dataIndex: "firstIp",
    },
    {
      title: "过滤地址2",
      key: "secondIp",
      dataIndex: "secondIp"
    },
    {
      title: "过滤协议",
      key: "protocolName",
      dataIndex: "protocolName",
    },
    {
      title: "上次下发时间",
      key: "lastTime",
      dataIndex: "lastTime",
      render: (text, record) => (!text? '' : moment(text).format('YYYY-MM-DD HH:mm:ss'))
    },
    {
        title: "应用设备",
        key: "deviceNames",
        dataIndex: "deviceNames"
      },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      render: (text, record) => (
        <>
          <div className="table-option">
          <a
              onClick={() => {
                handleAdd(record);
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                deleteProtocol(record);
              }}
            >
              删除
            </a>
            <a
              onClick={() => {
                distribute(record);
              }}
            >
              下发
            </a>
          </div>
        </>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };
  return (
    <div>
      <div style={{ marginBottom: 20, marginTop: 20 }}>
        <Button
          type="primary"
          style={{ marginRight: 15, borderRadius: 2 }}
          onClick={() => {
            handleAdd();
          }}
        >
          新建过滤策略
        </Button>
        <Button
          type="primary"
          style={{ marginRight: 15, borderRadius: 2 }}
          onClick={()=>{batchDistribute()}}
        >
          批量下发
        </Button>
        <Button style={{ borderRadius: 2 }} onClick={()=>{batchDeleteProtocol()}}>
          批量删除
        </Button>
      </div>
      <div className={`tableBg`}>
        <SbrTable
          columns={columns}
          scroll={false}
          tableList={tableList}
          getSourceData={getSourceData}
          style={{ wordWrap: "break-word", wordBreak: "break-all" }}
          rowKey={(record) => record.id}
          ref={tableRef}
          loading={loading}
          rowSelection={rowSelection}
        />
      </div>
      <DeviceComponent ref={deviceRef} getSourceData={getSourceData} typeButton={type} tacticsDistrib={tacticsDistrib}/>
      <AddStrstegyFilter ref={addStrstegyFilterRef} getSourceData={getSourceData}/>
    </div>
  );
};

export default Form.create()(List);
