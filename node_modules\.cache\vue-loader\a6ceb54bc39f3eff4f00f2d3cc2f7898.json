{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\AddSentineUpdateModal.vue", "mtime": 1750059153637}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}