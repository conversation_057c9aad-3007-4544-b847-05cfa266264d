{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-673ca78e\"],{\"0122\":function(e,t,i){\"use strict\";i.d(t,\"a\",(function(){return n}));i(\"a4d3\"),i(\"e01a\"),i(\"d28b\"),i(\"d3b7\"),i(\"3ca3\"),i(\"ddb0\");function n(e){return n=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},n(e)}},\"078a\":function(e,t,i){\"use strict\";var n=i(\"2b0e\"),a=(i(\"99af\"),i(\"caad\"),i(\"ac1f\"),i(\"2532\"),i(\"5319\"),{bind:function(e,t,i){var n=[e.querySelector(\".el-dialog__header\"),e.querySelector(\".el-dialog\")],a=n[0],o=n[1];a.style.cssText+=\";cursor:move;\",o.style.cssText+=\";top:0px;\";var s=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();a.onmousedown=function(e){var t=[e.clientX-a.offsetLeft,e.clientY-a.offsetTop,o.offsetWidth,o.offsetHeight,document.body.clientWidth,document.body.clientHeight],n=t[0],r=t[1],l=t[2],c=t[3],u=t[4],d=t[5],m=[o.offsetLeft,u-o.offsetLeft-l,o.offsetTop,d-o.offsetTop-c],p=m[0],f=m[1],h=m[2],g=m[3],v=[s(o,\"left\"),s(o,\"top\")],w=v[0],b=v[1];w.includes(\"%\")?(w=+document.body.clientWidth*(+w.replace(/%/g,\"\")/100),b=+document.body.clientHeight*(+b.replace(/%/g,\"\")/100)):(w=+w.replace(/px/g,\"\"),b=+b.replace(/px/g,\"\")),document.onmousemove=function(e){var t=e.clientX-n,a=e.clientY-r;-t>p?t=-p:t>f&&(t=f),-a>h?a=-h:a>g&&(a=g),o.style.cssText+=\";left:\".concat(t+w,\"px;top:\").concat(a+b,\"px;\"),i.child.$emit(\"dragDialog\")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),o=function(e){e.directive(\"el-dialog-drag\",a)};window.Vue&&(window[\"el-dialog-drag\"]=a,n[\"default\"].use(o)),a.elDialogDrag=o;t[\"a\"]=a},1205:function(e,t,i){},\"134a\":function(e,t){e.exports=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAipJREFUWEftlr+LE0EUx79vIxdiIQErBS2ONKaRzGwCVv4AQW21EezOf+JsxEb/Ce0EG69VQfBHJSQzE2xiE67wQCshWLjc4e7zRmd1yeays9kcKbzpdmfn+z77vu/NDGHFgxaJ3+v1TsZxfAXAWbf+c61We9Pv97+V1SsF0O1215MkeQTgJoDaVLAYwFYQBJuDwWDbF8QbIAzDG8z8DMCJAvHvRHRbKfXCB8ILoNPpXAiC4C2AekbUEJGyz8wcAhCZud0kSS4Ph8MPRRCFAO12e63RaHwCsO7EdohoQyn1OisehuFVZn4C4Ix7vx1F0bnRaLQ3D6IQIAzDDWZ+7ER+MPN5Y8x4lqgQokVEHwEct/NEdFcpZaEOHD4AL5n5mlO4p7W2RXjgkFJuAnjoAF4ppa5XApBSfgFwKvXaGKPnCQohZFobAL5qrU9XBfiZtlwURfUiT13N7Lqgsdb6WFUATgW01oWW2W+llN5rCgXLiKWgZdbkAFqtVr3ZbN5J24mZ76fCRPS+qK9drVzMrHmQtu9kMnk6Ho9Te36/zgEIIZ4Tkd1qlz6YecsYcysrnAPYbyNLuLb06H8E9/bbOLub5jOQ9Y+IbPouVYR5l7VxupBnZcC7gn3B5hXlEcDCGRBC/G1Pa4UxJm23nDOHYkFW1Eact0seARxKBlZeA757QNHpuHAX/B8A7iwo87Mzvy17Fqz2NFz5fWD6RlQ5//8EdrxuREsM6CX1Czl0NTBQXfK3AAAAAElFTkSuQmCC\"},2532:function(e,t,i){\"use strict\";var n=i(\"23e7\"),a=i(\"5a34\"),o=i(\"1d80\"),s=i(\"ab13\");n({target:\"String\",proto:!0,forced:!s(\"includes\")},{includes:function(e){return!!~String(o(this)).indexOf(a(e),arguments.length>1?arguments[1]:void 0)}})},\"5a34\":function(e,t,i){var n=i(\"44e7\");e.exports=function(e){if(n(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"7d4a\":function(e,t,i){\"use strict\";var n=i(\"ea95\"),a=i.n(n);a.a},\"84df\":function(e,t,i){\"use strict\";i.d(t,\"b\",(function(){return o})),i.d(t,\"a\",(function(){return s}));var n=i(\"276c\"),a=i(\"e954\");function o(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=new Date,i=t.getHours(),n=\"\";return n=i>=0&&i<=8?\"早上\":i>8&&i<=12?\"上午\":i>12&&i<18?\"下午\":\"晚上\",!0===e&&(n+=\"好\"),n}var s=function(){function e(t,i){Object(n[\"a\"])(this,e),this.callback=t,this.interval=i,this.timerId=null,this.remainingTime=i,this.isPaused=!1}return Object(a[\"a\"])(e,[{key:\"runTimer\",value:function(){var e=this;this.timerId=setInterval((function(){e.isPaused||e.callback()}),this.interval)}},{key:\"pause\",value:function(){this.timerId&&!this.isPaused&&(this.isPaused=!0,clearInterval(this.timerId),this.timerId=null)}},{key:\"resume\",value:function(){this.isPaused&&(this.isPaused=!1,this.runTimer())}},{key:\"start\",value:function(){this.timerId||(this.isPaused=!1,this.remainingTime=this.interval,this.runTimer())}},{key:\"stop\",value:function(){this.timerId&&(clearInterval(this.timerId),this.timerId=null,this.isPaused=!1)}},{key:\"reset\",value:function(){this.stop(),this.remainingTime=this.interval,this.start()}}]),e}()},\"8a93\":function(e,t,i){\"use strict\";var n=i(\"1205\"),a=i.n(n);a.a},9129:function(e,t,i){var n=i(\"23e7\");n({target:\"Number\",stat:!0},{isNaN:function(e){return e!=e}})},ab13:function(e,t,i){var n=i(\"b622\"),a=n(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(i){try{return t[a]=!1,\"/./\"[e](t)}catch(n){}}return!1}},c54a:function(e,t,i){\"use strict\";i.d(t,\"l\",(function(){return n})),i.d(t,\"m\",(function(){return a})),i.d(t,\"b\",(function(){return o})),i.d(t,\"c\",(function(){return s})),i.d(t,\"a\",(function(){return r})),i.d(t,\"j\",(function(){return l})),i.d(t,\"q\",(function(){return c})),i.d(t,\"d\",(function(){return u})),i.d(t,\"f\",(function(){return d})),i.d(t,\"g\",(function(){return m})),i.d(t,\"e\",(function(){return p})),i.d(t,\"n\",(function(){return f})),i.d(t,\"k\",(function(){return h})),i.d(t,\"p\",(function(){return g})),i.d(t,\"h\",(function(){return v})),i.d(t,\"i\",(function(){return w})),i.d(t,\"o\",(function(){return b}));i(\"ac1f\"),i(\"466d\"),i(\"1276\");function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=\"\";switch(t){case 0:i=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break;case 1:i=/^(?![_.\\-])(?!.*?[_.\\-]$)[a-zA-Z0-9_.\\-\\u4e00-\\u9fa5]+$/;break;case 2:i=/^(?![_./\\-])(?!.*?[_./\\-]$)[a-zA-Z0-9_./\\-\\u4e00-\\u9fa5]+$/;break;case 3:i=/^(?![_./\\-\\s])(?!.*?[_./\\-\\s]$)[a-zA-Z0-9_./\\-\\s\\u4e00-\\u9fa5]+$/;break;default:i=/^(?![_\\-])(?!.*?[_\\-]$)[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]+$/;break}return i.test(e)}function a(e){var t=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\\[\\].<>/?\\-%]).{0,}$/;return t.test(e)}function o(e){var t=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return t.test(e)}function s(e){var t=/^([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\\_|\\.\\-]?)*[a-zA-Z0-9]+\\.[a-zA-Z]{2,3}$/;return t.test(e)}function r(e){var t=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return t.test(e)}function l(e){for(var t=/^((\\+?86)|(\\(\\+86\\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,i=e.split(\",\"),n=0;n<i.length;n++)if(!t.test(i[n]))return!1;return!0}function c(e){var t=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return t.test(e)}function u(e){var t=/^(\\d{2,5}-)?\\d{6,9}(-\\d{2,4})?$/;return t.test(e)}function d(e){var t=/^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/;return t.test(e)}function m(e){var t=/:/.test(e)&&e.match(/:/g).length<8&&/::/.test(e)?1===e.match(/::/g).length&&/^::$|^(::)?([\\da-f]{1,4}(:|::))*[\\da-f]{1,4}(:|::)?$/i.test(e):/^([\\da-f]{1,4}:){7}[\\da-f]{1,4}$/i.test(e);return t}function p(e){return d(e)||m(e)}function f(e){var t=/^([0-9]|[1-9][0-9]{0,4})$/;return t.test(e)}function h(e){for(var t=/^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(\\:([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,i=e.split(\",\"),n=0;n<i.length;n++)if(!t.test(i[n]))return!1;return!0}function g(e){var t=/^[^ ]+$/;return t.test(e)}function v(e){var t=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\\.[A-Fa-f0-9]{4}){2}$/;return t.test(e)}function w(e){var t=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return t.test(e)}function b(e){var t=/[^\\u4E00-\\u9FA5]/;return t.test(e)}},caad:function(e,t,i){\"use strict\";var n=i(\"23e7\"),a=i(\"4d64\").includes,o=i(\"44d2\"),s=i(\"ae40\"),r=s(\"indexOf\",{ACCESSORS:!0,1:0});n({target:\"Array\",proto:!0,forced:!r},{includes:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}}),o(\"includes\")},cb21:function(e,t){e.exports=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAmtJREFUWEfVlk1rE1EUht8zDIUE3biziD8gBYW5E+JGEBFB1O4VcWMVRBcq+IGgCwXxA6wLRah1I6J7PxBEQqEbQ+4ZUGh+gIjdubEkIGGOvXAnTNI2d6YmjM4qmTn3fR/OPfeeQyj4obz+1Wp1SkRmROQggJ12/Tci+khE881mcymPZmaASqUyUS6XH4rIWQDeBiYxET1tt9uXWq3W7ywgmQCMealU+gBgfxZRAPVOp3MoC0QmgDAMH4vIuZT5oog8ICJt3olISESXAexNYojoidb6vAvYCWD2PI7jr0naieiO1voGgHhA3AvD8LaIXLfvY8/zdrlqwgkQhuGsiFywoovMvG8d84TFU0otJJkgokda64vDsuAEUEqZqq7YVE9HUfR2mGAQBEeJ6I2NaTHz1N8C/AKwxYpMMvPyMEGl1HYAP2zMCjNv/e8Bit2Cwouw8GNoCqjQi8gAFH4VJxCFNaP0OS6sHbuayma/O6/izQpnXbceAAVBMENEpwCYezy5hrNqDsatAFgSkedRFM2b7p0O6AOw9/jLHINHXqg6gBPpfpIGIKXUpzGaJ7B1Zj6QZKIHEATBaSKas1FmnrsJ4IWr+7lSYLN6cnWguQVgwrb1M1EUPTO/ewBKqc8AalbwGjPfc4nn+a6Uugrgrl3TYOY9gwC5+n4ecxO70ZyQzkCvOpl5LMdTKbXG458CMGOUGafMc4SZ3+dNs2NUO7zaWN/ZmGVmnhysgVcAjo3SdIjWa2Y+3gdQq9V2dLvdLwC2jRnip+/7uxuNxvc+APPHQtwHYGb/ZDtGxWOm6QXf968k5msARuWUR+cPY5NCMF4f3MMAAAAASUVORK5CYII=\"},d5d1:function(e,t,i){\"use strict\";i.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"login-container\"},[n(\"section\",{staticClass:\"login-form-container\"},[n(\"section\",{staticClass:\"form-container\"},[n(\"section\",[n(\"section\",{staticClass:\"login-form-top\"},[n(\"img\",{staticClass:\"login-form-top-logo\",attrs:{src:i(\"f4ec\")}}),n(\"div\",{staticClass:\"login-form-top-title\"},[e._v(e._s(e.$store.getters.systemName||\"\"))])]),n(\"section\",{class:{\"login-form-content\":!0}},[n(\"el-tabs\",{attrs:{stretch:\"\"},on:{\"tab-click\":e.handleClickTabs},model:{value:e.loginType,callback:function(t){e.loginType=t},expression:\"loginType\"}},[n(\"el-tab-pane\",{attrs:{label:e.$t(\"login.type.account\"),name:\"account\"}},[n(\"el-form\",{ref:\"loginFormDom\",staticClass:\"login-form\",attrs:{model:e.form,size:\"medium\"}},[n(\"el-form-item\",{attrs:{prop:\"username\"}},[n(\"img\",{staticClass:\"img-icon\",attrs:{src:i(\"cb21\")}}),n(\"el-input\",{ref:\"usernameInputDom\",staticClass:\"login-form-input\",attrs:{name:\"username\",size:\"medium\",\"auto-complete\":\"on\",placeholder:\"请输入用户名\"},nativeOn:{keydown:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:(t.preventDefault(),e.keydownUsernameInput(t))}},model:{value:e.form.username,callback:function(t){e.$set(e.form,\"username\",t)},expression:\"form.username\"}})],1),n(\"el-form-item\",{attrs:{prop:\"password\"}},[n(\"img\",{staticClass:\"img-icon\",attrs:{src:i(\"134a\")}}),n(\"el-input\",{ref:\"passwordInputDom\",staticClass:\"login-form-input\",attrs:{type:e.password.type,name:\"password\",\"auto-complete\":\"on\",placeholder:\"请输入密码\"},nativeOn:{keydown:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:(t.preventDefault(),e.clickLogin(t))}},model:{value:e.form.password,callback:function(t){e.$set(e.form,\"password\",t)},expression:\"form.password\"}})],1),e.account.captchaEnable?n(\"el-form-item\",{attrs:{prop:\"mailCaptcha\"}},[n(\"i\",{staticClass:\"login-form-icon soc-new-icon-captcha\"}),n(\"el-input\",{ref:\"mailCaptchaInputDom\",staticClass:\"login-form-input\",attrs:{name:\"mailCaptcha\",placeholder:\"请输入验证码\"},nativeOn:{keydown:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:(t.preventDefault(),e.clickLogin(t))}},model:{value:e.account.captcha,callback:function(t){e.$set(e.account,\"captcha\",t)},expression:\"account.captcha\"}}),n(\"img\",{staticClass:\"captchaImg\",attrs:{src:\"data:image/png;base64,\"+e.account.imgSrc}})],1):e._e()],1)],1),n(\"el-tab-pane\",{attrs:{label:e.$t(\"login.type.mail\"),name:\"mail\"}},[n(\"el-form\",{ref:\"mailFormDom\",staticClass:\"login-form\",attrs:{model:e.mail,size:\"medium\"}},[n(\"el-form-item\",{attrs:{prop:\"mail\"}},[n(\"i\",{staticClass:\"login-form-icon soc-icon-email\"}),n(\"el-input\",{ref:\"mailInputDom\",staticClass:\"login-form-input\",attrs:{name:\"mail\",size:\"medium\",\"auto-complete\":\"on\",placeholder:\"请输入邮箱\"},nativeOn:{keydown:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:(t.preventDefault(),e.keydownMailInput(t))}},model:{value:e.mail.value,callback:function(t){e.$set(e.mail,\"value\",t)},expression:\"mail.value\"}})],1),n(\"el-form-item\",{attrs:{prop:\"mailCaptcha\"}},[n(\"i\",{staticClass:\"login-form-icon soc-new-icon-captcha\"}),n(\"el-input\",{ref:\"mailCaptchaInputDom\",staticClass:\"login-form-input\",attrs:{name:\"mailCaptcha\",placeholder:\"请输入验证码\"},nativeOn:{keydown:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:(t.preventDefault(),e.clickLogin(t))}},model:{value:e.mail.captcha,callback:function(t){e.$set(e.mail,\"captcha\",t)},expression:\"mail.captcha\"}}),n(\"section\",{staticClass:\"mail-action\"},[n(\"el-button\",{staticClass:\"mail-action-button\",attrs:{loading:e.effect.mailCaptchaLoading,disabled:e.effect.mailCaptchaDisabled,type:\"text\"},on:{click:e.clickGetEmailCaptcha}},[e.effect.mailCaptchaLoading?n(\"span\",[e._v(\" \"+e._s(e.$t(\"login.mail.getAgainCaptcha\",[e.mail.seconds]))+\" \")]):n(\"span\",[e._v(e._s(e.$t(\"login.mail.getCaptcha\")))])])],1)],1)],1)],1)],1),n(\"section\",{staticClass:\"login-action\"},[n(\"el-button\",{staticClass:\"login-action-button\",attrs:{type:\"primary\",disabled:e.license.remain<=0},on:{click:e.clickLogin}},[e._v(\" \"+e._s(e.$t(\"login.text\"))+\" \")])],1),e.exception.login?n(\"section\",{staticClass:\"error-info\"},[n(\"el-alert\",{attrs:{type:e.exception.type}},[n(\"span\",{domProps:{innerHTML:e._s(e.exception.login)}}),[e.code.license.illegal,e.code.license.expire,e.code.license.admin].indexOf(e.exception.code)>-1?n(\"el-upload\",{ref:\"uploadLicense\",staticClass:\"upload-license\",attrs:{action:\"#\",headers:e.upload.header,\"auto-upload\":\"\",\"show-file-list\":!1,accept:\".lic, .license\",\"file-list\":e.upload.files,\"before-upload\":e.beforeUploadValidate,\"on-change\":e.onUploadFileChange,\"http-request\":e.submitUploadFile}},[e.exception.tip&&\"\"!==e.exception.tip?n(\"el-tooltip\",{attrs:{content:e.$t(\"login.license.sequence\")+\":\"+e.exception.tip,effect:\"light\",placement:\"top\"}},[n(\"el-button\",{staticClass:\"upload-license-button\",on:{click:e.clickUploadLicense}},[e._v(\" \"+e._s(e.$t(\"button.upload\"))+\" \")])],1):n(\"el-button\",{staticClass:\"upload-license-button\",on:{click:e.clickUploadLicense}},[e._v(\" \"+e._s(e.$t(\"button.upload\"))+\" \")])],1):e._e(),e.exception.tip&&\"\"!==e.exception.tip?n(\"el-button\",{staticClass:\"upload-license-button upload-copy-button\",on:{click:e.clickCopy}},[e._v(\" \"+e._s(e.$t(\"button.copy\"))+\" \")]):e._e()],1)],1):e._e()],1)])])]),e.exception.display.completeUser?n(\"complete-user\",{attrs:{visible:e.exception.visible.completeUser},on:{\"update:visible\":function(t){return e.$set(e.exception.visible,\"completeUser\",t)},\"on-submit\":e.clickSubmitExceptionLogin}}):e._e(),e.exception.display.updatePassword?n(\"complete-password\",{attrs:{visible:e.exception.visible.updatePassword},on:{\"update:visible\":function(t){return e.$set(e.exception.visible,\"updatePassword\",t)},\"on-submit\":e.clickSubmitExceptionLogin}}):e._e(),e.exception.display.resetPassword?n(\"complete-password\",{attrs:{visible:e.exception.visible.resetPassword,\"has-old-password\":!1},on:{\"update:visible\":function(t){return e.$set(e.exception.visible,\"resetPassword\",t)},\"on-submit\":e.clickSubmitExceptionLogin}}):e._e()],1)},a=[],o=(i(\"baa5\"),i(\"13d5\"),i(\"b0c0\"),i(\"b64b\"),i(\"ac1f\"),i(\"5319\"),i(\"96cf\"),i(\"c964\")),s=i(\"720d\"),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"section\",{staticClass:\"slide-captcha\"},[i(\"section\",{staticClass:\"slide-captcha-img\",style:{width:e.width+\"px\"}},[i(\"img\",{ref:\"intactDom\",staticClass:\"intact-img\",attrs:{src:e.imgSrc(e.intact)}}),i(\"img\",{ref:\"tailorDom\",staticClass:\"tailor-img\",style:{top:e.y+\"px\"},attrs:{src:e.imgSrc(e.tailor)}})]),i(\"section\",{class:[\"slide-captcha-track\",{\"is-active\":e.status.active,\"is-success\":1===e.status.jigsaw,\"is-fail\":2===e.status.jigsaw||3===e.status.jigsaw}],style:{width:e.width+\"px\"}},[i(\"section\",{staticClass:\"track-mask\",style:{width:e.track.maskWidth}},[i(\"aside\",{staticClass:\"track-mask-slide\",style:{left:e.track.left},on:{mousedown:e.mousedownSlider,touchstart:function(t){return e.mousedownSlider(t,!1)},touchmove:function(t){return e.mousemoveSlider(t,!1)},touchend:function(t){return e.mouseupSlider(t,!1)}}},[i(\"i\",{staticClass:\"track-mask-icon el-icon-right\"})])]),i(\"section\",{staticClass:\"track-text\"},[e._v(\" \"+e._s(e.track.text)+\" \")]),e.status.jigsaw?i(\"p\",{staticClass:\"track-result\"},[e._v(\" \"+e._s(e.validateResultText)+\" \")]):e._e()])])},l=[],c=(i(\"a9e3\"),i(\"25eb\"),i(\"b6802\"),i(\"fd82\")),u={props:{intact:{type:String,default:null},tailor:{type:String,default:null},y:{type:Number,default:null}},data:function(){return{timestamp:null,width:300,dom:{intact:null,tailor:null},status:{active:!1,jigsaw:!1,mousedown:!1},track:{maskWidth:0,left:0,text:this.$t(\"slideCaptcha.tip.rightSlider\"),originX:null},event:{move:null,up:null}}},computed:{imgSrc:function(){return function(e){return\"data:image/png;base64,\"+e}},validateResultText:function(){var e=\"\";if(1===this.status.jigsaw){var t=(this.timestamp/1e3).toFixed(1);e=this.$t(\"login.captcha.success\",[t])}return 2===this.status.jigsaw&&(e=this.$t(\"login.captcha.invalid\")),3===this.status.jigsaw&&(e=this.$t(\"login.captcha.fail\")),e}},mounted:function(){this.initEvent(),this.initDom()},destroyed:function(){this.removeEvent()},methods:{initEvent:function(){this.mousemoveSlider(),this.mouseupSlider()},initDom:function(){this.dom.intact=this.$refs.intactDom,this.dom.tailor=this.$refs.tailorDom},mousedownSlider:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.status.jigsaw||(this.track.originX=t?e.clientX:e.changedTouches[0].pageX,this.status.mousedown=!0,this.timestamp=+new Date)},mousemoveSlider:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=function(e){if(!t.status.mousedown)return!1;var n=i?e.clientX-t.track.originX:e.changedTouches[0].pageX-t.track.originX;if(n<0||n+Number.parseInt(t.dom.tailor.clientWidth)>t.width)return!1;t.track.left=n+\"px\",t.dom.tailor.style.left=n+\"px\",t.status.active=!0,t.track.maskWidth=n+\"px\"};i?this.event.move=document.addEventListener(\"mousemove\",(function(e){n(e)})):n(e)},mouseupSlider:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=function(){var e=Object(o[\"a\"])(regeneratorRuntime.mark((function e(n){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.status.mousedown){e.next=2;break}return e.abrupt(\"return\",!1);case 2:if(t.status.mousedown=!1,a=i?n.clientX:n.changedTouches[0].pageX,a!==t.track.originX){e.next=6;break}return e.abrupt(\"return\",!1);case 6:return t.status.active=!1,t.timestamp=+new Date-t.timestamp,e.next=10,t.validateCaptcha(Number.parseInt(t.dom.tailor.style.left));case 10:t.removeEvent();case 11:case\"end\":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();i?this.event.up=document.addEventListener(\"mouseup\",(function(e){n(e)})):n(e)},removeEvent:function(){document.removeEventListener(\"mousemove\",this.event.move),document.removeEventListener(\"mouseup\",this.event.up)},refresh:function(){this.$emit(\"on-refresh\"),this.reset()},reset:function(){this.status.jigsaw=!1,this.status.active=!1,this.track.maskWidth=0,this.track.left=0,this.track.originX=0,this.dom.tailor.style.left=\"0px\"},validateCaptcha:function(e){var t=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function i(){var n;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=null,i.next=3,Object(c[\"j\"])(e).then((function(e){t.status.jigsaw=e.check,n=setTimeout((function(){1===e.check&&t.$emit(\"on-success\",e.captcha),2!==e.check&&3!==e.check||t.refresh(),clearTimeout(n)}),1e3)}));case 3:case\"end\":return i.stop()}}),i)})))()}}},d=u,m=(i(\"8a93\"),i(\"2877\")),p=Object(m[\"a\"])(d,r,l,!1,null,\"c4301592\",null),f=p.exports,h=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"custom-dialog\",{ref:\"userFormDialog\",attrs:{visible:e.selfVisible,title:e.$t(\"layout.setting.user.label\"),width:\"60%\"},on:{\"on-close\":e.close,\"on-submit\":e.submit}},[i(\"el-form\",{ref:\"userForm\",attrs:{\"label-width\":\"100px\",rules:e.userForm.model.rules,model:e.userForm.model}},[i(\"el-row\",[i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.password.new\"),prop:\"newPassword\"}},[i(\"el-input\",{staticClass:\"width-small\",attrs:{\"show-password\":\"\"},nativeOn:{\"!paste\":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.userForm.model.newPassword,callback:function(t){e.$set(e.userForm.model,\"newPassword\",t)},expression:\"userForm.model.newPassword\"}})],1)],1),i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.password.confirm\"),prop:\"confirmPassword\"}},[i(\"el-input\",{staticClass:\"width-small\",attrs:{\"show-password\":\"\"},nativeOn:{\"!paste\":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.userForm.model.confirmPassword,callback:function(t){e.$set(e.userForm.model,\"confirmPassword\",t)},expression:\"userForm.model.confirmPassword\"}})],1)],1)],1),i(\"el-row\",[i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.username\"),prop:\"userFullName\"}},[i(\"el-input\",{staticClass:\"width-small\",attrs:{maxlength:\"16\",\"show-word-limit\":\"\"},model:{value:e.userForm.model.userFullName,callback:function(t){e.$set(e.userForm.model,\"userFullName\",t)},expression:\"userForm.model.userFullName\"}})],1)],1),i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.nickname\"),prop:\"userSortName\"}},[i(\"el-input\",{staticClass:\"width-small\",attrs:{maxlength:\"16\",\"show-word-limit\":\"\"},model:{value:e.userForm.model.userSortName,callback:function(t){e.$set(e.userForm.model,\"userSortName\",t)},expression:\"userForm.model.userSortName\"}})],1)],1)],1),i(\"el-row\",[i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.email\"),prop:\"userMail\"}},[i(\"el-input\",{staticClass:\"width-small\",model:{value:e.userForm.model.userMail,callback:function(t){e.$set(e.userForm.model,\"userMail\",t)},expression:\"userForm.model.userMail\"}})],1)],1),i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.mobilephone\"),prop:\"userMobile\"}},[i(\"el-input\",{staticClass:\"width-small\",model:{value:e.userForm.model.userMobile,callback:function(t){e.$set(e.userForm.model,\"userMobile\",t)},expression:\"userForm.model.userMobile\"}})],1)],1)],1),i(\"el-row\",[i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.telephone\"),prop:\"userPhone\"}},[i(\"el-input\",{staticClass:\"width-small\",model:{value:e.userForm.model.userPhone,callback:function(t){e.$set(e.userForm.model,\"userPhone\",t)},expression:\"userForm.model.userPhone\"}})],1)],1),i(\"el-col\",{attrs:{span:12}},[i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.user.menu\"),prop:\"defaultMenu\"}},[i(\"el-cascader\",{staticClass:\"width-small\",attrs:{\"expand-trigger\":\"hover\",options:e.data.menu,props:e.data.menuRule},model:{value:e.userForm.model.defaultMenu,callback:function(t){e.$set(e.userForm.model,\"defaultMenu\",t)},expression:\"userForm.model.defaultMenu\"}})],1)],1)],1)],1)],1)},g=[],v=(i(\"498a\"),i(\"d465\")),w=i(\"f7b5\"),b=i(\"c54a\"),y={components:{CustomDialog:v[\"a\"]},props:{visible:{type:Boolean,default:!1}},data:function(){var e=this,t=function(t,i,n){\"\"===i?n(new Error(e.$t(\"validate.password.new.empty\"))):i.length<8||i.length>20?n(new Error(e.$t(\"validate.password.size\"))):Object(b[\"m\"])(i)?n():n(new Error(e.$t(\"validate.password.rule\")))},i=function(t,i,n){\"\"===i?n(new Error(e.$t(\"validate.password.confirm.empty\"))):i!==e.userForm.model.newPassword?n(new Error(e.$t(\"validate.password.confirm.compare\"))):n()},n=function(t,i,n){\"\"===i.trim()||Object(b[\"c\"])(i)?n():n(new Error(e.$t(\"validate.comm.email\")))},a=function(t,i,n){\"\"===i.trim()||Object(b[\"a\"])(i)?n():n(new Error(e.$t(\"validate.comm.cellphone\")))},o=function(t,i,n){\"\"===i.trim()||Object(b[\"q\"])(i)?n():n(new Error(e.$t(\"validate.comm.telephone\")))};return{selfVisible:this.visible,userForm:{model:{newPassword:\"\",confirmPassword:\"\",userFullName:\"\",userSortName:\"\",userMail:\"\",userMobile:\"\",userPhone:\"\",defaultMenu:[],rules:{newPassword:[{required:!0,validator:t,trigger:\"blur\"}],confirmPassword:[{required:!0,validator:i,trigger:\"blur\"}],userFullName:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],userSortName:[{required:!0,message:this.$t(\"validate.empty\"),trigger:\"blur\"}],userMail:[{validator:n,trigger:\"blur\"}],userMobile:[{validator:a,trigger:\"blur\"}],userPhone:[{validator:o,trigger:\"blur\"}]}}},data:{menuRule:{value:\"menuId\",label:\"menuName\",children:\"children\"},menu:[]}}},watch:{visible:function(e){this.selfVisible=e},selfVisible:function(e){this.$emit(\"update:visible\",e)}},mounted:function(){this.getMenu()},methods:{close:function(){this.selfVisible=!1,this.$refs[\"userForm\"].resetFields()},submit:function(){var e=this;this.$refs[\"userForm\"].validate((function(t){if(t){var i=e.userForm.model.defaultMenu.length,n=new s[\"JSEncrypt\"];n.setPublicKey(e.$store.getters.publicKey);var a={userId:e.userId,userFullName:e.userForm.model.userFullName,userSortName:e.userForm.model.userSortName,userMail:e.userForm.model.userMail,userPhone:e.userForm.model.userPhone,userMobile:e.userForm.model.userMobile,defaultMenu:i>0?e.userForm.model.defaultMenu[i-1]:\"\",newPassword:n.encrypt(e.userForm.model.newPassword),confirmPassword:n.encrypt(e.userForm.model.confirmPassword)};e.updateUserExtend(a),e.close()}else Object(w[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){return!1}));e.$refs.userFormDialog.end()}))},disablePaste:function(){return null},getMenu:function(){var e=this;Object(c[\"f\"])().then((function(t){e.data.menu=t}))},updateUserExtend:function(e){var t=this;Object(c[\"h\"])(e).then((function(e){1===e?Object(w[\"a\"])({i18nCode:\"tip.update.success\",type:\"success\"},(function(){t.$emit(\"on-submit\")})):6===e?Object(w[\"a\"])({i18nCode:\"tip.update.emailTip\",type:\"error\"},(function(){})):5===e?Object(w[\"a\"])({i18nCode:\"login.account.error\",type:\"error\"}):Object(w[\"a\"])({i18nCode:\"tip.update.error\",type:\"error\"})}))}}},k=y,C=Object(m[\"a\"])(k,h,g,!1,null,null,null),x=C.exports,$=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"custom-dialog\",{ref:\"passwordFormDialog\",attrs:{visible:e.selfVisible,title:e.$t(\"layout.setting.password.label\"),width:\"40%\"},on:{\"on-close\":e.close,\"on-submit\":e.submit}},[i(\"el-form\",{ref:\"passwordForm\",staticClass:\"password-form\",attrs:{model:e.password.form,rules:e.password.rules,\"label-width\":\"25%\"}},[e.hasOldPassword?i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.password.old\"),prop:\"old\"}},[i(\"el-input\",{staticClass:\"width-mini\",attrs:{\"show-password\":\"\"},nativeOn:{\"!paste\":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.password.form.old,callback:function(t){e.$set(e.password.form,\"old\",t)},expression:\"password.form.old\"}}),i(\"aside\",{staticClass:\"soc-form-error-text\",attrs:{slot:\"error\"},slot:\"error\"},[e._v(\" \"+e._s(e.password.error.old)+\" \")])],1):e._e(),i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.password.new\"),prop:\"new\"}},[i(\"el-input\",{staticClass:\"width-mini\",attrs:{\"show-password\":\"\"},nativeOn:{\"!paste\":function(e){e.preventDefault()}},model:{value:e.password.form.new,callback:function(t){e.$set(e.password.form,\"new\",t)},expression:\"password.form.new\"}}),i(\"aside\",{staticClass:\"soc-form-error-text\",attrs:{slot:\"error\"},slot:\"error\"},[e._v(\" \"+e._s(e.password.error.new)+\" \")])],1),i(\"el-form-item\",{attrs:{label:e.$t(\"layout.setting.password.confirm\"),prop:\"confirm\"}},[i(\"el-input\",{staticClass:\"width-mini\",attrs:{\"show-password\":\"\"},nativeOn:{\"!paste\":function(e){e.preventDefault()}},model:{value:e.password.form.confirm,callback:function(t){e.$set(e.password.form,\"confirm\",t)},expression:\"password.form.confirm\"}}),i(\"aside\",{staticClass:\"soc-form-error-text\",attrs:{slot:\"error\"},slot:\"error\"},[e._v(\" \"+e._s(e.password.error.confirm)+\" \")])],1)],1)],1)},A=[],O={components:{CustomDialog:v[\"a\"]},props:{visible:{type:Boolean,default:!1},hasOldPassword:{type:Boolean,default:!0}},data:function(){var e=this,t=function(t,i,n){\"\"===i?n(e.password.error.old=e.$t(\"validate.password.old.empty\")):n()},i=function(t,i,n){\"\"===i?n(e.password.error.new=e.$t(\"validate.password.new.empty\")):i===e.password.form.old?n(e.password.error.new=e.$t(\"validate.password.new.compare\")):i.length<8||i.length>20?n(e.password.error.new=e.$t(\"validate.password.size\")):Object(b[\"m\"])(i)?n():n(e.password.error.new=e.$t(\"validate.password.rule\"))},n=function(t,i,n){\"\"===i?n(e.password.error.confirm=e.$t(\"validate.password.confirm.empty\")):i!==e.password.form.new?n(e.password.error.confirm=e.$t(\"validate.password.confirm.compare\")):n()};return{selfVisible:this.visible,password:{form:{old:\"\",new:\"\",confirm:\"\"},rules:{old:[{required:!0,validator:t,trigger:\"blur\"}],new:[{required:!0,validator:i,trigger:\"blur\"}],confirm:[{required:!0,validator:n,trigger:\"blur\"}]},error:{old:\"\",new:\"\",confirm:\"\"}}}},watch:{visible:function(e){this.selfVisible=e},selfVisible:function(e){this.$emit(\"update:visible\",e)}},methods:{close:function(){this.selfVisible=!1,this.$refs[\"passwordForm\"].resetFields()},submit:function(){var e=this;this.$refs[\"passwordForm\"].validate((function(t){if(t){var i=new s[\"JSEncrypt\"];i.setPublicKey(e.$store.getters.publicKey);var n={newPassword:i.encrypt(e.password.form.new),confirmPassword:i.encrypt(e.password.form.confirm)};e.hasOldPassword&&(n=Object.assign(n,{oldPassword:i.encrypt(e.password.form.old)})),e.resetPassword(n),e.close()}else Object(w[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){return!1}));e.$refs.passwordFormDialog.end()}))},disablePaste:function(){return null},resetPassword:function(e){var t=this;Object(c[\"g\"])(e).then((function(e){1===e?Object(w[\"a\"])({i18nCode:\"tip.update.success\",type:\"success\"},(function(){t.$emit(\"on-submit\")})):Object(w[\"a\"])({i18nCode:\"tip.update.error\",type:\"error\"})}))}}},E=O,F=Object(m[\"a\"])(E,$,A,!1,null,null,null),P=F.exports,j={success:1,account:{invalid:2,overdue:8,illegalTime:12,illegalUser:13,lock:4,toImproved:6,unauth:9},password:{overdue:7,beReset:5},captcha:{invalid:3},session:{invalid:-1},license:{overdue:10,admin:11},mail:{invalid:14,noExist:15}},S=i(\"fa9d\"),I=i(\"84df\"),N=i(\"a78e\"),D=i.n(N),L={components:{SlideCaptcha:f,CompleteUser:x,CompletePassword:P},data:function(){return{timer:null,effect:{mailCaptchaLoading:!1,mailCaptchaDisabled:!0},form:{username:\"\",password:\"\"},password:{type:\"password\",class:\"light\"===this.$store.getters.theme?\"soc-new-icon-lock\":\"soc-new-icon-lock-fill\"},exception:{login:null,code:0,tip:\"\",type:\"warning\",visible:{completeUser:!1,resetPassword:!1,updatePassword:!1},display:{completeUser:!1,resetPassword:!1,updatePassword:!1}},router:{redirect:null,query:{}},license:{remain:365,overdue:7},upload:{header:{\"Content-Type\":\"multipart/form-data\"},files:[]},mail:{value:\"\",captcha:\"\",timer:null,seconds:-1},account:{captchaEnable:!1,captcha:\"\",imgSrc:\"\"},loginType:\"account\"}},computed:{code:function(){return j}},watch:{$route:{handler:function(e){var t=e.query;t&&(this.router.redirect=t.redirect,this.router.query=this.getRouterQuery(t))},immediate:!0},\"mail.value\":function(e){this.effect.mailCaptchaDisabled=!Object(b[\"c\"])(e)},\"exception.login\":function(e){switch(e){case this.$t(\"login.account.toImproved\",[\"login-complete-account\"]):this.addExceptionEvent(\"login-complete-account\",\"completeUser\");break;case this.$t(\"login.password.overdue\",[\"login-complete-password\"]):this.addExceptionEvent(\"login-complete-password\",\"updatePassword\");break;case this.$t(\"login.password.beReset\",[\"login-reset-password\"]):this.addExceptionEvent(\"login-reset-password\",\"resetPassword\");break;default:this.exception.display.completeUser=this.exception.display.updatePassword=this.exception.display.resetPassword=!1;break}}},mounted:function(){this.timer=new I[\"a\"](this.preloadLoginData,3e5),this.timer.start(),this.preloadLoginData()},beforeDestroy:function(){var e;this.clearCountdownTimer(),null===(e=this.timer)||void 0===e||e.stop()},methods:{handleClickTabs:function(e){var t=this;\"account\"===e.name?this.$nextTick((function(){t.$refs.usernameInputDom.focus()})):this.$nextTick((function(){t.$refs.mailInputDom.focus()}))},preloadLoginData:function(){this.initLoginState(),this.getTokenPublickey()},getTokenPublickey:function(){var e=this;this.$store.dispatch(\"user/registry\").then((function(t){e.account.captchaEnable=t.captchaEnable,t.captchaEnable&&e.getCaptcha()}))},getCaptcha:function(){var e=this;Object(c[\"a\"])().then((function(t){e.account.imgSrc=t}))},initLoginState:function(){this.$store.dispatch(\"user/saveMode\",\"offline\")},clickLogin:function(){var e=this;if(\"account\"==this.loginType){var t=!this.form.username.isNotEmpty()||!this.form.password.isNotEmpty();t?setTimeout((function(){e.exception.login=e.$t(\"login.account.empty\")}),500):this.account.captchaEnable&&!this.account.captcha.isNotEmpty()?this.exception.login=\"验证码不能为空！\":this.accountLoginValidate()}else\"mail\"==this.loginType&&this.emailLogin()},accountLoginValidate:function(){var e=this;this.$refs.loginFormDom.validate((function(t){if(t){var i=new s[\"JSEncrypt\"];i.setPublicKey(e.$store.getters.publicKey),e.login({account:e.form.username,pasw:i.encrypt(e.form.password),captcha:e.account.captcha})}else e.validateFail()}))},emailLogin:function(){var e=this.mail.value,t=this.mail.captcha;\"\"!==e&&e?Object(b[\"c\"])(e)?\"\"!==t&&t?this.mailLoginValidate():Object(w[\"a\"])({i18nCode:\"validate.captcha.empty\",type:\"warning\"}):Object(w[\"a\"])({i18nCode:\"validate.comm.email\",type:\"warning\"}):Object(w[\"a\"])({i18nCode:\"login.validate.emailName\",type:\"warning\"})},mailLoginValidate:function(){var e=this;this.$refs.mailFormDom.validate((function(t){t?e.login({mail:e.mail.value,mailCaptcha:e.mail.captcha}):e.validateFail()}))},changeEnableAnimate:function(e){this.$store.dispatch(\"system/switchLoginAnimate\",e)},keydownUsernameInput:function(){var e=this.form.username.isNotEmpty()&&this.form.password.isNotEmpty();e?this.clickLogin():this.$refs.passwordInputDom.focus()},keydownMailInput:function(){var e=this.mail.value.isNotEmpty()&&this.mail.captcha.isNotEmpty();e?this.clickLogin():this.$refs.mailCaptchaInputDom.focus()},validateFail:function(){Object(w[\"a\"])({i18nCode:\"validate.form.warning\",type:\"warning\",print:!0},(function(){}))},login:function(e){var t=this,i=\"mail\"===this.loginType?\"user/mailLogin\":\"user/login\";this.$store.dispatch(i,e).then((function(e){e&&(t.exception.code=e.result,t.exception.tip=e.message,t.handleLoginCode(t.exception.code))})).catch((function(e){console.error(e)}))},handleLoginCode:function(e){switch(e){case this.code.success:this.loginSuccess();break;case this.code.account.invalid:this.loginInvalidHandler(\"login.account.invalid\",\"error\");break;case this.code.account.illegalUser:this.loginInvalidHandler(\"login.account.illegalUser\",\"error\");break;case this.code.account.illegalTime:this.loginInvalidHandler(\"login.account.illegalTime\",\"warning\");break;case this.code.account.overdue:this.loginInvalidHandler(\"login.account.overdue\",\"warning\");break;case this.code.account.lock:this.loginInvalidHandler(\"login.account.lock\",\"error\");break;case this.code.account.toImproved:this.loginInvalidHandler(\"login.account.toImproved\",\"warning\",[\"login-complete-account\"]);break;case this.code.account.unauth:this.loginInvalidHandler(\"login.account.unauth\",\"warning\");break;case this.code.password.overdue:this.loginInvalidHandler(\"login.password.overdue\",\"warning\",[\"login-complete-password\"]);break;case this.code.password.beReset:this.loginInvalidHandler(\"login.password.beReset\",\"warning\",[\"login-reset-password\"]);break;case this.code.session.invalid:this.loginInvalidHandler(\"login.session.invalid\",\"warning\");break;case this.code.captcha.invalid:this.loginInvalidHandler(\"login.captcha.fail\",\"warning\");break;case this.code.license.admin:this.loginInvalidHandler(\"login.license.admin\",\"error\",[\"\"]);break;case this.code.license.overdue:this.loginInvalidHandler(\"login.license.overdue\",\"warning\");break;case this.code.license.expire:this.loginInvalidHandler(\"login.license.expire\",\"error\",[\"\"]);break;case this.code.mail.invalid:this.loginInvalidHandler(\"login.mail.invalid\",\"error\");break;case this.code.mail.noExist:this.loginInvalidHandler(\"login.mail.noExist\",\"error\");break;default:console.error(\"err-question: no find return value\");break}},loginInvalidHandler:function(e,t,i){this.exception.login=i?this.$t(e,i):this.$t(e),this.exception.type=t},loginSuccess:function(){this.loginSuccessAddNotice(),this.loginSuccessAddState()},loginSuccessAddNotice:function(){var e=this,t=new Date,i=t.getHours(),n=function(t){if(0===document.getElementsByClassName(\"el-notification\").length){var i=\"account\"===e.loginType?e.form.username:e.mail.value;e.$notify({type:\"success\",title:e.$t(\"login.success.welcome\",[i]),message:e.$t(\"login.success.\".concat(t)),offset:70})}};i>=0&&i<6&&n(\"night\"),i>=6&&i<12&&n(\"morning\"),i>=12&&i<13&&n(\"noon\"),i>=13&&i<18&&n(\"afternoon\"),i>=18&&i<24&&n(\"evening\")},loginSuccessAddState:function(){var e=this;return Object(o[\"a\"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$store.dispatch(\"user/saveMode\",\"online\");case 2:\"/login\"===e.router.redirect&&(e.router.redirect=null),e.$router.replace({path:e.router.redirect||\"/layout\",query:e.router.query}),D.a.set(\"store\",JSON.stringify(e.$store.state));case 5:case\"end\":return t.stop()}}),t)})))()},clickPasswordLock:function(){this.password.type=\"password\"===this.password.type?\"text\":\"password\",\"light\"===this.$store.getters.theme&&(this.password.class=\"password\"===this.password.type?\"soc-new-icon-lock\":\"soc-new-icon-unlock\"),\"dark\"===this.$store.getters.theme&&(this.password.class=\"password\"===this.password.type?\"soc-new-icon-lock-fill\":\"soc-new-icon-unlock-fill\")},clickSubmitExceptionLogin:function(){this.exception.login=!1,this.preloadLoginData()},clickUploadLicense:function(){this.upload.files=[],this.$refs.uploadLicense.submit()},beforeUploadValidate:function(e){if(this.upload.files.length>0){var t=e.name.substring(e.name.lastIndexOf(\".\")+1),i=\"lic\"===t,n=\"license\"===t;return i||n||Object(w[\"a\"])({i18nCode:\"validate.upload.license\",type:\"warning\"}),i||n}},onUploadFileChange:function(e){this.upload.files.push(e)},submitUploadFile:function(e){if(e.file&&this.upload.files.length>0){var t=new FormData;t.append(\"name\",\"upload\"),t.append(\"file\",e.file),this.uploadLicense(t)}},addExceptionEvent:function(e,t){var i=this;this.exception.display[t]=!0,this.$nextTick((function(){i.exception.login&&document.querySelector(\".\".concat(e)).addEventListener(\"click\",(function(){i.exception.visible[t]=!0}))}))},getRouterQuery:function(e){return Object.keys(e).reduce((function(t,i){return\"redirect\"!==i&&(t[i]=e[i]),t}),{})},clickGetEmailCaptcha:function(){var e=this.mail.value;Object(S[\"b\"])(e)?Object(b[\"c\"])(e)?this.getCaptchaMail(this.mail.value):Object(w[\"a\"])({i18nCode:\"validate.comm.email\",type:\"warning\"}):Object(w[\"a\"])({i18nCode:\"login.validate.emailName\",type:\"warning\"})},countdownGetCaptcha:function(){var e=this,t=60;this.mail.timer||(this.mail.seconds=t,this.effect.mailCaptchaLoading=!0,this.mail.timer=setInterval((function(){e.mail.seconds>1&&e.mail.seconds<=t?e.mail.seconds--:e.clearCountdownTimer()}),1e3))},clearCountdownTimer:function(){clearTimeout(this.mail.timer),this.mail.timer=null,this.effect.mailCaptchaLoading=!1},uploadLicense:function(e){var t=this;Object(c[\"i\"])(e).then((function(e){1===e?(Object(w[\"a\"])({i18nCode:\"tip.upload.success\",type:\"success\"}),t.exception.login=!1):3===e?Object(w[\"a\"])({i18nCode:\"tip.upload.format\",type:\"error\"}):Object(w[\"a\"])({i18nCode:\"tip.upload.error\",type:\"error\"})}))},clickCopy:function(){var e=document.createElement(\"input\");e.setAttribute(\"value\",this.exception.tip),document.body.appendChild(e),e.select(),document.execCommand(\"Copy\"),document.body.removeChild(e)},getCaptchaMail:function(e){var t=this;Object(c[\"d\"])(e).then((function(e){1===e?Object(w[\"a\"])({i18nCode:\"login.validate.success\",type:\"success\",print:!0},(function(){t.countdownGetCaptcha()})):2===e?Object(w[\"a\"])({i18nCode:\"login.validate.invalid\",type:\"warning\",print:!0},(function(){t.effect.mailCaptchaLoading=!1})):3===e?Object(w[\"a\"])({i18nCode:\"login.validate.failed\",type:\"error\",print:!0},(function(){t.effect.mailCaptchaLoading=!1})):15===e&&Object(w[\"a\"])({i18nCode:\"login.mail.noExist\",type:\"error\",print:!0},(function(){t.effect.mailCaptchaLoading=!1}))}))}}},_=L,T=(i(\"7d4a\"),Object(m[\"a\"])(_,n,a,!1,null,\"c8354c78\",null));t[\"default\"]=T.exports},ea95:function(e,t,i){},f4ec:function(e,t,i){e.exports=i.p+\"static/img/login-new.95dc4f48.jpg\"},fa9d:function(e,t,i){\"use strict\";i.d(t,\"d\",(function(){return o})),i.d(t,\"b\",(function(){return s})),i.d(t,\"e\",(function(){return l})),i.d(t,\"c\",(function(){return c})),i.d(t,\"a\",(function(){return u}));i(\"a4d3\"),i(\"e01a\"),i(\"caad\"),i(\"fb6a\"),i(\"a9e3\"),i(\"9129\"),i(\"d3b7\"),i(\"25f0\");var n=i(\"d0ff\"),a=i(\"0122\"),o=\"undefined\"===typeof window;function s(e){return null!==e&&void 0!==e&&\"\"!==e}function r(e){return e.constructor===Object}function l(e){return\"string\"===typeof e||e.constructor===String}function c(e){return\"number\"===typeof e||e.constructor===Number}function u(e,t){var i=function(e){return Object.prototype.toString.call(e).slice(8,-1)};if(!r(e)&&!r(t))return!(!Number.isNaN(e)||!Number.isNaN(t))||e===t;if(!r(e)||!r(t))return!1;if(i(e)!==i(t))return!1;if(e===t)return!0;if([\"Array\"].includes(i(e)))return m(e,t);if([\"Object\"].includes(i(e)))return d(e,t);if([\"Map\",\"Set\"].includes(i(e))){var a=Object(n[\"a\"])(e),o=Object(n[\"a\"])(t);return u(a,o)}return!1}function d(e,t){for(var i in e){if(e.hasOwnProperty(i)!==t.hasOwnProperty(i))return!1;if(Object(a[\"a\"])(e[i])!==Object(a[\"a\"])(t[i]))return!1}for(var n in t){if(e.hasOwnProperty(n)!==t.hasOwnProperty(n))return!1;if(Object(a[\"a\"])(e[n])!==Object(a[\"a\"])(t[n]))return!1;if(e.hasOwnProperty(n))if(e[n]instanceof Array&&t[n]instanceof Array){if(!m(e[n],t[n]))return!1}else if(e[n]instanceof Object&&t[n]instanceof Object){if(!d(e[n],t[n]))return!1}else if(e[n]!==t[n])return!1}return!0}function m(e,t){if(!e||!t)return!1;if(e.length!==t.length)return!1;for(var i=0,n=e.length;i<n;i++)if(e[i]instanceof Array&&t[i]instanceof Array){if(!m(e[i],t[i]))return!1}else if(e[i]instanceof Object&&t[i]instanceof Object){if(!d(e[i],t[i]))return!1}else if(e[i]!==t[i])return!1;return!0}}}]);", "extractedComments": []}