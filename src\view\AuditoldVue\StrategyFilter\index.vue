<template>
  <div>
    <div style="margin-bottom: 20px; margin-top: 20px">
      <el-button
        type="primary"
        style="margin-right: 15px; border-radius: 2px"
        @click="handleAdd()"
      >
        新建过滤策略
      </el-button>
      <el-button
        type="primary"
        style="margin-right: 15px; border-radius: 2px"
        @click="batchDistribute()"
      >
        批量下发
      </el-button>
      <el-button style="border-radius: 2px" @click="batchDeleteProtocol()">
        批量删除
      </el-button>
    </div>
    <div class="tableBg">
      <el-table
        :data="tableList.list || []"
        :loading="loading"
        row-key="id"
        @selection-change="onSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="firstIp" label="过滤地址1" />
        <el-table-column prop="secondIp" label="过滤地址2" />
        <el-table-column prop="protocolName" label="过滤协议" />
        <el-table-column prop="lastTime" label="上次下发时间">
          <template slot-scope="scope">
            {{
              !scope.row.lastTime
                ? ''
                : $moment(scope.row.lastTime).format('YYYY-MM-DD HH:mm:ss')
            }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceNames" label="应用设备" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="table-option">
              <el-button type="text" @click="handleAdd(scope.row)">编辑</el-button>
              <el-button type="text" @click="deleteProtocol(scope.row)">删除</el-button>
              <el-button type="text" @click="distribute(scope.row)">下发</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-row type="flex" justify="end" style="margin-top: 20px">
        <el-pagination
          :current-page="pagination.pageIndex"
          :page-size="pagination.pageSize"
          :total="tableList.total || 0"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onShowSizeChange"
          @current-change="handlePageChange"
        />
      </el-row>
    </div>
    <device-component
      ref="deviceRef"
      :type-button="type"
      :tactics-distrib="tacticsDistrib"
      @getSourceData="getSourceData"
    />
    <add-strategy-filter ref="addStrategyFilterRef" @getSourceData="getSourceData" />
  </div>
</template>

<script>
import { tacticsSearch, tacticsDelete, tacticsDistrib } from '@api/auditold/strategyFilter'
import DeviceComponent from './components/deviceComponent'
import AddStrategyFilter from './components/addStrstegyFilter'
import moment from 'moment'

export default {
  name: 'StrategyFilter',
  components: {
    DeviceComponent,
    AddStrategyFilter,
  },
  data() {
    return {
      tableList: {},
      type: '',
      loading: false,
      selectedRowKeys: [],
      pagination: {
        pageIndex: 1,
        pageSize: 10,
      },
      tacticsDistrib,
    }
  },
  mounted() {
    this.getSourceData(true)
  },
  methods: {
    // 查询列表
    async getSourceData(isSearch = false) {
      try {
        this.loading = true
        const params = isSearch
          ? {
              pageIndex: 1,
              pageSize: 10,
            }
          : {
              ...this.pagination,
            }
        const res = await tacticsSearch(params)
        if (res.retcode === 0) {
          this.tableList = res.data
          this.loading = false
          this.selectedRowKeys = []
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (err) {
        console.error('查询列表失败:', err)
        this.loading = false
      }
    },

    // 新建/编辑
    handleAdd(record = {}) {
      this.$refs.addStrategyFilterRef.showDrawer(record)
    },

    // 删除
    deleteProtocol(record = {}) {
      this.$confirm('确定要删除选中过滤策略吗?删除后不可恢复', '删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(async () => {
          try {
            const res = await tacticsDelete({ ids: record.id })
            if (res.retcode === 0) {
              this.$message.success('删除成功')
              this.calcPageNo(this.tableList, 1)
              this.getSourceData()
            } else {
              this.$message.error(res.msg)
            }
          } catch (err) {
            console.error('删除失败:', err)
          }
        })
        .catch(() => {
          console.log('取消删除')
        })
    },

    // 批量删除
    batchDeleteProtocol() {
      if (this.selectedRowKeys.length) {
        this.$confirm('确定要删除选中过滤策略吗?删除后不可恢复', '删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
        })
          .then(async () => {
            try {
              const res = await tacticsDelete({ ids: this.selectedRowKeys.join(',') })
              if (res.retcode === 0) {
                this.$message.success('删除成功')
                this.calcPageNo(this.tableList, this.selectedRowKeys.length)
                this.getSourceData()
              } else {
                this.$message.error(res.msg)
              }
            } catch (err) {
              console.error('批量删除失败:', err)
            }
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 下发
    async distribute(record = {}) {
      this.type = '1'
      this.$refs.deviceRef.showDrawer(record, [record.id])
    },

    // 批量下发
    async batchDistribute() {
      this.type = '1'
      if (this.selectedRowKeys.length) {
        this.$refs.deviceRef.showDrawer({}, this.selectedRowKeys)
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 选择改变
    onSelectionChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)
    },

    // 分页大小改变
    onShowSizeChange(pageSize, current) {
      this.pagination.pageSize = pageSize
      this.pagination.pageIndex = current
      this.getSourceData()
    },

    // 页码改变
    handlePageChange(pageNumber) {
      this.pagination.pageIndex = pageNumber
      this.getSourceData()
    },

    // 计算页码
    calcPageNo(tableList, deleteCount) {
      const { pageIndex, pageSize } = this.pagination
      const total = tableList.total || 0
      const currentPageCount = tableList.list ? tableList.list.length : 0
      
      if (currentPageCount <= deleteCount && pageIndex > 1) {
        this.pagination.pageIndex = pageIndex - 1
      }
    },
  },
}
</script>

<style scoped lang="scss">
.tableBg {
  .el-table {
    border: 1px solid #ebeef5;
  }
}

.table-option {
  .el-button {
    margin-right: 10px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
