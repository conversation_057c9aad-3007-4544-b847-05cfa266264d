import { stringify } from "qs";
import { request } from "@/utils/umiRequest.js";
//策略下发记录-删除
export async function tacticsDeleteR(params) {
  return request(`/home_dev/filter_tactics/delete_record`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}
//策略下发记录-查询
export async function tacticsPages(params) {
  return request(`/home_dev/filter_tactics/records`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
    data: JSON.stringify(params),
  });
}









