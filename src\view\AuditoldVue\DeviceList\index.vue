<template>
  <div class="router-wrap-table">
    <el-tabs v-model="activeKey" @tab-click="onChange" @tab-remove="onEdit">
      <el-tab-pane label="设备列表" name="0" :closable="false">
        <header class="table-header">
          <section class="table-header-main">
            <section class="table-header-search">
              <section v-show="!isShow" class="table-header-search-input">
                <el-input
                  v-model="searchForm.fireName"
                  clearable
                  placeholder="设备名称"
                  prefix-icon="soc-icon-search"
                  @change="handleAdvancedSearch"
                />
              </section>
              <section class="table-header-search-button">
                <el-button v-if="!isShow" type="primary" @click="handleAdvancedSearch">查询</el-button>
                <el-button @click="toggleShow">
                  高级搜索
                  <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
                </el-button>
              </section>
            </section>
            <section class="table-header-button">
              <el-button type="primary" @click="handleAddClick">新建设备</el-button>
              <el-button type="danger" @click="handleDeleteClick">批量删除</el-button>
            </section>
          </section>
          <section class="table-header-extend">
            <el-collapse-transition>
              <div v-show="isShow">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input v-model="searchForm.fireName" clearable placeholder="设备名称" @change="handleAdvancedSearch" />
                  </el-col>
                  <el-col :span="6">
                    <el-input v-model="searchForm.ip" clearable placeholder="设备IP" @change="handleAdvancedSearch" />
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="searchForm.onlinStatus" clearable placeholder="在线状态" @change="handleAdvancedSearch">
                      <el-option label="全部" value="" />
                      <el-option label="在线" value="1" />
                      <el-option label="离线" value="0" />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24" align="right">
                    <el-button type="primary" @click="handleAdvancedSearch">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                    <el-button icon="soc-icon-scroller-top-all" @click="toggleShow"></el-button>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>
          </section>
        </header>

        <main class="table-body">
          <section class="table-body-header">
            <h2 class="table-body-title">审计设备管理</h2>
          </section>
          <section v-loading="loading" class="table-body-main">
            <el-table
              :data="auditData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @selection-change="onSelectChange"
            >
                <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" width="80" align="center">
                <template slot-scope="scope">
                  {{ (currentPage - 1) * currentPageSize + scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="notes" label="设备名称" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button type="text" class="el-button--blue" @click="handleLook(scope.row)">
                    {{ scope.row.notes }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="category_text" label="设备类型" />
              <el-table-column label="在线状态" width="100">
                <template slot-scope="scope">
                  <span :class="scope.row.status === 1 ? 'status-online' : 'status-offline'">
                    <i :class="scope.row.status === 1 ? 'el-icon-success' : 'el-icon-error'"></i>
                    {{ scope.row.status === 1 ? '在线' : '离线' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="syl_cpu" label="CPU率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_cpu" :percentage="parseInt(scope.row.syl_cpu)" :stroke-width="8" color="#52C41A" />
                </template>
              </el-table-column>
              <el-table-column prop="syl_nc" label="内存率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_nc" :percentage="parseInt(scope.row.syl_nc)" :stroke-width="8" color="#4C24ED" />
                </template>
              </el-table-column>
              <el-table-column prop="syl_disk" label="磁盘率" width="120">
                <template slot-scope="scope">
                  <el-progress v-if="scope.row.syl_disk" :percentage="parseInt(scope.row.syl_disk)" :stroke-width="8" color="#1373F1" />
                </template>
              </el-table-column>
              <el-table-column prop="originPort" label="实时流量" width="200">
                <template slot-scope="scope">
                  <echarts-test :record="scope.row.device_flow_info" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="280" fixed="right">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button class="el-button--blue" type="text" @click="handleShowConfig(scope.row)">配置</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleLook(scope.row)">查看</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleDeleteClick(scope.row)">删除</el-button>
                    <el-button class="el-button--blue" type="text" @click="handlePing(scope.row)">Ping</el-button>
                    <el-button class="el-button--blue" type="text" @click="handleUser(scope.row)">用户管理</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="auditTotal > 0"
            small
            background
            align="right"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="currentPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="auditTotal"
            @size-change="onShowSizeChange"
            @current-change="handlePageChange"
          />
        </footer>
      </el-tab-pane>

      <!-- 动态标签页 -->
      <el-tab-pane v-for="pane in panes" :key="pane.key" :label="pane.title" :name="pane.key" :closable="true">
        <iframe :src="pane.content" width="100%" height="600px" frameborder="0"></iframe>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加设备模态框 -->
    <add-device-modal
      ref="addModal"
      :group-data="groupData"
      @save="handleAddDevice"
    />

    <!-- 编辑配置模态框 -->
    <edit-config-modal
      ref="configModal"
      :group-data="groupData"
      :current-config="currentConfig"
      @save="handleConifgSave"
    />

    <!-- 用户管理模态框 -->
    <el-dialog
      :visible.sync="userVisible"
      title="已保存用户"
      width="650px"
      :close-on-click-modal="false"
      center
    >
      <template slot="title">
        <span style="font-weight: bold">已保存用户</span>
        <span style="color: #999; margin-left: 10px">请选择登陆用户并保存</span>
      </template>
      <el-table
        :data="curDevUserList"
        :loading="loading"
        row-key="id"
        @selection-change="onUserSelectChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="60">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="account_name" label="用户名" />
        <el-table-column prop="account_role_text" label="用户类型" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              style="border-radius: 2px; color: #1890ff"
              @click="handleDeleteUser(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row type="flex" justify="end" style="margin-top: 20px">
        <el-pagination
          :current-page="currentUserPage"
          :page-size="currentUserPageSize"
          :total="userTotal"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onUserShowSizeChange"
          @current-change="handleUserPageChange"
        />
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick">取消</el-button>
        <el-button type="primary" @click="handleOkUser">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getData,
  getUser,
  addUser,
  modifyUser,
  deleteUser,
  loginForLog,
  devicePing,
  DeleteData,
  addEquipment,
  editEquipment,
  getTopoData,
  setTopoData,
  searchGroup,
} from '@api/auditold/deviceList'
import AddDeviceModal from './components/AddDeviceModal'
import EditConfigModal from './components/EditConfigModal'
import EchartsTest from '../highcharts/commen'
import onlineIcon from '@/assets/IconFont/online.png'
import unonlineIcon from '@/assets/IconFont/unonline.png'
import CryptoJS from 'crypto-js'

// 审计角色映射关系 1=>管理员 2=>操作员 3=>审计员
const auditRoleMap = {
  manage: 1,
  operator: 2,
  audit: 3,
}

export default {
  name: 'DeviceList',
  components: {
    AddDeviceModal,
    EditConfigModal,
    EchartsTest,
  },
  data() {
    return {
      activeKey: '0',
      isShow: false,
      panes: [],
      timer: null,
      currentPage: 1,
      currentPageSize: 10,
      currentUserPage: 1,
      currentUserPageSize: 10,
      userTotal: 0,
      selectedRowKeys: [],
      selectedDevList: [],
      userSelectedRowKeys: [],
      searchForm: {
        fireName: '',
        ip: '',
        onlinStatus: '',
      },
      searchValue: {},
      visible: false,
      remarkVisible: false,
      userVisible: false,
      currentData_id: null,
      currentDeviceId: 0,
      auditPwdList: [],
      isManualErr: false, // 是否手工登录错误，错误则不保存用户名密码
      isLocked: false, // 是否锁定，如果锁定自动和手动都会通知，手动表示密码错误，自动表示要停止自动登录
      iframeShow: false,
      currentConfig: null,
      curDevUserList: [],
      topoData: {}, // 存储topo数据
      groupData: [], // 设备分组
      auditData: [],
      auditTotal: 0,
      loading: false,
      onlineIcon,
      unonlineIcon,
    }
  },
  mounted() {
    this.getDeviceList()
    this.getTopoDataFunc()
    this.getGroupList()
    window.addEventListener('message', this.receiveMessageFromIndex, false)

    // 设置定时器
    this.timer = setInterval(() => this.getDeviceList(), 30000)

    // 获取缓存打开设备标签页
    let panes = localStorage.getItem('auditpanes')
    let auditUserPwdList = localStorage.getItem('auditUserPwdList')
    if (panes) {
      panes = JSON.parse(panes)
      this.panes = panes
    }
    if (auditUserPwdList) {
      auditUserPwdList = JSON.parse(auditUserPwdList)
      this.auditPwdList = auditUserPwdList
    }
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('message', this.receiveMessageFromIndex)
    // 清空定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow
    },

    // 设备分组
    getGroupList(callback) {
      searchGroup()
        .then((res) => {
          if (res.retcode === 0) {
            this.groupData = res.data
            if (callback) callback()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('获取分组列表失败:', error)
        })
    },

    // 接受子页面postMessage消息监听方法
    receiveMessageFromIndex(event) {
      let { auditPwdList, isLocked, curDevUserList } = this
      if (event !== undefined) {
        const { status } = event.data
        let auditPwdObj = {}
        if (status !== 'success') {
          if (event.data === 'loadSuccess') {
            // 设备登录页加载成功自动登录
            // 获取对应ip在数组中的位置
            let resIndex = auditPwdList.findIndex(
              (item) => item.ip === event.origin.substring(8)
            )
            if (resIndex !== -1 && !event.data['type']) {
              this.handleAutoLogin(
                event.origin.substring(8),
                auditPwdList[resIndex]['UserNamePwd']
              )
            }
          } else if (event.data.type && event.data.state === 'fail') {
            // 处理登录失败情况
          } else {
            // 手工登录设备记录保存用户信息
            let isAdd = auditPwdList.some((item) => item.ip === event.origin.substring(8))
            if (!isLocked) {
              if (!isAdd) {
                auditPwdObj['ip'] = event.origin.substring(8)
                auditPwdObj['UserNamePwd'] = event.data
                auditPwdList.push(auditPwdObj)
              } else {
                // 获取对应ip在数组中的位置
                let resIndex = auditPwdList.findIndex(
                  (item) => item.ip === event.origin.substring(8)
                )
                auditPwdList[resIndex]['UserNamePwd'] = event.data
              }

              // 缓存审计密码列表状态
              localStorage.setItem('auditUserPwdList', JSON.stringify(auditPwdList))
              this.auditPwdList = auditPwdList
            }
          }
        } else {
          // 设备登录成功添加角色信息到用户信息
          let resIndex = auditPwdList.findIndex(
            (item) => item.ip === event.origin.substring(8)
          )
          let { accountId } = auditPwdList[resIndex]
          // 获取第二次请求成功登陆角色名
          if (auditPwdList[resIndex]) {
            auditPwdList[resIndex]['roleName'] = event.data.roleName
            this.auditPwdList = auditPwdList
            this.isManualErr = false
            this.isLocked = false
            this.getIframeData(event.data.roleName, event.origin.substring(8))
          }

          // 是否是第二次登录 是查找id不是为原id
          let account = curDevUserList.find(
            (item) => item.account_name === event.data.userName
          )

          if (account) {
            accountId = account.id
          }

          // 调用登录日志接口记录日志
          if (accountId) {
            this.loginSuccessSaveLog(accountId)
          }
        }
      }
    },

    // 自动登录成功发送请求保存日志
    loginSuccessSaveLog(account_id) {
      loginForLog({ account_id })
        .then((res) => {
          console.log('登录日志保存成功')
        })
        .catch((error) => {
          console.error('登录日志保存失败:', error)
        })
    },

    // 处理自动登录逻辑
    handleAutoLogin(deviceIp, tokenData) {
      const childFrameObj = document.querySelectorAll('iframe')
      childFrameObj.forEach((item, index) => {
        let src = item.getAttribute('src')
        let ip = src.substring(8)
        if (deviceIp === ip) {
          const tokenArr = tokenData.split(':')
          item.contentWindow.postMessage(
            {
              username: tokenArr[0],
              pwd: tokenArr[1],
            },
            `https://${deviceIp}`
          )
        }
      })
    },

    // 封装登录设备成功处理逻辑
    getIframeData(roleName, deviceIp) {
      const auditUserPwdList = localStorage.getItem('auditUserPwdList')
      if (auditUserPwdList) {
        const resIndex = JSON.parse(auditUserPwdList).findIndex(
          (item) => item.ip === deviceIp
        )
        let loginValue = CryptoJS.enc.Base64.stringify(
          CryptoJS.enc.Utf8.parse(JSON.parse(auditUserPwdList)[resIndex]['UserNamePwd'])
        )
        const { currentDeviceId } = this
        if (currentDeviceId !== 0) {
          addUser({
            auth: loginValue,
            device_id: currentDeviceId,
            account_role: auditRoleMap[roleName],
          })
            .then((res) => {
              if (res.code === 0) {
                console.log('添加用户成功')
              }
            })
            .catch((error) => {
              console.error('添加用户失败:', error)
            })
        }
      }
    },

    // 获取设备列表数据
    getDeviceList() {
      this.loading = true
      const { currentPageSize, currentPage, searchValue } = this
      getData({
        _limit: currentPageSize,
        _page: currentPage,
        queryParams: searchValue,
        type: 2,
      })
        .then((res) => {
          if (res.code === 0) {
            // 添加序号
            const auditData = res.data.items || []
            for (let i = 0; i < auditData.length; i++) {
              auditData[i] = {
                number: (currentPage - 1) * currentPageSize + i + 1,
                ...auditData[i],
              }
            }
            this.auditData = auditData
            this.auditTotal = res.data.total || 0
            this.selectedRowKeys = []
            this.loading = false
          } else {
            this.$message.error(res.message)
            this.loading = false
          }
        })
        .catch((error) => {
          console.error('获取设备列表失败:', error)
          this.loading = false
        })
    },

    // 获取用户列表数据
    getUserListData(selfcallback) {
      const { currentUserPage, currentUserPageSize, currentDeviceId } = this
      getUser({
        device_id: currentDeviceId,
        page: currentUserPage,
        per_page: currentUserPageSize,
      })
        .then((res) => {
          if (res.code === 0) {
            if (res.data.total) {
              this.userTotal = res.data.total
              this.curDevUserList = res.data.items
            }
          }
          if (selfcallback) selfcallback(res)
        })
        .catch((error) => {
          console.error('获取用户列表失败:', error)
        })
    },

    // 设备选择改变事件
    onSelectChange(selectedRows) {
      this.selectedRowKeys = selectedRows.map((item) => item.id)
      this.selectedDevList = selectedRows.map((item) => item.ip)
    },

    // 用户选择改变事件处理
    onUserSelectChange(selectedRows) {
      this.userSelectedRowKeys = selectedRows.map((item) => item.id)
    },

    // 设备列表分页大小改变事件
    onShowSizeChange(pageSize, current) {
      this.currentPage = current
      this.currentPageSize = pageSize
      this.getDeviceList()
    },

    // 用户列表分页大小改变事件
    onUserShowSizeChange(pageSize, current) {
      this.currentUserPage = current
      this.currentUserPageSize = pageSize
      this.getUserListData()
    },

    // 页面改变处理事件
    handlePageChange(pageNumber) {
      this.currentPage = pageNumber
      this.getDeviceList()
    },

    // 用户列表分页改变事件处理
    handleUserPageChange(pageNumber) {
      this.currentUserPage = pageNumber
      this.getUserListData()
    },

    // 按条件查询设备
    handleAdvancedSearch() {
      let searchValue = {}
      if (this.searchForm.fireName) searchValue.fireName = this.searchForm.fireName
      if (this.searchForm.ip) searchValue.originIp = this.searchForm.ip
      if (this.searchForm.onlinStatus) searchValue.onlinStatus = this.searchForm.onlinStatus
      this.searchValue = searchValue
      this.currentPage = 1
      this.getDeviceList()
    },

    // 重置搜索条件
    handleReset() {
      this.searchForm = {
        fireName: '',
        ip: '',
        onlinStatus: '',
      }
      this.searchValue = {}
      this.currentPage = 1
      this.getDeviceList()
    },

    // 点击添加按钮显示模态框逻辑
    handleAddClick() {
      this.$refs.addModal.onShow()
    },

    // 查看设备事件处理
    handleLook(record) {
      if (record.status === 1) {
        window.open(`https://${record.ip}/#/login?type=smp`)
      } else {
        this.$message.error('设备不在线，无法查看!')
      }
    },

    // Ping
    handlePing(record) {
      devicePing({ ip: record.ip })
        .then((res) => {
          if (res.code === 0) {
            if (res.data === 0) {
              this.$message.success('Ping成功')
            } else {
              this.$message.error('网络不通')
            }
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('Ping失败:', error)
        })
    },

    // 根据设备id获取设备用户列表
    getUserList(deviceId, deviceIp) {
      let { auditPwdList } = this
      getUser({
        device_id: deviceId,
        page: 1,
        per_page: 10,
      })
        .then((res) => {
          if (res.code === 0) {
            if (res.data.items && res.data.items.length > 0) {
              let { default_account_auth } = res.data
              if (default_account_auth && default_account_auth.auth) {
                default_account_auth.auth = CryptoJS.enc.Base64.parse(
                  default_account_auth.auth
                ).toString(CryptoJS.enc.Utf8)
              }
              // 获取对应ip在数组中的位置
              let resIndex = auditPwdList.findIndex((item) => item.ip === deviceIp)
              if (resIndex === -1) {
                let auditPwdObj = {}
                auditPwdObj['ip'] = deviceIp
                auditPwdObj['UserNamePwd'] = default_account_auth.auth
                auditPwdObj['accountId'] = default_account_auth.account_id
                auditPwdList.push(auditPwdObj)
              } else {
                if (default_account_auth) {
                  auditPwdList[resIndex]['UserNamePwd'] = default_account_auth.auth
                  auditPwdList[resIndex]['accountId'] = default_account_auth.account_id
                }
              }
              this.auditPwdList = auditPwdList
            }
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('获取用户列表失败:', error)
        })
    },

    // 取消按钮事件处理
    handleCancelClick() {
      this.visible = false
      this.remarkVisible = false
      this.userVisible = false
    },

    // 修改默认登录账号请求
    modifyDefLoginAccount(params) {
      modifyUser(params)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success('保存默认登录用户配置成功')
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('修改默认登录账号失败:', error)
        })
    },

    // 保存默认登录用户配置按钮事件处理
    handleOkUser() {
      const { currentDeviceId, userSelectedRowKeys } = this
      const accountId = userSelectedRowKeys[0]
      if (accountId) {
        this.modifyDefLoginAccount({
          deviceId: currentDeviceId,
          accountId,
        })
      }
      this.visible = false
      this.remarkVisible = false
      this.userVisible = false
    },

    // 配置模态框显示逻辑
    handleShowConfig(record) {
      this.currentData_id = record.id
      this.currentConfig = record
      this.$refs.configModal.onShow()
    },

    // 处理用户按钮点击事件
    handleUser(record) {
      this.userVisible = true
      this.currentDeviceId = record.id
      this.getUserListData((res) => {
        if (res.data && res.data.default_account_auth) {
          const { account_id } = res.data.default_account_auth
          this.userSelectedRowKeys = [account_id]
        }
      })
    },

    // 删除用户按钮点击事件处理
    handleDeleteUser(record) {
      let { auditPwdList, userSelectedRowKeys } = this
      deleteUser({ account_id: Number(record.id) })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            auditPwdList = auditPwdList.filter((item) => item.accountId !== record.id)
            localStorage.setItem('auditUserPwdList', JSON.stringify(auditPwdList))
            let accountId = userSelectedRowKeys ? userSelectedRowKeys[0] : null
            userSelectedRowKeys =
              accountId && accountId === record.id ? [] : userSelectedRowKeys
            this.auditPwdList = auditPwdList
            this.userSelectedRowKeys = userSelectedRowKeys
            this.getUserListData()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('删除用户失败:', error)
        })
    },

    // tab标签页切换事件
    onChange(tab) {
      this.activeKey = tab.name
    },

    // 标签页编辑事件
    onEdit(targetKey, action) {
      if (action === 'remove') {
        let { panes, auditPwdList } = this
        panes = panes.filter((item) => item.key !== targetKey)
        auditPwdList = auditPwdList.filter((item) => item.ip !== targetKey)
        this.handleAutoLoginOut(targetKey)
        localStorage.setItem('auditUserPwdList', JSON.stringify(auditPwdList))
        localStorage.setItem('auditpanes', JSON.stringify(panes))

        this.panes = panes
        this.auditPwdList = auditPwdList
        this.remove(targetKey)
      }
    },

    // 处理自动登出逻辑
    handleAutoLoginOut(deviceIp) {
      const childFrameObj = document.querySelectorAll('iframe')
      childFrameObj.forEach((item, index) => {
        let src = item.getAttribute('src')
        let ip = src.substring(8)
        if (deviceIp === ip) {
          item.contentWindow.postMessage('logout', '*')
        }
      })
    },

    // 移除标签页
    remove(targetKey) {
      let activeKey = this.activeKey
      let lastIndex
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1
        }
      })
      const panes = this.panes.filter((pane) => pane.key !== targetKey)
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key
        } else {
          activeKey = panes[0].key
        }
      }
      this.panes = panes
      this.activeKey = this.panes.length === 1 ? '0' : activeKey
    },

    // 设备配置提交事件处理
    handleConifgSave(form) {
      const { currentData_id, currentConfig } = this
      if (
        currentConfig.importance !== form.importance ||
        currentConfig.position !== form.position ||
        currentConfig.person_liable !== form.person_liable ||
        currentConfig.contact !== form.contact ||
        currentConfig.group_id !== form.group_id
      ) {
        const payload = {
          device_id: currentData_id,
          importance: parseInt(form.importance),
          group_id: form.group_id === undefined ? '' : form.group_id,
          position: form.position === undefined ? '' : form.position,
          person_liable: form.person_liable === undefined ? '' : form.person_liable,
          contact: form.contact === undefined ? '' : form.contact,
        }
        editEquipment(payload)
          .then((res) => {
            if (res.code === 0) {
              this.$message.success('编辑成功')
              this.remarkVisible = false
              this.getDeviceList()
            } else {
              this.$message.error(res.message)
            }
            this.$refs.configModal.onHide()
          })
          .catch((error) => {
            console.error('编辑设备失败:', error)
          })
      } else {
        this.$message.error('配置未更改！')
      }
    },

    // 批量删除设备
    handleDeleteClick(record) {
      let selectedRowKeys = []
      let selectedDevList = []
      if (record && record.id) {
        selectedRowKeys.push(record.id)
        selectedDevList.push(record.ip)
      } else {
        selectedRowKeys = this.selectedRowKeys
        selectedDevList = this.selectedDevList
      }
      if (selectedRowKeys.length) {
        this.$confirm(
          `确认删除这${selectedRowKeys.length}条数据吗？`,
          '删除',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            DeleteData({ device_ids: selectedRowKeys })
              .then((res) => {
                if (res.code === 0) {
                  this.getDeviceList()
                  this.$message.success('删除成功')
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((error) => {
                console.error('删除设备失败:', error)
              })
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('至少选中一条数据')
      }
    },

    // 处理添加设备确定事件
    handleAddDevice(form) {
      const payload = {
        ...form,
        category: 2,
      }
      addEquipment(payload)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success('新增成功')
            this.getDeviceList()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('添加设备失败:', error)
        })
    },

    // 获取拓扑数据
    getTopoDataFunc(callback) {
      getTopoData()
        .then((res) => {
          if (res.code === 0) {
            this.topoData = res.data
            if (callback) callback()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('获取拓扑数据失败:', error)
        })
    },

    // 生成设备节点
    generateNode(options) {
      return {
        id: this.guid(),
        type: 'node',
        size: '50',
        shape: 'koni-custom-node',
        color: '#69C0FF',
        labelOffsetY: 38,
        ...options,
      }
    },

    // 生成GUID
    guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },

    // 调用保存拓扑数据接口
    saveTopoData(topoData) {
      setTopoData({ topology_text: topoData })
        .then((res) => {
          if (res.code === 0) {
            this.getDeviceList()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((error) => {
          console.error('保存拓扑数据失败:', error)
        })
    },
  },
}
</script>

<style scoped lang="scss">
.router-wrap-table {
  padding: 8px 24px;
  .table-header {
    background-color: transparent;
  }
}
.el-button--blue {
  color: #409eff;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-online {
  color: #67c23a;
}

.status-offline {
  color: #f56c6c;
}

::v-deep .el-tabs__nav-wrap::after {
  // width: 0;
}

::v-deep textarea::-webkit-input-placeholder {
  font-family: Arial;
}
</style>
