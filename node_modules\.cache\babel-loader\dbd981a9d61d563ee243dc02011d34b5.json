{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\AuthManagement\\component\\DeviceComponent.vue", "mtime": 1750059801334}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}