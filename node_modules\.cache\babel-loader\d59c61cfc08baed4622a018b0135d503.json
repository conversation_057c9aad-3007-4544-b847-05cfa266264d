{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\TabsChange.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\SentineVue\\UpgradeManagement\\component\\TabsChange.vue", "mtime": 1749799266715}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}