<template>
  <el-dialog
    :visible.sync="visible"
    title="编辑配置"
    width="600px"
    :close-on-click-modal="false"
    @close="onHide"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="重要性" prop="importance">
        <el-select v-model="form.importance" placeholder="请选择重要性">
          <el-option label="高" :value="1" />
          <el-option label="中" :value="2" />
          <el-option label="低" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备分组" prop="group_id">
        <el-select v-model="form.group_id" placeholder="请选择设备分组">
          <el-option
            v-for="group in groupData"
            :key="group.id"
            :label="group.groupName"
            :value="group.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="位置" prop="position">
        <el-input v-model="form.position" placeholder="请输入位置" />
      </el-form-item>
      <el-form-item label="负责人" prop="person_liable">
        <el-input v-model="form.person_liable" placeholder="请输入负责人" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="form.contact" placeholder="请输入联系方式" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onHide">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EditConfigModal',
  props: {
    groupData: {
      type: Array,
      default: () => [],
    },
    currentConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        importance: undefined,
        group_id: undefined,
        position: '',
        person_liable: '',
        contact: '',
      },
      rules: {
        importance: [
          { required: true, message: '请选择重要性', trigger: 'change' },
        ],
      },
    }
  },
  watch: {
    currentConfig: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.form = {
            importance: newVal.importance,
            group_id: newVal.group_id,
            position: newVal.position || '',
            person_liable: newVal.person_liable || '',
            contact: newVal.contact || '',
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    onShow() {
      this.visible = true
      if (this.currentConfig && Object.keys(this.currentConfig).length > 0) {
        this.form = {
          importance: this.currentConfig.importance,
          group_id: this.currentConfig.group_id,
          position: this.currentConfig.position || '',
          person_liable: this.currentConfig.person_liable || '',
          contact: this.currentConfig.contact || '',
        }
      }
    },

    onHide() {
      this.visible = false
      this.loading = false
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('save', this.form)
          setTimeout(() => {
            this.loading = false
            this.onHide()
          }, 1000)
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
