{"remainingRequest": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AuthManage\\components\\AddAuthModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\asset\\FirewallVue\\AuthManage\\components\\AddAuthModal.vue", "mtime": 1750123945291}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1745219675171}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}