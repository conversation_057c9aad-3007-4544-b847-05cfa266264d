{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-61b21f26\"],{\"0545\":function(e,t,a){\"use strict\";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"log-config-container\"},[a(\"div\",{staticClass:\"log-config-content\"},[a(\"el-tabs\",{attrs:{type:\"card\"},on:{\"tab-click\":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:\"activeTab\"}},[a(\"el-tab-pane\",{attrs:{label:\"日志服务器\",name:\"server\"}},[\"server\"===e.activeTab?a(\"log-server\"):e._e()],1),a(\"el-tab-pane\",{attrs:{label:\"日志外发\",name:\"logout\"}},[\"logout\"===e.activeTab?a(\"log-logout\"):e._e()],1)],1)],1)])},n=[],o=(a(\"b0c0\"),a(\"f3f3\")),i=(a(\"96cf\"),a(\"c964\")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"log-server-container self-page self-position\"},[a(\"el-button\",{staticClass:\"action-button\",attrs:{type:\"primary\"},on:{click:function(t){return e.toEdit({server_type:\"syslog\"})}}},[e._v(\" 新增 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.toDel()}}},[e._v(\" 删除 \")]),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{data:e.data,height:e.tableHeight},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\"}}),a(\"el-table-column\",{attrs:{prop:\"name\",label:\"名称\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"server_type\",label:\"类型\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"servers\",label:\"集群地址\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"protocol\",label:\"协议\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"addr\",label:\"IP地址\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"port\",label:\"端口\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(\"syslog\"===t.row.server_type?t.row.port:\"-\")+\" \")]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"180\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{staticClass:\"operation-button text-link\",attrs:{type:\"text\"},on:{click:function(a){return e.toEdit(Object.assign({},t.row))}}},[e._v(\"编辑\")]),a(\"el-button\",{staticClass:\"text-link\",attrs:{type:\"text\"},on:{click:function(a){return e.toDel(t.row.id)}}},[e._v(\"删除\")])]}}])},[a(\"template\",{slot:\"header\"},[a(\"span\",[e._v(\" 操作 \"),a(\"el-popover\",{attrs:{placement:\"top\",width:\"200\",trigger:\"hover\"}},[a(\"div\",[e._v(\" 日志服务器被日志外发规则 \"),a(\"br\"),e._v(\" 引用，日志服务器无法删除 \")]),a(\"i\",{staticClass:\"el-icon-question\",staticStyle:{color:\"rgba(0, 0, 0, 0.3)\"},attrs:{slot:\"reference\"},slot:\"reference\"})])],1)])],2)],1),a(\"pagination\",{staticClass:\"self-pagination\",attrs:{total:e.total,current:e.page,\"page-size\":e.limit},on:{change:e.handleCurrentChange,showSizeChange:e.handleSizeChange}}),a(\"el-dialog\",{attrs:{title:e.activeItem.id?\"编辑\":\"新增\",visible:e.visible,width:\"560px\",\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.visible=t},closed:e.afterClose}},[a(\"div\",[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"80px\",\"label-position\":\"right\"}},[a(\"el-form-item\",{attrs:{label:\"类型\",prop:\"server_type\"}},[a(\"el-radio-group\",{attrs:{disabled:!!e.activeItem.id},model:{value:e.form.server_type,callback:function(t){e.$set(e.form,\"server_type\",t)},expression:\"form.server_type\"}},[a(\"el-radio\",{attrs:{label:\"syslog\"}},[e._v(\"syslog\")])],1)],1),a(\"el-form-item\",{attrs:{label:\"名称\",prop:\"name\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入名称\"},model:{value:e.form.name,callback:function(t){e.$set(e.form,\"name\",\"string\"===typeof t?t.trim():t)},expression:\"form.name\"}})],1),\"kafka\"===e.form.server_type?a(\"el-form-item\",{attrs:{label:\"集群地址\",prop:\"servers\"}},[a(\"el-input\",{attrs:{placeholder:\"多个集群地址间用逗号隔开\"},model:{value:e.form.servers,callback:function(t){e.$set(e.form,\"servers\",\"string\"===typeof t?t.trim():t)},expression:\"form.servers\"}})],1):e._e(),\"syslog\"===e.form.server_type?a(\"el-form-item\",{attrs:{label:\"IP地址\",prop:\"addr\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入IP地址\"},model:{value:e.form.addr,callback:function(t){e.$set(e.form,\"addr\",\"string\"===typeof t?t.trim():t)},expression:\"form.addr\"}})],1):e._e(),\"syslog\"===e.form.server_type?a(\"el-form-item\",{attrs:{label:\"协议\",prop:\"protocol\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择协议\"},model:{value:e.form.protocol,callback:function(t){e.$set(e.form,\"protocol\",t)},expression:\"form.protocol\"}},[a(\"el-option\",{attrs:{label:\"UDP\",value:\"UDP\"}}),a(\"el-option\",{attrs:{label:\"TCP\",value:\"TCP\"}})],1)],1):e._e(),\"syslog\"===e.form.server_type?a(\"el-form-item\",{attrs:{label:\"端口\",prop:\"port\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入端口\"},model:{value:e.form.port,callback:function(t){e.$set(e.form,\"port\",e._n(t))},expression:\"form.port\"}})],1):e._e()],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:e.handleCancel}},[e._v(\"取消\")]),a(\"el-button\",{attrs:{type:\"primary\",loading:e.confirmLoading,disabled:!e.valid||e.activeItem.id&&!e.changed},on:{click:e.handleOk}},[e._v(\" 确定 \")])],1)])],1)},c=[],l=(a(\"4de4\"),a(\"4160\"),a(\"d81d\"),a(\"b64b\"),a(\"159b\"),a(\"6544\")),d=a(\"ee97\");function u(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/find_all\",method:\"get\",params:e})}function f(){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/find_list\",method:\"get\"})}function p(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/create_one\",method:\"post\",data:e})}function g(e,t){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/update_one\",method:\"post\",data:Object(o[\"a\"])(Object(o[\"a\"])({},t),{},{id:e})})}function m(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/delete_one\",method:\"delete\",data:{id:e}})}function h(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_server_config/delete_bulk\",method:\"delete\",data:{ids:e}})}function v(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/find_all\",method:\"get\",params:e})}function b(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/create_one\",method:\"post\",data:e})}function _(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/update_one\",method:\"post\",data:e})}function y(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/delete_one\",method:\"delete\",data:{id:e}})}function k(e){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/delete_bulk\",method:\"delete\",data:{ids:e}})}function w(e,t){return Object(d[\"a\"])({url:\"/api/ieg/v1/log_strategy_config/update_status\",method:\"post\",data:{ids:e,status:t}})}var x={regs:{allIp:/^(((((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))-(((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)))|(((([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4})|(:((:[\\da-fA-F]{1,4}){1,6}|:))|([\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:))|(([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:))|(([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:))|(([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:))|(([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?)|(([\\da-fA-F]{1,4}:){6}:))-((([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4})|(:((:[\\da-fA-F]{1,4}){1,6}|:))|([\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:))|(([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:))|(([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:))|(([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:))|(([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?)|(([\\da-fA-F]{1,4}:){6}:)))|(((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/(\\d|[1-2]\\d|3[0-2]))?)|((([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(:((:[\\da-fA-F]{1,4}){1,6}|:)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|([\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:)(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)|(([\\da-fA-F]{1,4}:){6}:(\\/([1-9]?\\d|(1([0-1]\\d|2[0-8]))))?)))$/,singleIp:/^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$|^([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$|^:((:[\\da-fA-F]{1,4}){1,6}|:)$|^[\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:)$|^([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:)$|^([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:)$|^([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:)$|^([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?$|^([\\da-fA-F]{1,4}:){6}$/,port:/^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])$/,domain:/^([-a-zA-Z0-9@:%._]{2,256}(\\.[a-z]{2,6}))|^((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(\\/\\w+)*$/,fileName:/^[a-zA-Z0-9\\s._-\\u4e00-\\u9fa5]+$/,fileName1:/^[^<>\"|?*]+$/,catalog:/^(?:(?:[a-zA-Z]:)?[\\\\/](?:[^\\\\?/*|<>:\"]+[\\\\/])*)(?:(?:[^\\\\?/*|<>:\"]+?)(?:\\.[^.\\\\?/*|<>:\"]+)?)?$/},isValidIp:function(e){return this.regs.allIp.test(e)},isValidPort:function(e){return this.regs.port.test(e)},isValidDomain:function(e){return this.regs.domain.test(e)},isSingleIp:function(e){return this.regs.singleIp.test(e)},isFileName:function(e){return this.regs.fileName1.test(e)},isCatalog:function(e){return this.regs.catalog.test(e)}},A=a(\"3a86\"),F=function(e,t,a){\"any\"===t?a():t&&!x.isValidPort(t)?a(new Error(\"端口格式错误\")):a()},$=function(e,t,a){\"any\"===t?a():t&&!x.isValidIp(t)?a(new Error(\"IP格式错误\")):a()},O={name:\"LogServer\",components:{Pagination:l[\"a\"]},data:function(){return{data:[],visible:!1,valid:!1,confirmLoading:!1,tableHeight:0,form:{server_type:\"syslog\",addr:\"\",name:\"\",port:void 0,protocol:\"\",servers:\"\"},rules:{name:[{trigger:\"change\",required:!0,message:\"名称不能为空\"},{trigger:\"blur\",max:32,message:\"名称不能超过32个字符\"}],addr:[{trigger:\"change\",required:!0,message:\"IP地址不能为空\"},{validator:$,trigger:\"blur\"}],gateway:[{trigger:\"change\",required:!0,message:\"网关不能为空\"}],protocol:[{trigger:\"change\",required:!0,message:\"请选择协议\"}],servers:[{trigger:\"change\",required:!0,message:\"集群地址不能为空\"}],port:[{trigger:\"change\",required:!0,message:\"端口不能为空\"},{validator:F,trigger:\"blur\"}]},selectedRowKeys:[],selected:[],activeItem:{},page:1,limit:20,total:0,loading:!1}},computed:{changed:function(){var e=this,t=Object.keys(this.form).filter((function(t){return e.form[t]!==e.activeItem[t]&&(e.form[t]||e.activeItem[t])}));return!!t.length}},watch:{\"form.server_type\":{handler:function(){var e=this;this.$refs.form&&this.$nextTick((function(){e.$refs.form.validate((function(t){e.valid=!!t})),e.$refs.form.clearValidate()}))}},form:{deep:!0,handler:function(){var e=this;this.$refs.form&&(this.$refs.form.validate((function(t){e.valid=!!t})),this.$refs.form.clearValidate())}}},mounted:function(){var e=this;this.getData(),window.addEventListener(\"resize\",(function(){e.tableHeight=Object(A[\"a\"])(e.total)}))},beforeDestroy:function(){var e=this;window.removeEventListener(\"resize\",(function(){e.tableHeight=Object(A[\"a\"])(e.total)}))},methods:{afterClose:function(){this.$refs.form.clearValidate()},toEdit:function(e){Object.keys(e).forEach((function(t){\"-\"===e[t]&&(e[t]=\"\")})),this.activeItem=e,this.resetForm(e),this.visible=!0},getData:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a,r,n,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,u({page:e.page,limit:e.limit});case 4:a=t.sent,0===a.code&&(r=a.data,n=r.list,o=r.pagination,e.data=n.map((function(e){return Object.keys(e).forEach((function(t){\"string\"!==typeof e[t]||e[t]||(e[t]=\"-\")})),e})),e.total=o.total,e.$nextTick((function(){e.tableHeight=Object(A[\"a\"])(e.total)}))),t.next=12;break;case 8:t.prev=8,t.t0=t[\"catch\"](1),console.error(\"获取日志服务器列表失败:\",t.t0),e.$message.error(\"获取日志服务器列表失败\");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[1,8,12,15]])})))()},resetForm:function(e){var t=this;Object.assign(this.form,e);var a=Object.keys(this.form);a.forEach((function(a){t.form[a]=e[a]}))},toDel:function(e){var t=this;this.$confirm(e?\"确定删除该日志服务器吗？\":\"确定删除已选日志服务器吗？\",\"提示\",{confirmButtonText:\"删除\",cancelButtonText:\"取消\",type:\"warning\",center:!0}).then(Object(i[\"a\"])(regeneratorRuntime.mark((function a(){var r,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!e){a.next=8;break}return a.next=4,m(e);case 4:r=a.sent,0===r.code&&t.$message.success(r.msg||\"删除成功\"),a.next=12;break;case 8:return a.next=10,h(t.selectedRowKeys);case 10:n=a.sent,0===n.code&&(t.$message.success(n.msg||\"删除成功\"),t.selected=[],t.selectedRowKeys=[]);case 12:t.getData(),a.next=19;break;case 15:a.prev=15,a.t0=a[\"catch\"](0),console.error(\"删除失败:\",a.t0),t.$message.error(\"删除失败\");case 19:case\"end\":return a.stop()}}),a,null,[[0,15]])})))).catch((function(){}))},handleOk:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i[\"a\"])(regeneratorRuntime.mark((function t(a){var r,n,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=24;break}if(e.confirmLoading=!0,t.prev=2,r=e.activeItem.id,\"number\"!==typeof r){t.next=11;break}return t.next=7,g(r,e.form);case 7:n=t.sent,0===n.code&&(e.$message.success(n.msg||\"更新成功\"),e.visible=!1,e.getData()),t.next=15;break;case 11:return t.next=13,p(e.form);case 13:o=t.sent,0===o.code&&(e.$message.success(o.msg||\"新增成功\"),e.visible=!1,e.getData());case 15:t.next=21;break;case 17:t.prev=17,t.t0=t[\"catch\"](2),console.error(\"保存失败:\",t.t0),e.$message.error(\"保存失败\");case 21:return t.prev=21,e.confirmLoading=!1,t.finish(21);case 24:case\"end\":return t.stop()}}),t,null,[[2,17,21,24]])})));return function(e){return t.apply(this,arguments)}}())},handleCancel:function(){this.visible=!1},handleSelectionChange:function(e){this.selected=e,this.selectedRowKeys=e.map((function(e){return e.id}))},handleCurrentChange:function(e){this.page=e,this.getData()},handleSizeChange:function(e,t){this.page=1,this.limit=t,this.getData()}}},j=O,C=(a(\"eb82\"),a(\"2877\")),S=Object(C[\"a\"])(j,s,c,!1,null,\"5c48a9b0\",null),R=S.exports,I=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"logout-container self-page self-position\"},[a(\"el-button\",{staticClass:\"action-button\",attrs:{type:\"primary\"},on:{click:function(t){return e.toEdit({status:\"enabled\",log_type:\"client_log_audit,white_list_audit,peripheral_audit,file_protect_audit,registry_audit,network_audit,process_safe,terminal_host\"})}}},[e._v(\" 新增 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.toggleStatus(e.selectedRowKeys,\"enabled\")}}},[e._v(\" 启用 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.toggleStatus(e.selectedRowKeys,\"disabled\")}}},[e._v(\" 禁用 \")]),a(\"el-button\",{staticClass:\"action-button\",attrs:{disabled:!e.selectedRowKeys.length},on:{click:function(t){return e.toDel()}}},[e._v(\" 删除 \")]),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{data:e.data,height:e.tableHeight},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\"}}),a(\"el-table-column\",{attrs:{prop:\"name\",label:\"名称\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"status\",label:\"状态\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"enabled\"===t.row.status?a(\"span\",{staticStyle:{color:\"#00ab7a\"}},[e._v(\"已启用\")]):a(\"span\",{staticStyle:{color:\"#fa8c16\"}},[e._v(\"已禁用\")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"server_name\",label:\"日志服务器\",width:\"150\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{prop:\"log_type\",label:\"外发日志\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[e._v(\" \"+e._s(e.getLogTypeLabel(t.row.log_type))+\" \")]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",width:\"180\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{staticClass:\"operation-button text-link\",attrs:{type:\"text\"},on:{click:function(a){return e.toEdit(t.row)}}},[e._v(\"编辑\")]),a(\"el-button\",{staticClass:\"operation-button text-link\",attrs:{type:\"text\"},on:{click:function(a){return e.toggleStatus(t.row.id,\"enabled\"===t.row.status?\"disabled\":\"enabled\")}}},[e._v(\" \"+e._s(\"enabled\"===t.row.status?\"禁用\":\"启用\")+\" \")]),a(\"el-button\",{staticClass:\"text-link\",attrs:{type:\"text\"},on:{click:function(a){return e.toDel(t.row.id)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{staticClass:\"self-pagination\",attrs:{current:e.page,total:e.total,\"page-size\":e.limit},on:{change:e.handleCurrentChange,showSizeChange:e.handleSizeChange}}),a(\"el-dialog\",{attrs:{title:e.activeItem.id?\"编辑\":\"新增\",visible:e.visible,width:\"560px\",\"close-on-click-modal\":!1},on:{\"update:visible\":function(t){e.visible=t},closed:e.afterClose}},[a(\"div\",[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\",\"label-position\":\"right\"}},[a(\"el-form-item\",{attrs:{label:\"名称\",prop:\"name\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入名称\"},model:{value:e.form.name,callback:function(t){e.$set(e.form,\"name\",\"string\"===typeof t?t.trim():t)},expression:\"form.name\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},[a(\"el-radio\",{attrs:{label:\"enabled\"}},[e._v(\"启用\")]),a(\"el-radio\",{attrs:{label:\"disabled\"}},[e._v(\"禁用\")])],1)],1),a(\"el-form-item\",{attrs:{label:\"日志服务器\",prop:\"server_id\"}},[a(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{filterable:\"\",placeholder:\"请选择日志服务器\"},model:{value:e.form.server_id,callback:function(t){e.$set(e.form,\"server_id\",t)},expression:\"form.server_id\"}},e._l(e.serverList,(function(e,t){return a(\"el-option\",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1),a(\"el-form-item\",{attrs:{label:\"外发日志\",prop:\"log_type\"}},[a(\"el-checkbox-group\",{model:{value:e.form.log_type,callback:function(t){e.$set(e.form,\"log_type\",t)},expression:\"form.log_type\"}},[a(\"el-row\",{attrs:{gutter:16}},e._l(e.logType,(function(t,r){return a(\"el-col\",{key:r,attrs:{span:9}},[a(\"el-checkbox\",{attrs:{label:r}},[e._v(\" \"+e._s(t)+\" \")])],1)})),1)],1)],1),a(\"el-form-item\",{attrs:{label:\"执行时间\",prop:\"schedule\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入执行时间格式为(* * * * * *)\"},model:{value:e.form.schedule,callback:function(t){e.$set(e.form,\"schedule\",\"string\"===typeof t?t.trim():t)},expression:\"form.schedule\"}}),a(\"span\",{staticClass:\"schedule-tip\",staticStyle:{color:\"#f5222d\"}},[e._v(\" 注：格式为(* * * * * *) 分别为表示秒、分钟、小时、日期、月份和星期 \"),a(\"br\"),e._v(\" 例：0 0 * * * *：每小时的第0分钟和第0秒执行一次任务。 \")])],1)],1),a(\"div\",{staticClass:\"dialog-footer\"},[a(\"el-button\",{staticClass:\"cancel-button\",on:{click:e.handleCancel}},[e._v(\"取消\")]),a(\"el-button\",{attrs:{type:\"primary\",disabled:!e.valid||e.activeItem.id&&!e.changed,loading:e.confirmLoading},on:{click:e.handleOk}},[e._v(\" 确定 \")])],1)],1)])],1)},E=[],T=(a(\"99af\"),a(\"c740\"),a(\"fb6a\"),a(\"a434\"),a(\"d3b7\"),a(\"ac1f\"),a(\"25f0\"),a(\"1276\"),a(\"d0ff\")),L={client_log_audit:\"终端操作审计\",white_list_audit:\"白名单审计\",peripheral_audit:\"外设管控审计\",file_protect_audit:\"文件防护审计\",registry_audit:\"注册表防护审计\",network_audit:\"网络防护审计\",process_safe:\"进程防护\",host_health:\"主机健康\",terminal_host:\"终端信息\"},D={name:\"LogOut\",components:{Pagination:l[\"a\"]},data:function(){return{data:[],valid:!1,visible:!1,confirmLoading:!1,logType:L,fileList:[],form:{name:\"\",status:\"\",server_id:void 0,log_type:[],template_file_name:\"\",schedule:\"\"},serverList:[],rules:{name:[{trigger:\"change\",required:!0,message:\"名称不能为空\"},{trigger:\"blur\",max:32,message:\"名称不能超过32个字符\"}],server_id:[{trigger:\"change\",required:!0,message:\"请选择日志服务器\"}],log_type:[{trigger:\"change\",required:!0,message:\"请选择外发日志类型\"}],schedule:[{trigger:\"change\",required:!0,message:\"执行时间不能为空\"}]},selectedRowKeys:[],selected:[],tableHeight:0,activeItem:{},page:1,limit:20,total:0,loading:!1}},computed:{AuthToken:function(){return this.$store.state.hostguardianUser.userData.token},changed:function(){var e=this,t=Object.keys(this.form).filter((function(t){var a,r;return(null===(a=e.form[t])||void 0===a?void 0:a.toString())!==(null===(r=e.activeItem[t])||void 0===r?void 0:r.toString())&&(e.form[t]||e.activeItem[t])}));return!!t.length}},watch:{\"form.server_type\":{handler:function(){this.$refs.form&&this.$refs.form.clearValidate()}},form:{deep:!0,handler:function(){var e=this,t=Object.keys(this.rules).filter((function(t){var a=e.form[t],r=-1!==e.rules[t].findIndex((function(e){return e.required}));return r&&!a}));t.length?this.valid=!1:this.valid=!0,this.$refs.form&&this.$refs.form.clearValidate()}}},created:function(){this.getServerList()},mounted:function(){var e=this;this.getData(),window.addEventListener(\"resize\",(function(){e.tableHeight=Object(A[\"a\"])(e.total)}))},beforeDestroy:function(){var e=this;window.removeEventListener(\"resize\",(function(){e.tableHeight=Object(A[\"a\"])(e.total)}))},methods:{getServerList:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,f();case 3:a=t.sent,0===a.code&&(e.serverList=a.data.list||a.data),t.next=11;break;case 7:t.prev=7,t.t0=t[\"catch\"](0),console.error(\"获取服务器列表失败:\",t.t0),e.$message.error(\"获取服务器列表失败\");case 11:case\"end\":return t.stop()}}),t,null,[[0,7]])})))()},afterClose:function(){this.$refs.form.clearValidate()},toEdit:function(e){this.activeItem=e,this.resetForm(e),this.visible=!0},toggleStatus:function(e,t){var a=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function r(){var n;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,w(\"number\"===typeof e?[e]:e,t);case 3:n=r.sent,0===n.code&&(a.$message.success(n.msg||\"更新成功\"),a.getData()),r.next=11;break;case 7:r.prev=7,r.t0=r[\"catch\"](0),console.error(\"更新状态失败:\",r.t0),a.$message.error(\"更新状态失败\");case 11:case\"end\":return r.stop()}}),r,null,[[0,7]])})))()},handleRemove:function(){this.fileList=[],this.valid=!1},beforeUpload:function(e){return this.fileList=[].concat(Object(T[\"a\"])(this.fileList),[e]).slice(-1),!1},getLogTypeLabel:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:\"\",a=(null===t||void 0===t?void 0:t.split(\",\"))||[];return a.map((function(t){return e.logType[t]})).toString()},getData:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a,r,n,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,v({page:e.page,limit:e.limit});case 4:a=t.sent,0===a.code&&(r=a.data,n=r.list,o=r.pagination,e.data=n,e.total=o.total,e.$nextTick((function(){e.tableHeight=Object(A[\"a\"])(e.total)}))),t.next=12;break;case 8:t.prev=8,t.t0=t[\"catch\"](1),console.error(\"获取日志外发列表失败:\",t.t0),e.$message.error(\"获取日志外发列表失败\");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case\"end\":return t.stop()}}),t,null,[[1,8,12,15]])})))()},resetForm:function(e){var t=this,a=Object.keys(this.form);this.fileList.splice(0,this.fileList.length),a.forEach((function(a){\"log_type\"===a&&e.log_type?e.log_type?t.form[a]=e.log_type.split(\",\"):t.form[a]=[]:t.form[a]=e[a]}))},toDel:function(e){var t=this;this.$confirm(e?\"确定删除该日志外发规则吗？\":\"确定删除已选日志外发规则吗？\",\"提示\",{confirmButtonText:\"删除\",cancelButtonText:\"取消\",type:\"warning\",center:!0}).then(Object(i[\"a\"])(regeneratorRuntime.mark((function a(){var r,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,!e){a.next=8;break}return a.next=4,y(e);case 4:r=a.sent,0===r.code&&t.$message.success(r.msg||\"删除成功\"),a.next=12;break;case 8:return a.next=10,k(t.selectedRowKeys);case 10:n=a.sent,0===n.code&&(t.$message.success(n.msg||\"删除成功\"),t.selected=[],t.selectedRowKeys=[]);case 12:t.getData(),a.next=19;break;case 15:a.prev=15,a.t0=a[\"catch\"](0),console.error(\"删除失败:\",a.t0),t.$message.error(\"删除失败\");case 19:case\"end\":return a.stop()}}),a,null,[[0,15]])})))).catch((function(){}))},handleOk:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i[\"a\"])(regeneratorRuntime.mark((function t(a){var r,n,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=26;break}if(e.confirmLoading=!0,t.prev=2,r=Object(o[\"a\"])({},e.form),r.log_type=r.log_type?r.log_type.toString():\"\",n=e.activeItem.id,\"number\"!==typeof n){t.next=13;break}return t.next=9,_(Object(o[\"a\"])({id:n},r));case 9:i=t.sent,0===i.code&&(e.$message.success(i.msg||\"更新成功\"),e.visible=!1,e.getData()),t.next=17;break;case 13:return t.next=15,b(r);case 15:s=t.sent,0===s.code&&(e.$message.success(s.msg||\"新增成功\"),e.visible=!1,e.getData());case 17:t.next=23;break;case 19:t.prev=19,t.t0=t[\"catch\"](2),console.error(\"保存失败:\",t.t0),e.$message.error(\"保存失败\");case 23:return t.prev=23,e.confirmLoading=!1,t.finish(23);case 26:case\"end\":return t.stop()}}),t,null,[[2,19,23,26]])})));return function(e){return t.apply(this,arguments)}}())},handleCancel:function(){this.visible=!1},handleSelectionChange:function(e){this.selected=e,this.selectedRowKeys=e.map((function(e){return e.id}))},handleCurrentChange:function(e){this.page=e,this.getData()},handleSizeChange:function(e,t){this.page=1,this.limit=t,this.getData()}}},P=D,q=(a(\"f8ab\"),Object(C[\"a\"])(P,I,E,!1,null,\"33393560\",null)),V=q.exports,z=a(\"d2c9\"),K={name:\"LogConfiguration\",components:{LogServer:R,LogLogout:V},data:function(){return{activeTab:\"server\"}},mounted:function(){var e=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(z[\"a\"])();case 2:a=e.$route.query.tab,a&&(e.activeTab=a);case 4:case\"end\":return t.stop()}}),t)})))()},methods:{handleTabClick:function(e){this.$router.push({query:Object(o[\"a\"])(Object(o[\"a\"])({},this.$route.query),{},{tab:e.name})})}}},U=K,N=(a(\"ac85\"),a(\"1a72\")),H=a.n(N),B=Object(C[\"a\"])(U,r,n,!1,null,\"7c4ff366\",null);\"function\"===typeof H.a&&H()(B);t[\"default\"]=B.exports},\"1a72\":function(e,t){},\"2ca0\":function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"06cf\").f,o=a(\"50c4\"),i=a(\"5a34\"),s=a(\"1d80\"),c=a(\"ab13\"),l=a(\"c430\"),d=\"\".startsWith,u=Math.min,f=c(\"startsWith\"),p=!l&&!f&&!!function(){var e=n(String.prototype,\"startsWith\");return e&&!e.writable}();r({target:\"String\",proto:!0,forced:!p&&!f},{startsWith:function(e){var t=String(s(this));i(e);var a=o(u(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return d?d.call(t,r,a):t.slice(a,a+r.length)===r}})},\"3a86\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return o}));var r=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.offsetTop),t},n=function(e){var t=0,a=document.querySelector(e);return a&&(t=a.clientHeight),t},o=function(e){try{var t=n(\".audit-container\")||n(\".router-container\"),a=r(\".el-table\"),o=48,i=t-a-o-20;return e>0?Math.max(i,300):300}catch(s){return console.error(\"计算表格高度出错:\",s),500}}},\"47ff\":function(e,t,a){},\"5a34\":function(e,t,a){var r=a(\"44e7\");e.exports=function(e){if(r(e))throw TypeError(\"The method doesn't accept regular expressions\");return e}},\"6a53\":function(e,t,a){},\"8ff2\":function(e,t,a){},a434:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"23cb\"),o=a(\"a691\"),i=a(\"50c4\"),s=a(\"7b0b\"),c=a(\"65f0\"),l=a(\"8418\"),d=a(\"1dde\"),u=a(\"ae40\"),f=d(\"splice\"),p=u(\"splice\",{ACCESSORS:!0,0:0,1:2}),g=Math.max,m=Math.min,h=9007199254740991,v=\"Maximum allowed length exceeded\";r({target:\"Array\",proto:!0,forced:!f||!p},{splice:function(e,t){var a,r,d,u,f,p,b=s(this),_=i(b.length),y=n(e,_),k=arguments.length;if(0===k?a=r=0:1===k?(a=0,r=_-y):(a=k-2,r=m(g(o(t),0),_-y)),_+a-r>h)throw TypeError(v);for(d=c(b,r),u=0;u<r;u++)f=y+u,f in b&&l(d,u,b[f]);if(d.length=r,a<r){for(u=y;u<_-r;u++)f=u+r,p=u+a,f in b?b[p]=b[f]:delete b[p];for(u=_;u>_-r+a;u--)delete b[u-1]}else if(a>r)for(u=_-r;u>y;u--)f=u+r-1,p=u+a-1,f in b?b[p]=b[f]:delete b[p];for(u=0;u<a;u++)b[u+y]=arguments[u+2];return b.length=_-r+a,d}})},ab13:function(e,t,a){var r=a(\"b622\"),n=r(\"match\");e.exports=function(e){var t=/./;try{\"/./\"[e](t)}catch(a){try{return t[n]=!1,\"/./\"[e](t)}catch(r){}}return!1}},ac85:function(e,t,a){\"use strict\";var r=a(\"6a53\"),n=a.n(r);n.a},bae8:function(e,t,a){\"use strict\";a.d(t,\"b\",(function(){return n})),a.d(t,\"a\",(function(){return o}));var r=a(\"ee97\");function n(e){return Object(r[\"a\"])({url:\"/api/ieg/v1/login/login_in\",method:\"post\",data:e})}function o(){return Object(r[\"a\"])({url:\"/api/ieg/v1/system/info\",method:\"get\"})}},c740:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").findIndex,o=a(\"44d2\"),i=a(\"ae40\"),s=\"findIndex\",c=!0,l=i(s);s in[]&&Array(1)[s]((function(){c=!1})),r({target:\"Array\",proto:!0,forced:c||!l},{findIndex:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),o(s)},d2c9:function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return s}));a(\"d3b7\"),a(\"25f0\"),a(\"96cf\");var r=a(\"c964\"),n=a(\"bae8\"),o=6e5;function i(){var e=localStorage.getItem(\"hg_token_timestamp\");if(!e)return!1;var t=(new Date).getTime(),a=parseInt(e),r=t-a;return r<o}function s(){return c.apply(this,arguments)}function c(){return c=Object(r[\"a\"])(regeneratorRuntime.mark((function e(){var t,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=localStorage.getItem(\"hg_token\"),!t){e.next=7;break}if(a=i(),!a){e.next=6;break}return console.log(\"本地token有效，无需重新登录\"),e.abrupt(\"return\",t);case 6:console.log(\"本地token已过期，需要重新登录\");case 7:return e.prev=7,e.next=10,Object(n[\"b\"])({name:\"admin\",password:\"123456\"});case 10:if(r=e.sent,!(r&&0===r.code&&r.data&&r.data.token)){e.next=18;break}return console.log(\"隐式登录成功，获取到新token\"),localStorage.setItem(\"hg_token\",r.data.token),localStorage.setItem(\"hg_token_timestamp\",(new Date).getTime().toString()),e.abrupt(\"return\",r.data.token);case 18:return console.error(\"隐式登录失败:\",r),e.abrupt(\"return\",\"\");case 20:e.next=26;break;case 22:return e.prev=22,e.t0=e[\"catch\"](7),console.error(\"隐式登录出错:\",e.t0),e.abrupt(\"return\",\"\");case 26:case\"end\":return e.stop()}}),e,null,[[7,22]])}))),c.apply(this,arguments)}},d81d:function(e,t,a){\"use strict\";var r=a(\"23e7\"),n=a(\"b727\").map,o=a(\"1dde\"),i=a(\"ae40\"),s=o(\"map\"),c=i(\"map\");r({target:\"Array\",proto:!0,forced:!s||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},eb82:function(e,t,a){\"use strict\";var r=a(\"47ff\"),n=a.n(r);n.a},ee97:function(e,t,a){\"use strict\";a(\"99af\"),a(\"c975\"),a(\"a9e3\"),a(\"d3b7\"),a(\"ac1f\"),a(\"5319\"),a(\"2ca0\");var r=a(\"bc3a\"),n=a.n(r),o=a(\"4360\"),i=a(\"a18c\"),s=a(\"a47e\"),c=a(\"f7b5\"),l=a(\"f907\"),d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),d=r.NODE_ENV,u=r.VUE_APP_IS_MOCK,f=r.VUE_APP_BASE_API,p=\"true\"===u?\"\":f;\"production\"===d&&(p=\"\");var g={baseURL:p,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===d&&(g.timeout=a),t){case\"upload\":g.headers[\"Content-Type\"]=\"multipart/form-data\",g[\"processData\"]=!1,g[\"contentType\"]=!1;break;case\"download\":g[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var m=n.a.create(g);return m.interceptors.request.use((function(e){var t=o[\"a\"].getters.token;if(\"\"!==t&&e.url.startsWith(\"/api/ieg/\")){var a=localStorage.getItem(\"hg_token\");a&&(e.headers[\"authtoken\"]=a)}return e}),(function(e){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:e,print:!0}),Promise.reject(\"response-err:\"+e)})),m.interceptors.response.use((function(e){var a=void 0===e.headers[\"code\"]?200:Number(e.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,n=\"\";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n=\"error\"),e.data.code>=2e3&&e.data.code<3e3&&(n=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(a,\".\").concat(t),type:n}),Promise.reject(\"response-err-status:\".concat(r||l[\"a\"][a][t],\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.\".concat(a,\".\").concat(t))))};switch(e.data.code){case l[\"a\"].exception.system:t(\"system\");break;case l[\"a\"].exception.server:t(\"server\");break;case l[\"a\"].exception.session:r();break;case l[\"a\"].exception.access:r();break;case l[\"a\"].exception.certification:t(\"certification\");break;case l[\"a\"].exception.auth:t(\"auth\"),i[\"a\"].replace({path:\"/401\"});break;case l[\"a\"].exception.token:t(\"token\");break;case l[\"a\"].exception.param:t(\"param\");break;case l[\"a\"].exception.idempotency:t(\"idempotency\");break;case l[\"a\"].exception.ip:t(\"ip\"),o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"});break;case l[\"a\"].exception.upload:t(\"upload\");break;case l[\"a\"].attack.xss:t(\"xss\",\"attack\");break;default:t(\"code\",\"exception\",-1);break}};switch(t){case\"upload\":if(0===a)return e.data.data;n();break;case\"download\":if(0===a)return{data:e.data,fileName:decodeURI(e.headers[\"file-name\"])};n();break;default:if(0===e.data.code)return e.data;n();break}}),(function(e){var a=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===t?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(s[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),e.response&&403==e.response.status&&a(),Promise.reject(\"response-err-status:\".concat(e,\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.service.timeout\"))))})),m(e)};t[\"a\"]=d},f8ab:function(e,t,a){\"use strict\";var r=a(\"8ff2\"),n=a.n(r);n.a}}]);", "extractedComments": []}