{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-6ead155e\"],{\"2ca0\":function(t,e,n){\"use strict\";var r=n(\"23e7\"),a=n(\"06cf\").f,o=n(\"50c4\"),i=n(\"5a34\"),s=n(\"1d80\"),c=n(\"ab13\"),u=n(\"c430\"),l=\"\".startsWith,p=Math.min,f=c(\"startsWith\"),d=!u&&!f&&!!function(){var t=a(String.prototype,\"startsWith\");return t&&!t.writable}();r({target:\"String\",proto:!0,forced:!d&&!f},{startsWith:function(t){var e=String(s(this));i(t);var n=o(p(arguments.length>1?arguments[1]:void 0,e.length)),r=String(t);return l?l.call(e,r,n):e.slice(n,n+r.length)===r}})},\"511b\":function(t,e,n){\"use strict\";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",{staticClass:\"system-settings-container\"},[n(\"div\",{staticClass:\"system-settings-content\"},[n(\"el-tabs\",{attrs:{type:\"card\"},on:{\"tab-click\":t.handleTabClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:\"activeTab\"}},[n(\"el-tab-pane\",{attrs:{label:\"服务器升级\",name:\"upgrade\"}},[\"upgrade\"===t.activeTab?n(\"server-upgrade\"):t._e()],1),n(\"el-tab-pane\",{attrs:{label:\"系统授权\",name:\"authorization\"}},[\"authorization\"===t.activeTab?n(\"system-authorization\"):t._e()],1)],1)],1)])},a=[],o=(n(\"b0c0\"),n(\"f3f3\")),i=(n(\"96cf\"),n(\"c964\")),s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",{staticClass:\"auth-container\"},[n(\"el-card\",{staticClass:\"systembase auth-card\",attrs:{\"body-style\":{padding:\"20px\"}}},[n(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[n(\"span\",[t._v(\"证书信息\")]),n(\"el-button\",{staticStyle:{float:\"right\"},on:{click:t.importCertificate}},[t._v(\"导入\")])],1),n(\"el-descriptions\",{staticClass:\"separately-set-class-descriptions\",attrs:{title:\"\",column:1,border:\"\"}},[n(\"el-descriptions-item\",{attrs:{label:\"证书类型\"}},[t._v(\" \"+t._s(t.auth_info.auth_type)+\" \")]),t.auth_info.active_time?n(\"el-descriptions-item\",{attrs:{label:\"证书有效期\"}},[t._v(\" \"+t._s(t.auth_info.active_time.substring(0,10)+\" 至 \"+t.expires_time.substring(0,t.expires_time.indexOf(\"T\")))+\" \")]):t._e(),n(\"el-descriptions-item\",{attrs:{label:\"授权客户\"}},[t._v(\" \"+t._s(t.auth_info.auth_user)+\" \")]),n(\"el-descriptions-item\",{attrs:{label:\"授权设备识别号\"}},[n(\"span\",{attrs:{id:\"copyDownLoadAuthorization\"}},[t._v(t._s(t.auth_info.host_code))]),n(\"el-button\",{staticClass:\"copy-down-load-btn-authorization copy-button\",attrs:{size:\"small\",\"data-clipboard-target\":\"#copyDownLoadAuthorization\"}},[t._v(\" 复制 \")])],1)],1)],1),n(\"upload-modal\",{ref:\"importFiles\",attrs:{\"get-info\":t.getSystemBaseInfo,action:t.action,title:\"导入文件\"}})],1)},c=[],u=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n(\"div\",[n(\"el-dialog\",{staticClass:\"upload-modal\",attrs:{title:t.title,visible:t.visible,width:\"420px\",\"close-on-click-modal\":!1},on:{\"update:visible\":function(e){t.visible=e},close:t.close}},[n(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.status===t.ViewStatus.INIT,expression:\"status === ViewStatus.INIT\"}]},[n(\"el-upload\",{ref:\"upload\",staticClass:\"upload-dragger\",attrs:{drag:\"\",accept:t.accept,name:\"upload_file\",multiple:!1,\"before-upload\":\"/api/ieg/v1/system/upgrade_package\"==t.action?t.beforeUpload:t.empty,\"file-list\":t.fileList,action:t.action,headers:{AuthToken:t.token},\"on-change\":t.handleChange}},[n(\"div\",{staticClass:\"upload-modal__drag-icon\"},[n(\"i\",{staticClass:\"el-icon-upload\"})]),n(\"div\",{staticClass:\"upload-modal__drag-text\"},[t._v(\" 请从电脑中 \"),n(\"el-link\",{attrs:{type:\"primary\"}},[t._v(\"选择文件\")])],1)]),n(\"p\",{staticClass:\"upload-modal-tip\",style:{margin:\"16px 0 4px\"}},[t._t(\"tip\")],2)],1),n(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.status===t.ViewStatus.UPLOADING,expression:\"status === ViewStatus.UPLOADING\"}],staticClass:\"flex flex-col items-center\"},[n(\"i\",{staticClass:\"el-icon-loading\",staticStyle:{\"font-size\":\"60px\"}}),n(\"p\",[t._v(t._s(t.currentInfo))]),n(\"el-progress\",{attrs:{percentage:t.percent,format:function(t){return t.toFixed(2)+\"%\"},status:\"active\"}})],1),n(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.status===t.ViewStatus.SUCCESS,expression:\"status === ViewStatus.SUCCESS\"}],staticClass:\"flex flex-col items-center\"},[n(\"i\",{staticClass:\"el-icon-success\",staticStyle:{\"font-size\":\"60px\",color:\"#67C23A\"}}),n(\"p\",[t._v(\"导入成功\")]),n(\"p\",[t._v(t._s(t.successTip))])]),n(\"div\",{directives:[{name:\"show\",rawName:\"v-show\",value:t.status===t.ViewStatus.FAILED,expression:\"status === ViewStatus.FAILED\"}],staticClass:\"flex flex-col items-center\"},[n(\"i\",{staticClass:\"el-icon-error\",staticStyle:{\"font-size\":\"60px\",color:\"#F56C6C\"}}),t._t(\"failed-tip\"),n(\"p\",[t._v(t._s(t.failedTip))])],2)])],1)},l=[],p=(n(\"fb6a\"),{INIT:0,UPLOADING:1,SUCCESS:2,FAILED:3}),f={name:\"UploadModal\",props:{title:{type:String,default:\"上传\"},action:{type:String,required:!0},accept:{type:String,default:\"\"},getInfo:{type:Function,default:function(){}}},data:function(){return{token:this.$store.state.hostguardianUser.userData.token,ViewStatus:p,status:p.INIT,visible:!1,currentInfo:null,percent:0,successTip:\"\",failedTip:\"\",fileList:[]}},methods:{open:function(){this.status=p.INIT,this.visible=!0,this.fileList=[]},close:function(){this.visible=!1},beforeUpload:function(t){var e=t.size/1024/1024<300;return e||this.$message.error(\"文件最大不能超过300M\"),e},empty:function(){},handleChange:function(t,e){var n=this;this.fileList=e.slice(-1);this.fileList;if(this.currentInfo=t.name,\"uploading\"===t.status)return this.status=p.UPLOADING,void(this.percent=Math.round(t.percentage||0));if(\"success\"===t.status)\"/api/ieg/v1/system/upgrade_package\"==this.action?-1===t.response.code?(this.status=p.FAILED,this.failedTip=t.response.msg||\"\"):(this.percent=100,this.status=p.SUCCESS,this.successTip=t.response.msg,this.getInfo()):0===t.response.code?(this.percent=100,this.status=p.SUCCESS,this.successTip=t.response.msg,this.getInfo()):(this.status=p.FAILED,this.failedTip=t.response.msg||\"\");else if(\"error\"===t.status&&\"/api/ieg/v1/system/upgrade_package\"==this.action){this.percent=100,this.status=p.SUCCESS;var r=5,a=setInterval((function(){r--,n.successTip=\"页面即将在\".concat(r,\"秒后跳转\"),0==r&&(clearInterval(a),window.localStorage.removeItem(\"userData\"),window.localStorage.removeItem(\"routes\"),window.location.href=\"/login\")}),1e3);this.successTip=\"页面即将在5秒后跳转\"}else\"error\"===t.status&&(this.status=p.FAILED,this.failedTip=t.response&&t.response.msg||\"\")}}},d=f,h=(n(\"bba3\"),n(\"2877\")),m=Object(h[\"a\"])(d,u,l,!1,null,\"60414f34\",null),v=m.exports,b=n(\"ee97\");function g(){return Object(b[\"a\"])({url:\"/api/ieg/v1/system/base_info\",method:\"get\"})}var y=n(\"b311\"),_=n.n(y),x={name:\"SystemAuthorization\",components:{UploadModal:v},data:function(){return{auth_info:{},expires_time:\"\",action:\"\",copy:null}},mounted:function(){var t=this;this.getSystemBaseInfo(),this.copy=new _.a(\".copy-down-load-btn-authorization\"),this.copy.on(\"success\",(function(){t.$message.success(\"授权设备识别号复制成功\")}))},beforeDestroy:function(){this.copy&&this.copy.destroy()},methods:{getSystemBaseInfo:function(){var t=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,g();case 3:n=e.sent,0===n.code&&(t.auth_info=n.data.auth_info,t.expires_time=n.data.auth_func[1]?n.data.auth_func[1].expires_time:\"\"),e.next=11;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取系统授权信息失败:\",e.t0),t.$message.error(\"获取系统授权信息失败\");case 11:case\"end\":return e.stop()}}),e,null,[[0,7]])})))()},importCertificate:function(){this.action=\"/api/ieg/v1/service_auth/import_auth_file\",this.$refs.importFiles.open()}}},w=x,S=(n(\"ac03\"),Object(h[\"a\"])(w,s,c,!1,null,\"2ea28949\",null)),k=S.exports,C=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r(\"div\",{staticClass:\"upgrade-container\"},[r(\"el-card\",{staticClass:\"systembase upgrade-card\",attrs:{\"body-style\":{padding:\"20px\"}}},[r(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[r(\"span\",[t._v(\"系统信息\")]),r(\"el-button\",{staticStyle:{float:\"right\"},on:{click:t.offlineUpgrade}},[t._v(\"离线升级\")])],1),r(\"el-descriptions\",{attrs:{title:\"\",column:1,border:\"\"}},[r(\"el-descriptions-item\",{attrs:{label:\"产品信息\"}},[r(\"span\",{staticClass:\"systembase-span\"},[r(\"img\",{attrs:{src:n(\"5a18\"),width:\"200\"}})])]),r(\"el-descriptions-item\",{attrs:{label:\"产品LOGO\"}},[r(\"span\",{staticClass:\"systembase-span\"},[r(\"img\",{attrs:{src:n(\"fe9c\"),width:\"32\"}})])]),r(\"el-descriptions-item\",{attrs:{label:\"当前版本\"}},[t._v(\" \"+t._s(t.versionNumber)+\" \")]),r(\"el-descriptions-item\",{attrs:{label:\"授权序号\"}},[t._v(\" \"+t._s(t.auth_info.serial_no)+\" \")])],1)],1),r(\"upload-modal\",{ref:\"upload\",attrs:{accept:t.accept,action:t.action,title:\"离线升级\"}},[r(\"div\",{attrs:{slot:\"tip\"},slot:\"tip\"},[r(\"p\",[t._v(\"只支持.zip格式的文件，上传成功后系统将自动进行离线升级\")])]),r(\"span\",{staticClass:\"failed-tip\",staticStyle:{color:\"#333\"},attrs:{slot:\"failed-tip\"},slot:\"failed-tip\"},[t._v(\" 升级失败，请联系工程师 \")])])],1)},E=[],T=n(\"bae8\"),O={name:\"ServerUpgrade\",components:{UploadModal:v},data:function(){return{auth_info:{},action:\"\",accept:\"\",versionNumber:\"\"}},mounted:function(){this.getSystemBaseInfo(),this.getSystemInfo()},methods:{getSystemBaseInfo:function(){var t=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,g();case 3:n=e.sent,0===n.code&&(t.auth_info=n.data.auth_info),e.next=11;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取系统基本信息失败:\",e.t0),t.$message.error(\"获取系统基本信息失败\");case 11:case\"end\":return e.stop()}}),e,null,[[0,7]])})))()},offlineUpgrade:function(){this.action=\"/api/ieg/v1/system/upgrade_package\",this.accept=\"application/x-zip-compressed\",this.$refs.upload.open()},getSystemInfo:function(){var t=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(T[\"a\"])();case 3:n=e.sent,0===n.code&&(t.versionNumber=n.data.version),e.next=11;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取系统信息失败:\",e.t0),t.$message.error(\"获取系统信息失败\");case 11:case\"end\":return e.stop()}}),e,null,[[0,7]])})))()}}},I=O,j=(n(\"6998\"),Object(h[\"a\"])(I,C,E,!1,null,\"5e7ea3f4\",null)),A=j.exports,L=n(\"d2c9\"),P={name:\"SystemSettings\",components:{SystemAuthorization:k,ServerUpgrade:A},data:function(){return{activeTab:\"upgrade\"}},mounted:function(){var t=this;return Object(i[\"a\"])(regeneratorRuntime.mark((function e(){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(L[\"a\"])();case 2:n=t.$route.query.tab,n&&(t.activeTab=n);case 4:case\"end\":return e.stop()}}),e)})))()},methods:{handleTabClick:function(t){this.$router.push({query:Object(o[\"a\"])(Object(o[\"a\"])({},this.$route.query),{},{tab:t.name})})}}},N=P,U=(n(\"fcba\"),n(\"cfd6\")),R=n.n(U),D=Object(h[\"a\"])(N,r,a,!1,null,\"8138e768\",null);\"function\"===typeof R.a&&R()(D);e[\"default\"]=D.exports},\"5a18\":function(t,e,n){t.exports=n.p+\"static/img/menu-logo.a8f46cbf.png\"},\"5a34\":function(t,e,n){var r=n(\"44e7\");t.exports=function(t){if(r(t))throw TypeError(\"The method doesn't accept regular expressions\");return t}},\"5d52\":function(t,e,n){},\"609d\":function(t,e,n){},6998:function(t,e,n){\"use strict\";var r=n(\"609d\"),a=n.n(r);a.a},ab13:function(t,e,n){var r=n(\"b622\"),a=r(\"match\");t.exports=function(t){var e=/./;try{\"/./\"[t](e)}catch(n){try{return e[a]=!1,\"/./\"[t](e)}catch(r){}}return!1}},ac03:function(t,e,n){\"use strict\";var r=n(\"5d52\"),a=n.n(r);a.a},aef7:function(t,e,n){},b311:function(t,e,n){\n/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\n(function(e,n){t.exports=n()})(0,(function(){return function(){var t={686:function(t,e,n){\"use strict\";n.d(e,{default:function(){return L}});var r=n(279),a=n.n(r),o=n(370),i=n.n(o),s=n(817),c=n.n(s);function u(t){try{return document.execCommand(t)}catch(e){return!1}}var l=function(t){var e=c()(t);return u(\"cut\"),e},p=l;function f(t){var e=\"rtl\"===document.documentElement.getAttribute(\"dir\"),n=document.createElement(\"textarea\");n.style.fontSize=\"12pt\",n.style.border=\"0\",n.style.padding=\"0\",n.style.margin=\"0\",n.style.position=\"absolute\",n.style[e?\"right\":\"left\"]=\"-9999px\";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top=\"\".concat(r,\"px\"),n.setAttribute(\"readonly\",\"\"),n.value=t,n}var d=function(t,e){var n=f(t);e.container.appendChild(n);var r=c()(n);return u(\"copy\"),n.remove(),r},h=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n=\"\";return\"string\"===typeof t?n=d(t,e):t instanceof HTMLInputElement&&![\"text\",\"search\",\"url\",\"tel\",\"password\"].includes(null===t||void 0===t?void 0:t.type)?n=d(t.value,e):(n=c()(t),u(\"copy\")),n},m=h;function v(t){return v=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},v(t)}var b=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,n=void 0===e?\"copy\":e,r=t.container,a=t.target,o=t.text;if(\"copy\"!==n&&\"cut\"!==n)throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');if(void 0!==a){if(!a||\"object\"!==v(a)||1!==a.nodeType)throw new Error('Invalid \"target\" value, use a valid Element');if(\"copy\"===n&&a.hasAttribute(\"disabled\"))throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');if(\"cut\"===n&&(a.hasAttribute(\"readonly\")||a.hasAttribute(\"disabled\")))throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes')}return o?m(o,{container:r}):a?\"cut\"===n?p(a):m(a,{container:r}):void 0},g=b;function y(t){return y=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},y(t)}function _(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}function x(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function w(t,e,n){return e&&x(t.prototype,e),n&&x(t,n),t}function S(t,e){if(\"function\"!==typeof e&&null!==e)throw new TypeError(\"Super expression must either be null or a function\");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&k(t,e)}function k(t,e){return k=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},k(t,e)}function C(t){var e=O();return function(){var n,r=I(t);if(e){var a=I(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return E(this,n)}}function E(t,e){return!e||\"object\"!==y(e)&&\"function\"!==typeof e?T(t):e}function T(t){if(void 0===t)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return t}function O(){if(\"undefined\"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function I(t){return I=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},I(t)}function j(t,e){var n=\"data-clipboard-\".concat(t);if(e.hasAttribute(n))return e.getAttribute(n)}var A=function(t){S(n,t);var e=C(n);function n(t,r){var a;return _(this,n),a=e.call(this),a.resolveOptions(r),a.listenClick(t),a}return w(n,[{key:\"resolveOptions\",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action=\"function\"===typeof t.action?t.action:this.defaultAction,this.target=\"function\"===typeof t.target?t.target:this.defaultTarget,this.text=\"function\"===typeof t.text?t.text:this.defaultText,this.container=\"object\"===y(t.container)?t.container:document.body}},{key:\"listenClick\",value:function(t){var e=this;this.listener=i()(t,\"click\",(function(t){return e.onClick(t)}))}},{key:\"onClick\",value:function(t){var e=t.delegateTarget||t.currentTarget,n=this.action(e)||\"copy\",r=g({action:n,container:this.container,target:this.target(e),text:this.text(e)});this.emit(r?\"success\":\"error\",{action:n,text:r,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:\"defaultAction\",value:function(t){return j(\"action\",t)}},{key:\"defaultTarget\",value:function(t){var e=j(\"target\",t);if(e)return document.querySelector(e)}},{key:\"defaultText\",value:function(t){return j(\"text\",t)}},{key:\"destroy\",value:function(){this.listener.destroy()}}],[{key:\"copy\",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return m(t,e)}},{key:\"cut\",value:function(t){return p(t)}},{key:\"isSupported\",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[\"copy\",\"cut\"],e=\"string\"===typeof t?[t]:t,n=!!document.queryCommandSupported;return e.forEach((function(t){n=n&&!!document.queryCommandSupported(t)})),n}}]),n}(a()),L=A},828:function(t){var e=9;if(\"undefined\"!==typeof Element&&!Element.prototype.matches){var n=Element.prototype;n.matches=n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}function r(t,n){while(t&&t.nodeType!==e){if(\"function\"===typeof t.matches&&t.matches(n))return t;t=t.parentNode}}t.exports=r},438:function(t,e,n){var r=n(828);function a(t,e,n,r,a){var o=i.apply(this,arguments);return t.addEventListener(n,o,a),{destroy:function(){t.removeEventListener(n,o,a)}}}function o(t,e,n,r,o){return\"function\"===typeof t.addEventListener?a.apply(null,arguments):\"function\"===typeof n?a.bind(null,document).apply(null,arguments):(\"string\"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return a(t,e,n,r,o)})))}function i(t,e,n,a){return function(n){n.delegateTarget=r(n.target,e),n.delegateTarget&&a.call(t,n)}}t.exports=o},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var n=Object.prototype.toString.call(t);return void 0!==t&&(\"[object NodeList]\"===n||\"[object HTMLCollection]\"===n)&&\"length\"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return\"string\"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return\"[object Function]\"===e}},370:function(t,e,n){var r=n(879),a=n(438);function o(t,e,n){if(!t&&!e&&!n)throw new Error(\"Missing required arguments\");if(!r.string(e))throw new TypeError(\"Second argument must be a String\");if(!r.fn(n))throw new TypeError(\"Third argument must be a Function\");if(r.node(t))return i(t,e,n);if(r.nodeList(t))return s(t,e,n);if(r.string(t))return c(t,e,n);throw new TypeError(\"First argument must be a String, HTMLElement, HTMLCollection, or NodeList\")}function i(t,e,n){return t.addEventListener(e,n),{destroy:function(){t.removeEventListener(e,n)}}}function s(t,e,n){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,n)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,n)}))}}}function c(t,e,n){return a(document.body,t,e,n)}t.exports=o},817:function(t){function e(t){var e;if(\"SELECT\"===t.nodeName)t.focus(),e=t.value;else if(\"INPUT\"===t.nodeName||\"TEXTAREA\"===t.nodeName){var n=t.hasAttribute(\"readonly\");n||t.setAttribute(\"readonly\",\"\"),t.select(),t.setSelectionRange(0,t.value.length),n||t.removeAttribute(\"readonly\"),e=t.value}else{t.hasAttribute(\"contenteditable\")&&t.focus();var r=window.getSelection(),a=document.createRange();a.selectNodeContents(t),r.removeAllRanges(),r.addRange(a),e=r.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,n){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:n}),this},once:function(t,e,n){var r=this;function a(){r.off(t,a),e.apply(n,arguments)}return a._=e,this.on(t,a,n)},emit:function(t){var e=[].slice.call(arguments,1),n=((this.e||(this.e={}))[t]||[]).slice(),r=0,a=n.length;for(r;r<a;r++)n[r].fn.apply(n[r].ctx,e);return this},off:function(t,e){var n=this.e||(this.e={}),r=n[t],a=[];if(r&&e)for(var o=0,i=r.length;o<i;o++)r[o].fn!==e&&r[o].fn._!==e&&a.push(r[o]);return a.length?n[t]=a:delete n[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function n(r){if(e[r])return e[r].exports;var a=e[r]={exports:{}};return t[r](a,a.exports,n),a.exports}return function(){n.n=function(t){var e=t&&t.__esModule?function(){return t[\"default\"]}:function(){return t};return n.d(e,{a:e}),e}}(),function(){n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}}(),function(){n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),n(686)}().default}))},bae8:function(t,e,n){\"use strict\";n.d(e,\"b\",(function(){return a})),n.d(e,\"a\",(function(){return o}));var r=n(\"ee97\");function a(t){return Object(r[\"a\"])({url:\"/api/ieg/v1/login/login_in\",method:\"post\",data:t})}function o(){return Object(r[\"a\"])({url:\"/api/ieg/v1/system/info\",method:\"get\"})}},bba3:function(t,e,n){\"use strict\";var r=n(\"aef7\"),a=n.n(r);a.a},cfd6:function(t,e){},d2c9:function(t,e,n){\"use strict\";n.d(e,\"a\",(function(){return s}));n(\"d3b7\"),n(\"25f0\"),n(\"96cf\");var r=n(\"c964\"),a=n(\"bae8\"),o=6e5;function i(){var t=localStorage.getItem(\"hg_token_timestamp\");if(!t)return!1;var e=(new Date).getTime(),n=parseInt(t),r=e-n;return r<o}function s(){return c.apply(this,arguments)}function c(){return c=Object(r[\"a\"])(regeneratorRuntime.mark((function t(){var e,n,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=localStorage.getItem(\"hg_token\"),!e){t.next=7;break}if(n=i(),!n){t.next=6;break}return console.log(\"本地token有效，无需重新登录\"),t.abrupt(\"return\",e);case 6:console.log(\"本地token已过期，需要重新登录\");case 7:return t.prev=7,t.next=10,Object(a[\"b\"])({name:\"admin\",password:\"123456\"});case 10:if(r=t.sent,!(r&&0===r.code&&r.data&&r.data.token)){t.next=18;break}return console.log(\"隐式登录成功，获取到新token\"),localStorage.setItem(\"hg_token\",r.data.token),localStorage.setItem(\"hg_token_timestamp\",(new Date).getTime().toString()),t.abrupt(\"return\",r.data.token);case 18:return console.error(\"隐式登录失败:\",r),t.abrupt(\"return\",\"\");case 20:t.next=26;break;case 22:return t.prev=22,t.t0=t[\"catch\"](7),console.error(\"隐式登录出错:\",t.t0),t.abrupt(\"return\",\"\");case 26:case\"end\":return t.stop()}}),t,null,[[7,22]])}))),c.apply(this,arguments)}},ee97:function(t,e,n){\"use strict\";n(\"99af\"),n(\"c975\"),n(\"a9e3\"),n(\"d3b7\"),n(\"ac1f\"),n(\"5319\"),n(\"2ca0\");var r=n(\"bc3a\"),a=n.n(r),o=n(\"4360\"),i=n(\"a18c\"),s=n(\"a47e\"),c=n(\"f7b5\"),u=n(\"f907\"),l=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"default\",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"40000\",r=Object({NODE_ENV:\"production\",VUE_APP_BASE_API:\"/prod-api\",VUE_APP_IS_MOCK:\"false\",VUE_APP_PROXY_TARGET:\"\",BASE_URL:\"/\"}),l=r.NODE_ENV,p=r.VUE_APP_IS_MOCK,f=r.VUE_APP_BASE_API,d=\"true\"===p?\"\":f;\"production\"===l&&(d=\"\");var h={baseURL:d,withCredentials:!1,headers:{\"Content-Type\":\"application/json;charset=utf-8\"}};switch(\"production\"===l&&(h.timeout=n),e){case\"upload\":h.headers[\"Content-Type\"]=\"multipart/form-data\",h[\"processData\"]=!1,h[\"contentType\"]=!1;break;case\"download\":h[\"responseType\"]=\"blob\";break;case\"eventSource\":break;default:break}var m=a.a.create(h);return m.interceptors.request.use((function(t){var e=o[\"a\"].getters.token;if(\"\"!==e&&t.url.startsWith(\"/api/ieg/\")){var n=localStorage.getItem(\"hg_token\");n&&(t.headers[\"authtoken\"]=n)}return t}),(function(t){Object(c[\"a\"])({i18nCode:\"ajax.interceptors.error\",type:\"error\",error:t,print:!0}),Promise.reject(\"response-err:\"+t)})),m.interceptors.response.use((function(t){var n=void 0===t.headers[\"code\"]?200:Number(t.headers[\"code\"]),r=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))},a=function(){var e=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"exception\",r=arguments.length>2?arguments[2]:void 0,a=\"\";return(500===t.data.code||t.data.code>=1e3&&t.data.code<2e3)&&(a=\"error\"),t.data.code>=2e3&&t.data.code<3e3&&(a=\"warning\"),Object(c[\"a\"])({i18nCode:\"ajax.\".concat(n,\".\").concat(e),type:a}),Promise.reject(\"response-err-status:\".concat(r||u[\"a\"][n][e],\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.\".concat(n,\".\").concat(e))))};switch(t.data.code){case u[\"a\"].exception.system:e(\"system\");break;case u[\"a\"].exception.server:e(\"server\");break;case u[\"a\"].exception.session:r();break;case u[\"a\"].exception.access:r();break;case u[\"a\"].exception.certification:e(\"certification\");break;case u[\"a\"].exception.auth:e(\"auth\"),i[\"a\"].replace({path:\"/401\"});break;case u[\"a\"].exception.token:e(\"token\");break;case u[\"a\"].exception.param:e(\"param\");break;case u[\"a\"].exception.idempotency:e(\"idempotency\");break;case u[\"a\"].exception.ip:e(\"ip\"),o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"});break;case u[\"a\"].exception.upload:e(\"upload\");break;case u[\"a\"].attack.xss:e(\"xss\",\"attack\");break;default:e(\"code\",\"exception\",-1);break}};switch(e){case\"upload\":if(0===n)return t.data.data;a();break;case\"download\":if(0===n)return{data:t.data,fileName:decodeURI(t.headers[\"file-name\"])};a();break;default:if(0===t.data.code)return t.data;a();break}}),(function(t){var n=function(){Object(c[\"a\"])({i18nCode:\"logout.message\",type:\"error\"},(function(){i[\"a\"].currentRoute.path.indexOf(\"/login\")>-1?location.reload():(o[\"a\"].dispatch(\"user/reset\"),i[\"a\"].replace({path:\"/login\"}))}))};return\"upload\"===e?(Object(c[\"a\"])({i18nCode:\"ajax.service.upload\",type:\"error\",duration:2e3}),t.response&&403==t.response.status&&n(),Promise.reject(\"response-err-status:Upload Error \\nerr-question: \".concat(s[\"a\"].t(\"ajax.service.upload\")))):(Object(c[\"a\"])({i18nCode:\"ajax.service.timeout\",type:\"error\"}),t.response&&403==t.response.status&&n(),Promise.reject(\"response-err-status:\".concat(t,\" \\nerr-question: \").concat(s[\"a\"].t(\"ajax.service.timeout\"))))})),m(t)};e[\"a\"]=l},efeb5:function(t,e,n){},fcba:function(t,e,n){\"use strict\";var r=n(\"efeb5\"),a=n.n(r);a.a},fe9c:function(t,e,n){t.exports=n.p+\"static/img/logo-s.fb29dcde.png\"}}]);", "extractedComments": []}