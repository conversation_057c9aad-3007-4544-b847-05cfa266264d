<template>
  <el-drawer
    :visible.sync="visible"
    title="查看详情"
    size="50%"
    direction="rtl"
    @close="onClose"
  >
    <div style="padding: 20px;">
      <el-descriptions title="策略记录详情" :column="2" border>
        <el-descriptions-item label="协议">
          {{ record.name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="时间">
          {{ record.addTime === '-' ? record.addTime : formatTime(record.addTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="record.status == '0' ? 'danger' : 'success'">
            {{ record.status == '0' ? '下发失败' : '下发成功' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">
          {{ record.operateType == 0 ? '采集' : '过滤' }}
        </el-descriptions-item>
        <el-descriptions-item label="操作数量">
          {{ record.counts || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ record.description || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script>
import moment from 'moment'

export default {
  name: 'ViewComponent',
  data() {
    return {
      visible: false,
      record: {},
    }
  },
  methods: {
    showDrawer(record = {}) {
      this.record = record
      this.visible = true
    },

    onClose() {
      this.visible = false
      this.record = {}
    },

    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
  },
}
</script>

<style scoped lang="scss">
.el-descriptions {
  margin-top: 20px;
}
</style>
