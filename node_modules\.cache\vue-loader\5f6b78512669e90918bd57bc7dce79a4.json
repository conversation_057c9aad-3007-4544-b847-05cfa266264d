{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\index.vue?vue&type=template&id=3758630a&scoped=true&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\index.vue", "mtime": 1750323702934}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745219693993}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}