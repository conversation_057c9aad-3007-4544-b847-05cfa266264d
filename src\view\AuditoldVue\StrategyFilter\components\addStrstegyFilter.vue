<template>
  <el-drawer
    :visible.sync="visible"
    :title="isEdit ? '编辑过滤策略' : '新建过滤策略'"
    size="50%"
    direction="rtl"
    @close="onClose"
  >
    <div style="padding: 20px;">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="过滤地址1" prop="firstIp">
          <el-input v-model="form.firstIp" placeholder="请输入过滤地址1" />
        </el-form-item>
        <el-form-item label="过滤地址2" prop="secondIp">
          <el-input v-model="form.secondIp" placeholder="请输入过滤地址2" />
        </el-form-item>
        <el-form-item label="过滤协议" prop="protocolName">
          <el-input v-model="form.protocolName" placeholder="请输入过滤协议" />
        </el-form-item>
      </el-form>
      <div style="text-align: right; margin-top: 20px;">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'AddStrategyFilter',
  data() {
    return {
      visible: false,
      loading: false,
      isEdit: false,
      form: {
        firstIp: '',
        secondIp: '',
        protocolName: '',
      },
      rules: {
        firstIp: [
          { required: true, message: '请输入过滤地址1', trigger: 'blur' },
        ],
        protocolName: [
          { required: true, message: '请输入过滤协议', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    showDrawer(record = {}) {
      this.isEdit = !!(record && record.id)
      if (this.isEdit) {
        this.form = {
          firstIp: record.firstIp || '',
          secondIp: record.secondIp || '',
          protocolName: record.protocolName || '',
        }
      } else {
        this.form = {
          firstIp: '',
          secondIp: '',
          protocolName: '',
        }
      }
      this.visible = true
    },

    onClose() {
      this.visible = false
      this.loading = false
      this.isEdit = false
      this.form = {
        firstIp: '',
        secondIp: '',
        protocolName: '',
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          setTimeout(() => {
            this.$emit('getSourceData')
            this.loading = false
            this.onClose()
          }, 1000)
        }
      })
    },
  },
}
</script>
